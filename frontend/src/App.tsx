import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Layout from './components/Layout/Layout';
import Dashboard from './pages/Dashboard';
import Leads from './pages/Leads';
import Emails from './pages/Emails';
import Sent from './pages/Sent';
import Campaign from './pages/Campaign';
import Tracking from './pages/Tracking';
import Followups from './pages/Followups';
import Campaigns from './pages/Campaigns';
import Statistics from './pages/Statistics';
import Settings from './pages/Settings';



function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<Layout />}>
          <Route index element={<Dashboard />} />
          <Route path="leads" element={<Leads />} />
          <Route path="emails" element={<Emails />} />
          <Route path="sent" element={<Sent />} />
          <Route path="tracking" element={<Tracking />} />
          <Route path="followups" element={<Followups />} />
          <Route path="campaigns" element={<Campaigns />} />
          <Route path="campaign" element={<Campaign />} />
          <Route path="statistics" element={<Statistics />} />
          <Route path="settings" element={<Settings />} />
        </Route>
      </Routes>
    </Router>
  );
}

export default App;
