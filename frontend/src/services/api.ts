import axios from 'axios';
import type {
  ApiResponse,
  Company,
  EmailTemplate,
  SentEmail,
  Campaign,
  Statistics,
  DashboardOverview,
  LeadGenerationRequest,
  EmailGenerationRequest,
  EmailSendingRequest,
} from '../types/api';

// Configuration de l'API
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 300000, // 5 minutes pour les opérations longues
  headers: {
    'Content-Type': 'application/json',
  },
});

// Intercepteur pour gérer les erreurs
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('❌ API Error Details:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      url: error.config?.url,
      method: error.config?.method,
      timeout: error.code === 'ECONNABORTED' ? 'TIMEOUT' : 'NO_TIMEOUT'
    });
    return Promise.reject(error);
  }
);

export class ApiService {
  // Health check
  static async healthCheck(): Promise<ApiResponse<any>> {
    const response = await api.get('/health');
    return response.data;
  }

  // Leads
  static async generateLeads(request: LeadGenerationRequest): Promise<ApiResponse<{ leads: Company[]; total_generated: number }>> {
    const response = await api.post('/api/v1/leads/generate', request);
    return response.data;
  }

  static async getLeads(limit = 100, offset = 0): Promise<ApiResponse<Company[]>> {
    const response = await api.get(`/api/v1/leads?limit=${limit}&offset=${offset}`);
    return response.data;
  }

  // Emails
  static async generateEmails(request: EmailGenerationRequest): Promise<ApiResponse<{ templates: EmailTemplate[]; total_generated: number }>> {
    const response = await api.post('/api/v1/emails/generate', request);
    return response.data;
  }

  static async sendEmails(request: EmailSendingRequest): Promise<ApiResponse<{ sent_emails: SentEmail[]; total_sent: number }>> {
    const response = await api.post('/api/v1/emails/send', request);
    return response.data;
  }

  static async getSentEmails(limit = 100, offset = 0): Promise<ApiResponse<SentEmail[]>> {
    const response = await api.get(`/api/v1/emails/sent?limit=${limit}&offset=${offset}`);
    return response.data;
  }

  // Campaigns
  static async getCampaigns(): Promise<ApiResponse<Campaign[]>> {
    const response = await api.get('/api/v1/campaigns');
    return response.data;
  }

  static async createCampaign(campaign: Partial<Campaign>): Promise<ApiResponse<Campaign>> {
    const response = await api.post('/api/v1/campaigns', campaign);
    return response.data;
  }

  static async getCampaign(id: string): Promise<ApiResponse<Campaign>> {
    const response = await api.get(`/api/v1/campaigns/${id}`);
    return response.data;
  }

  static async updateCampaign(id: string, campaign: Partial<Campaign>): Promise<ApiResponse<Campaign>> {
    const response = await api.put(`/api/v1/campaigns/${id}`, campaign);
    return response.data;
  }

  static async deleteCampaign(id: string): Promise<ApiResponse<null>> {
    const response = await api.delete(`/api/v1/campaigns/${id}`);
    return response.data;
  }

  // Statistics
  static async getStatistics(daysBack = 30): Promise<ApiResponse<Statistics>> {
    const response = await api.get(`/api/v1/statistics?days_back=${daysBack}`);
    return response.data;
  }

  // Metrics - Vraies métriques de tracking
  static async getRealtimeMetrics(): Promise<ApiResponse<any>> {
    try {
      const response = await api.get('/api/v1/metrics/realtime');
      return response.data;
    } catch (error) {
      console.warn('Erreur métriques temps réel:', error);
      return { success: false, message: 'Erreur métriques', data: null };
    }
  }

  static async getTokenUsage(): Promise<ApiResponse<any>> {
    try {
      const response = await api.get('/api/v1/metrics/tokens');
      return response.data;
    } catch (error) {
      console.warn('Erreur utilisation tokens:', error);
      return { success: false, message: 'Erreur tokens', data: null };
    }
  }

  // Tracking des emails (pour usage futur)
  static async trackEmailOpen(trackingId: string): Promise<ApiResponse<any>> {
    const response = await api.post(`/api/v1/metrics/track/open/${trackingId}`);
    return response.data;
  }

  static async trackEmailClick(trackingId: string, url: string): Promise<ApiResponse<any>> {
    const response = await api.post(`/api/v1/metrics/track/click/${trackingId}?url=${encodeURIComponent(url)}`);
    return response.data;
  }

  // Dashboard
  static async getDashboardOverview(daysBack = 30): Promise<ApiResponse<DashboardOverview>> {
    const response = await api.get(`/api/v1/dashboard/overview?days_back=${daysBack}`);
    return response.data;
  }

  // Tracking
  static async getTrackingStats(daysBack = 30): Promise<ApiResponse<any>> {
    const response = await api.get(`/api/v1/tracking/stats?days_back=${daysBack}`);
    return response.data;
  }

  static async checkReplies(hoursBack = 24): Promise<ApiResponse<any>> {
    const response = await api.get(`/api/v1/tracking/replies?hours_back=${hoursBack}`);
    return response.data;
  }

  // Follow-ups
  static async getFollowupStats(daysBack = 30): Promise<ApiResponse<any>> {
    const response = await api.get(`/api/v1/followups/stats?days_back=${daysBack}`);
    return response.data;
  }

  static async processFollowups(dryRun = true): Promise<ApiResponse<any>> {
    const response = await api.post('/api/v1/followups/process', { dry_run: dryRun });
    return response.data;
  }
}

// Export de l'instance axios pour les appels directs
export { api };

export default ApiService;
