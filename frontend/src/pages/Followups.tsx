import React, { useState, useEffect } from 'react';
import { RefreshCw, Clock, Send, CheckCircle, AlertCircle, Calendar, Filter, Play, Pause } from 'lucide-react';
import Card from '../components/UI/Card';
import Button from '../components/UI/Button';
import Badge from '../components/UI/Badge';
import ApiService from '../services/api';

interface FollowupStats {
  emails_needing_followup: number;
  followups_sent: number;
  followup_response_rate: number;
  pending_followups: number;
  scheduled_followups: number;
  successful_followups: number;
}

interface FollowupEmail {
  id: number;
  original_email_id: number;
  company_name: string;
  email_to: string;
  subject: string;
  scheduled_date: string;
  status: 'pending' | 'scheduled' | 'sent' | 'failed';
  followup_number: number;
  days_since_original: number;
}

export default function Followups() {
  const [stats, setStats] = useState<FollowupStats | null>(null);
  const [followups, setFollowups] = useState<FollowupEmail[]>([]);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [autoMode, setAutoMode] = useState(false);
  const [filters, setFilters] = useState({
    status: 'all',
    timeRange: '7',
  });

  useEffect(() => {
    loadFollowupData();
  }, []);

  const loadFollowupData = async () => {
    try {
      setLoading(true);
      const response = await ApiService.getFollowupStats(30);
      
      if (response.success && response.data) {
        setStats({
          emails_needing_followup: response.data.emails_needing_followup || 0,
          followups_sent: response.data.followups_sent || 0,
          followup_response_rate: response.data.followup_response_rate || 0,
          pending_followups: response.data.pending_followups || 0,
          scheduled_followups: response.data.scheduled_followups || 0,
          successful_followups: response.data.successful_followups || 0,
        });
      }

      // Charger les vraies données de relances depuis l'API
      try {
        const followupResponse = await ApiService.getFollowupStats(30);
        if (followupResponse.success && followupResponse.data) {
          setFollowups(followupResponse.data.followups || []);
        } else {
          setFollowups([]);
        }
      } catch (error) {
        console.error('Erreur chargement relances:', error);
        setFollowups([]);
      }

    } catch (error) {
      console.error('Error loading followup data:', error);
    } finally {
      setLoading(false);
    }
  };

  const processFollowups = async (dryRun = false) => {
    try {
      setProcessing(true);
      const response = await ApiService.processFollowups(dryRun);
      if (response.success) {
        await loadFollowupData(); // Recharger les données
      }
    } catch (error) {
      console.error('Error processing followups:', error);
    } finally {
      setProcessing(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="warning">En attente</Badge>;
      case 'scheduled':
        return <Badge variant="info">Programmé</Badge>;
      case 'sent':
        return <Badge variant="success">Envoyé</Badge>;
      case 'failed':
        return <Badge variant="danger">Échec</Badge>;
      default:
        return <Badge variant="info">{status}</Badge>;
    }
  };

  const filteredFollowups = followups.filter(followup => {
    if (filters.status !== 'all' && followup.status !== filters.status) return false;
    return true;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Gestion des Relances</h1>
          <p className="text-gray-600 mt-2">
            Automatisez et suivez vos emails de relance pour maximiser les réponses
          </p>
        </div>
        <div className="flex space-x-3">
          <Button
            variant={autoMode ? "danger" : "secondary"}
            icon={autoMode ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
            onClick={() => setAutoMode(!autoMode)}
          >
            {autoMode ? 'Arrêter Auto' : 'Mode Auto'}
          </Button>
          <Button 
            variant="primary" 
            icon={<RefreshCw className="h-4 w-4" />}
            onClick={() => processFollowups(false)}
            loading={processing}
          >
            Traiter les Relances
          </Button>
        </div>
      </div>

      {/* Auto Mode Status */}
      {autoMode && (
        <Card className="border-green-200 bg-green-50">
          <div className="flex items-center">
            <CheckCircle className="h-5 w-5 text-green-600 mr-3" />
            <div>
              <p className="text-sm font-medium text-green-800">Mode automatique activé</p>
              <p className="text-xs text-green-600">Les relances seront envoyées automatiquement selon les règles définies</p>
            </div>
          </div>
        </Card>
      )}

      {/* Stats */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-yellow-500 text-white">
                <Clock className="h-6 w-6" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">En Attente</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.emails_needing_followup}</p>
                <p className="text-xs text-gray-500">Emails sans réponse</p>
              </div>
            </div>
          </Card>
          
          <Card>
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-blue-500 text-white">
                <Calendar className="h-6 w-6" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Programmées</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.scheduled_followups}</p>
                <p className="text-xs text-gray-500">Relances planifiées</p>
              </div>
            </div>
          </Card>
          
          <Card>
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-green-500 text-white">
                <Send className="h-6 w-6" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Envoyées</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.followups_sent}</p>
                <p className="text-xs text-gray-500">Ce mois-ci</p>
              </div>
            </div>
          </Card>
          
          <Card>
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-purple-500 text-white">
                <CheckCircle className="h-6 w-6" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Taux de Réponse</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.followup_response_rate.toFixed(1)}%</p>
                <p className="text-xs text-gray-500">Après relance</p>
              </div>
            </div>
          </Card>
        </div>
      )}

      {/* Configuration */}
      <Card title="Configuration des Relances">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <h4 className="font-medium text-gray-900 mb-3">Délais de Relance</h4>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">1ère relance</span>
                <Badge variant="info">7 jours</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">2ème relance</span>
                <Badge variant="info">14 jours</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">3ème relance</span>
                <Badge variant="info">21 jours</Badge>
              </div>
            </div>
          </div>
          
          <div>
            <h4 className="font-medium text-gray-900 mb-3">Conditions d'Arrêt</h4>
            <div className="space-y-2">
              <div className="flex items-center">
                <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                <span className="text-sm text-gray-600">Email ouvert</span>
              </div>
              <div className="flex items-center">
                <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                <span className="text-sm text-gray-600">Lien cliqué</span>
              </div>
              <div className="flex items-center">
                <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                <span className="text-sm text-gray-600">Réponse reçue</span>
              </div>
            </div>
          </div>
          
          <div>
            <h4 className="font-medium text-gray-900 mb-3">Statistiques</h4>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Taux d'ouverture</span>
                <span className="text-sm font-medium text-gray-900">+15%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Taux de réponse</span>
                <span className="text-sm font-medium text-gray-900">+8%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">ROI moyen</span>
                <span className="text-sm font-medium text-gray-900">+25%</span>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Filters */}
      <Card title="Filtres">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Statut</label>
            <select
              value={filters.status}
              onChange={(e) => setFilters({ ...filters, status: e.target.value })}
              className="input-field"
            >
              <option value="all">Tous les statuts</option>
              <option value="pending">En attente</option>
              <option value="scheduled">Programmées</option>
              <option value="sent">Envoyées</option>
              <option value="failed">Échecs</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Période</label>
            <select
              value={filters.timeRange}
              onChange={(e) => setFilters({ ...filters, timeRange: e.target.value })}
              className="input-field"
            >
              <option value="7">7 prochains jours</option>
              <option value="14">14 prochains jours</option>
              <option value="30">30 prochains jours</option>
            </select>
          </div>
          
          <div className="flex items-end">
            <Button 
              variant="secondary" 
              icon={<Filter className="h-4 w-4" />}
              className="w-full"
            >
              Appliquer les Filtres
            </Button>
          </div>
        </div>
      </Card>

      {/* Followups List */}
      <Card title="Liste des Relances" subtitle={`${filteredFollowups.length} relances trouvées`}>
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Destinataire
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Sujet
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Relance #
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Statut
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Programmée pour
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Délai
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredFollowups.map((followup) => (
                  <tr key={followup.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{followup.company_name}</div>
                        <div className="text-sm text-gray-500">{followup.email_to}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900 max-w-xs truncate">{followup.subject}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <Badge variant="info">#{followup.followup_number}</Badge>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(followup.status)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(followup.scheduled_date).toLocaleDateString('fr-FR')}
                      <div className="text-xs text-gray-400">
                        {new Date(followup.scheduled_date).toLocaleTimeString('fr-FR')}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {followup.days_since_original} jours
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </Card>
    </div>
  );
}
