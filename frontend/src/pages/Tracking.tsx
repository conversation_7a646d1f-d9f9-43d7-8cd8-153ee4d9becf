import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Pointer, <PERSON><PERSON>, TrendingUp, BarChart3, RefreshCw, Calendar, Globe } from 'lucide-react';
import Card from '../components/UI/Card';
import Button from '../components/UI/Button';
import Badge from '../components/UI/Badge';
import ApiService from '../services/api';

interface TrackingStats {
  total_events: number;
  open_rate: number;
  click_rate: number;
  reply_rate: number;
  unique_opens: number;
  unique_clicks: number;
  bounce_rate: number;
  engagement_score: number;
}

interface TrackingEvent {
  id: number;
  email_id: number;
  event_type: 'open' | 'click' | 'reply';
  timestamp: string;
  user_agent: string;
  ip_address: string;
  location: string;
  device: string;
}

export default function Tracking() {
  const [stats, setStats] = useState<TrackingStats | null>(null);
  const [events, setEvents] = useState<TrackingEvent[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [timeRange, setTimeRange] = useState('7');

  useEffect(() => {
    loadTrackingData();
  }, [timeRange]);

  const loadTrackingData = async () => {
    try {
      setLoading(true);
      const [statsResponse, repliesResponse] = await Promise.all([
        ApiService.getTrackingStats(parseInt(timeRange)),
        ApiService.checkReplies(24)
      ]);

      if (statsResponse.success && statsResponse.data) {
        setStats({
          total_events: statsResponse.data.total_events || 0,
          open_rate: statsResponse.data.open_rate || 0,
          click_rate: statsResponse.data.click_rate || 0,
          reply_rate: 0,
          unique_opens: statsResponse.data.unique_opens || 0,
          unique_clicks: statsResponse.data.unique_clicks || 0,
          bounce_rate: 0, // Pas encore implémenté
          engagement_score: Math.round((statsResponse.data.open_rate + statsResponse.data.click_rate) / 2) || 0,
        });
      }

      // Charger les vraies données de tracking depuis l'API
      try {
        const trackingResponse = await ApiService.getTrackingStats(30);
        if (trackingResponse.success && trackingResponse.data) {
          // Utiliser les vraies données de tracking
          setEvents(trackingResponse.data.events || []);
        } else {
          setEvents([]);
        }
      } catch (error) {
        console.error('Erreur chargement tracking:', error);
        setEvents([]);
      }

    } catch (error) {
      console.error('Error loading tracking data:', error);
    } finally {
      setLoading(false);
    }
  };

  const refreshData = async () => {
    setRefreshing(true);
    await loadTrackingData();
    setRefreshing(false);
  };

  const getEventIcon = (type: string) => {
    switch (type) {
      case 'open':
        return <Eye className="h-4 w-4 text-blue-500" />;
      case 'click':
        return <MousePointer className="h-4 w-4 text-purple-500" />;
      case 'reply':
        return <Reply className="h-4 w-4 text-green-500" />;
      default:
        return <BarChart3 className="h-4 w-4 text-gray-500" />;
    }
  };

  const getEventBadge = (type: string) => {
    switch (type) {
      case 'open':
        return <Badge variant="info">Ouverture</Badge>;
      case 'click':
        return <Badge variant="warning">Clic</Badge>;
      case 'reply':
        return <Badge variant="success">Réponse</Badge>;
      default:
        return <Badge variant="info">{type}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Tracking des Emails</h1>
          <p className="text-gray-600 mt-2">
            Analysez l'engagement et les interactions de vos campagnes
          </p>
        </div>
        <div className="flex space-x-3">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="input-field"
          >
            <option value="1">Aujourd'hui</option>
            <option value="7">7 derniers jours</option>
            <option value="30">30 derniers jours</option>
            <option value="90">90 derniers jours</option>
          </select>
          <Button 
            variant="secondary" 
            icon={<RefreshCw className="h-4 w-4" />}
            onClick={refreshData}
            loading={refreshing}
          >
            Actualiser
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-blue-500 text-white">
                <Eye className="h-6 w-6" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Taux d'Ouverture</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.open_rate.toFixed(1)}%</p>
                <p className="text-xs text-gray-500">{stats.unique_opens} ouvertures uniques</p>
              </div>
            </div>
          </Card>
          
          <Card>
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-purple-500 text-white">
                <MousePointer className="h-6 w-6" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Taux de Clic</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.click_rate.toFixed(1)}%</p>
                <p className="text-xs text-gray-500">{stats.unique_clicks} clics uniques</p>
              </div>
            </div>
          </Card>
          
          <Card>
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-green-500 text-white">
                <Reply className="h-6 w-6" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Taux de Réponse</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.reply_rate.toFixed(1)}%</p>
                <p className="text-xs text-gray-500">Réponses directes</p>
              </div>
            </div>
          </Card>
          
          <Card>
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-yellow-500 text-white">
                <TrendingUp className="h-6 w-6" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Score d'Engagement</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.engagement_score}/100</p>
                <p className="text-xs text-gray-500">Basé sur les interactions</p>
              </div>
            </div>
          </Card>
        </div>
      )}

      {/* Charts and Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card title="Évolution des Ouvertures" subtitle="Tendance sur la période sélectionnée">
          <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
            <div className="text-center">
              <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-600">Graphique des ouvertures</p>
              <p className="text-sm text-gray-500">Intégration avec une librairie de graphiques</p>
            </div>
          </div>
        </Card>

        <Card title="Répartition par Appareil" subtitle="Types d'appareils utilisés">
          <div className="h-32 flex items-center justify-center bg-gray-50 rounded-lg">
            <div className="text-center">
              <p className="text-gray-600">Données d'appareil non disponibles</p>
              <p className="text-sm text-gray-500">Nécessite l'analyse des User-Agent</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Recent Events */}
      <Card title="Événements Récents" subtitle={`${events.length} événements de tracking`}>
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Événement
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Email ID
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Localisation
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Appareil
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date/Heure
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {events.map((event) => (
                  <tr key={event.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {getEventIcon(event.event_type)}
                        <div className="ml-3">
                          {getEventBadge(event.event_type)}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      #{event.email_id}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center text-sm text-gray-900">
                        <Globe className="h-4 w-4 mr-1 text-gray-400" />
                        {event.location}
                      </div>
                      <div className="text-xs text-gray-500">{event.ip_address}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {event.device}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(event.timestamp).toLocaleDateString('fr-FR')}
                      <div className="text-xs text-gray-400">
                        {new Date(event.timestamp).toLocaleTimeString('fr-FR')}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </Card>
    </div>
  );
}
