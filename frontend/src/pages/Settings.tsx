import React, { useState } from 'react';
import { Settings as SettingsIcon, Save, RefreshCw, Key, Mail, Bot, Database, Bell, Shield } from 'lucide-react';
import Card from '../components/UI/Card';
import Button from '../components/UI/Button';
import Badge from '../components/UI/Badge';

export default function Settings() {
  const [activeTab, setActiveTab] = useState('general');
  const [saving, setSaving] = useState(false);
  const [settings, setSettings] = useState({
    // Paramètres généraux
    company_name: 'SL Conception',
    company_email: '<EMAIL>',
    company_phone: '06 23 31 58 39',
    company_website: 'www.slconception.fr',
    
    // Paramètres SMTP
    smtp_host: 'smtp.gmail.com',
    smtp_port: 587,
    smtp_username: '<EMAIL>',
    smtp_use_tls: true,
    
    // Paramètres OpenAI
    openai_model: 'gpt-4o',
    openai_temperature: 0.7,
    openai_max_tokens: 1000,
    
    // Paramètres de prospection
    max_emails_per_day: 100,
    followup_delay_days: 7,
    max_followups: 3,
    skip_good_websites: true,
    
    // Notifications
    email_notifications: true,
    success_notifications: true,
    error_notifications: true,
    daily_reports: true,
  });

  const tabs = [
    { id: 'general', name: 'Général', icon: SettingsIcon },
    { id: 'email', name: 'Email', icon: Mail },
    { id: 'ai', name: 'Intelligence Artificielle', icon: Bot },
    { id: 'prospection', name: 'Prospection', icon: Database },
    { id: 'notifications', name: 'Notifications', icon: Bell },
    { id: 'security', name: 'Sécurité', icon: Shield },
  ];

  const handleSave = async () => {
    setSaving(true);
    // Simuler la sauvegarde
    await new Promise(resolve => setTimeout(resolve, 1000));
    setSaving(false);
  };

  const testConnection = async (type: string) => {
    // Simuler le test de connexion
    await new Promise(resolve => setTimeout(resolve, 1000));
    alert(`Test de connexion ${type} réussi !`);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Paramètres</h1>
          <p className="text-gray-600 mt-2">
            Configurez votre système de prospection B2B
          </p>
        </div>
        <Button 
          variant="primary" 
          icon={<Save className="h-4 w-4" />}
          onClick={handleSave}
          loading={saving}
        >
          Sauvegarder
        </Button>
      </div>

      <div className="flex flex-col lg:flex-row gap-6">
        {/* Sidebar */}
        <div className="lg:w-64">
          <Card className="p-0">
            <nav className="space-y-1">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`w-full flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors ${
                    activeTab === tab.id
                      ? 'bg-primary-100 text-primary-700 border-r-2 border-primary-600'
                      : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                  }`}
                >
                  <tab.icon className="w-5 h-5 mr-3" />
                  {tab.name}
                </button>
              ))}
            </nav>
          </Card>
        </div>

        {/* Content */}
        <div className="flex-1">
          {activeTab === 'general' && (
            <Card title="Paramètres Généraux" subtitle="Informations de base de votre entreprise">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Nom de l'entreprise
                  </label>
                  <input
                    type="text"
                    value={settings.company_name}
                    onChange={(e) => setSettings({ ...settings, company_name: e.target.value })}
                    className="input-field"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email de contact
                  </label>
                  <input
                    type="email"
                    value={settings.company_email}
                    onChange={(e) => setSettings({ ...settings, company_email: e.target.value })}
                    className="input-field"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Téléphone
                  </label>
                  <input
                    type="tel"
                    value={settings.company_phone}
                    onChange={(e) => setSettings({ ...settings, company_phone: e.target.value })}
                    className="input-field"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Site web
                  </label>
                  <input
                    type="url"
                    value={settings.company_website}
                    onChange={(e) => setSettings({ ...settings, company_website: e.target.value })}
                    className="input-field"
                  />
                </div>
              </div>
            </Card>
          )}

          {activeTab === 'email' && (
            <div className="space-y-6">
              <Card title="Configuration SMTP" subtitle="Paramètres d'envoi d'emails">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Serveur SMTP
                    </label>
                    <input
                      type="text"
                      value={settings.smtp_host}
                      onChange={(e) => setSettings({ ...settings, smtp_host: e.target.value })}
                      className="input-field"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Port
                    </label>
                    <input
                      type="number"
                      value={settings.smtp_port}
                      onChange={(e) => setSettings({ ...settings, smtp_port: parseInt(e.target.value) })}
                      className="input-field"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Nom d'utilisateur
                    </label>
                    <input
                      type="email"
                      value={settings.smtp_username}
                      onChange={(e) => setSettings({ ...settings, smtp_username: e.target.value })}
                      className="input-field"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Mot de passe
                    </label>
                    <input
                      type="password"
                      placeholder="••••••••••••"
                      className="input-field"
                    />
                  </div>
                </div>
                
                <div className="mt-6">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={settings.smtp_use_tls}
                      onChange={(e) => setSettings({ ...settings, smtp_use_tls: e.target.checked })}
                      className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Utiliser TLS/SSL</span>
                  </label>
                </div>
                
                <div className="mt-6">
                  <Button
                    variant="secondary"
                    icon={<RefreshCw className="h-4 w-4" />}
                    onClick={() => testConnection('SMTP')}
                  >
                    Tester la Connexion
                  </Button>
                </div>
              </Card>
            </div>
          )}

          {activeTab === 'ai' && (
            <Card title="Configuration OpenAI" subtitle="Paramètres de génération d'emails avec IA">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Modèle
                  </label>
                  <select
                    value={settings.openai_model}
                    onChange={(e) => setSettings({ ...settings, openai_model: e.target.value })}
                    className="input-field"
                  >
                    <option value="gpt-4o">GPT-4 Optimized</option>
                    <option value="gpt-4">GPT-4</option>
                    <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Température ({settings.openai_temperature})
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.1"
                    value={settings.openai_temperature}
                    onChange={(e) => setSettings({ ...settings, openai_temperature: parseFloat(e.target.value) })}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>Conservateur</span>
                    <span>Créatif</span>
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Tokens maximum
                  </label>
                  <input
                    type="number"
                    value={settings.openai_max_tokens}
                    onChange={(e) => setSettings({ ...settings, openai_max_tokens: parseInt(e.target.value) })}
                    className="input-field"
                    min="100"
                    max="4000"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Clé API
                  </label>
                  <div className="flex">
                    <input
                      type="password"
                      placeholder="sk-••••••••••••••••••••••••••••••••••••••••••••••••"
                      className="input-field rounded-r-none"
                    />
                    <Button
                      variant="secondary"
                      icon={<Key className="h-4 w-4" />}
                      className="rounded-l-none border-l-0"
                    >
                      Tester
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          )}

          {activeTab === 'prospection' && (
            <Card title="Paramètres de Prospection" subtitle="Configuration des campagnes et limites">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Emails maximum par jour
                  </label>
                  <input
                    type="number"
                    value={settings.max_emails_per_day}
                    onChange={(e) => setSettings({ ...settings, max_emails_per_day: parseInt(e.target.value) })}
                    className="input-field"
                    min="1"
                    max="1000"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Délai de relance (jours)
                  </label>
                  <input
                    type="number"
                    value={settings.followup_delay_days}
                    onChange={(e) => setSettings({ ...settings, followup_delay_days: parseInt(e.target.value) })}
                    className="input-field"
                    min="1"
                    max="30"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Nombre maximum de relances
                  </label>
                  <input
                    type="number"
                    value={settings.max_followups}
                    onChange={(e) => setSettings({ ...settings, max_followups: parseInt(e.target.value) })}
                    className="input-field"
                    min="0"
                    max="10"
                  />
                </div>
              </div>
              
              <div className="mt-6">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={settings.skip_good_websites}
                    onChange={(e) => setSettings({ ...settings, skip_good_websites: e.target.checked })}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">
                    Ignorer les entreprises avec de bons sites web
                  </span>
                </label>
              </div>
            </Card>
          )}

          {activeTab === 'notifications' && (
            <Card title="Notifications" subtitle="Configurez vos préférences de notification">
              <div className="space-y-4">
                <label className="flex items-center justify-between">
                  <div>
                    <span className="text-sm font-medium text-gray-700">Notifications par email</span>
                    <p className="text-xs text-gray-500">Recevoir des notifications par email</p>
                  </div>
                  <input
                    type="checkbox"
                    checked={settings.email_notifications}
                    onChange={(e) => setSettings({ ...settings, email_notifications: e.target.checked })}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                </label>
                
                <label className="flex items-center justify-between">
                  <div>
                    <span className="text-sm font-medium text-gray-700">Notifications de succès</span>
                    <p className="text-xs text-gray-500">Être notifié des emails envoyés avec succès</p>
                  </div>
                  <input
                    type="checkbox"
                    checked={settings.success_notifications}
                    onChange={(e) => setSettings({ ...settings, success_notifications: e.target.checked })}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                </label>
                
                <label className="flex items-center justify-between">
                  <div>
                    <span className="text-sm font-medium text-gray-700">Notifications d'erreur</span>
                    <p className="text-xs text-gray-500">Être notifié en cas d'erreur</p>
                  </div>
                  <input
                    type="checkbox"
                    checked={settings.error_notifications}
                    onChange={(e) => setSettings({ ...settings, error_notifications: e.target.checked })}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                </label>
                
                <label className="flex items-center justify-between">
                  <div>
                    <span className="text-sm font-medium text-gray-700">Rapports quotidiens</span>
                    <p className="text-xs text-gray-500">Recevoir un résumé quotidien des activités</p>
                  </div>
                  <input
                    type="checkbox"
                    checked={settings.daily_reports}
                    onChange={(e) => setSettings({ ...settings, daily_reports: e.target.checked })}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                </label>
              </div>
            </Card>
          )}

          {activeTab === 'security' && (
            <Card title="Sécurité" subtitle="Paramètres de sécurité et confidentialité">
              <div className="space-y-6">
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-3">État de la Sécurité</h4>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Authentification 2FA</span>
                      <Badge variant="danger">Désactivée</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Chiffrement des données</span>
                      <Badge variant="success">Activé</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Connexions sécurisées</span>
                      <Badge variant="success">HTTPS/TLS</Badge>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-3">Actions de Sécurité</h4>
                  <div className="space-y-3">
                    <Button variant="secondary" className="w-full justify-start">
                      Changer le mot de passe
                    </Button>
                    <Button variant="secondary" className="w-full justify-start">
                      Configurer l'authentification 2FA
                    </Button>
                    <Button variant="secondary" className="w-full justify-start">
                      Télécharger les logs de sécurité
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
