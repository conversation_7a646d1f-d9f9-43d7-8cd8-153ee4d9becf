import React, { useState, useEffect } from 'react';
import { Users, Plus, Play, Pause, Edit, Trash2, Eye, BarChart3, Calendar, Target } from 'lucide-react';
import Card from '../components/UI/Card';
import Button from '../components/UI/Button';
import Badge from '../components/UI/Badge';
import ApiService from '../services/api';
import type { Campaign } from '../types/api';

export default function Campaigns() {
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedCampaign, setSelectedCampaign] = useState<Campaign | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [newCampaign, setNewCampaign] = useState({
    name: '',
    description: '',
  });

  useEffect(() => {
    loadCampaigns();
  }, []);

  const loadCampaigns = async () => {
    try {
      setLoading(true);
      const response = await ApiService.getCampaigns();
      if (response.success && response.data) {
        // Les données sont directement dans response.data (array)
        setCampaigns(Array.isArray(response.data) ? response.data : []);
      }
    } catch (error) {
      console.error('Error loading campaigns:', error);
      setCampaigns([]); // Assurer qu'on a toujours un array
    } finally {
      setLoading(false);
    }
  };

  const createCampaign = async () => {
    try {
      const response = await ApiService.createCampaign(newCampaign);
      if (response.success && response.data) {
        setCampaigns([...campaigns, response.data]);
        setShowCreateModal(false);
        setNewCampaign({ name: '', description: '' });
      }
    } catch (error) {
      console.error('Error creating campaign:', error);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'draft':
        return <Badge variant="info">Brouillon</Badge>;
      case 'running':
        return <Badge variant="success">En cours</Badge>;
      case 'completed':
        return <Badge variant="warning">Terminée</Badge>;
      case 'paused':
        return <Badge variant="danger">En pause</Badge>;
      default:
        return <Badge variant="info">{status}</Badge>;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'draft':
        return <Edit className="h-4 w-4 text-gray-500" />;
      case 'running':
        return <Play className="h-4 w-4 text-green-500" />;
      case 'completed':
        return <BarChart3 className="h-4 w-4 text-blue-500" />;
      case 'paused':
        return <Pause className="h-4 w-4 text-red-500" />;
      default:
        return <Target className="h-4 w-4 text-gray-500" />;
    }
  };

  const calculateStats = () => {
    return {
      total: campaigns.length,
      running: campaigns.filter(c => c.status === 'running').length,
      completed: campaigns.filter(c => c.status === 'completed').length,
      draft: campaigns.filter(c => c.status === 'draft').length,
      totalLeads: campaigns.reduce((acc, c) => acc + c.total_leads, 0),
      totalSent: campaigns.reduce((acc, c) => acc + c.emails_sent, 0),
      totalReplies: campaigns.reduce((acc, c) => acc + c.emails_replied, 0),
    };
  };

  const stats = calculateStats();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Gestion des Campagnes</h1>
          <p className="text-gray-600 mt-2">
            Créez, gérez et analysez vos campagnes de prospection
          </p>
        </div>
        <Button 
          variant="primary" 
          icon={<Plus className="h-4 w-4" />}
          onClick={() => setShowCreateModal(true)}
        >
          Nouvelle Campagne
        </Button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
        <Card>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
            <div className="text-sm text-gray-600">Total Campagnes</div>
          </div>
        </Card>
        
        <Card>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{stats.running}</div>
            <div className="text-sm text-gray-600">En Cours</div>
          </div>
        </Card>
        
        <Card>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{stats.totalLeads}</div>
            <div className="text-sm text-gray-600">Total Leads</div>
          </div>
        </Card>
        
        <Card>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">{stats.totalReplies}</div>
            <div className="text-sm text-gray-600">Total Réponses</div>
          </div>
        </Card>
      </div>

      {/* Campaigns Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {loading ? (
          <div className="col-span-full flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
          </div>
        ) : campaigns.length === 0 ? (
          <div className="col-span-full text-center py-12">
            <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">Aucune campagne créée. Commencez par créer votre première campagne.</p>
          </div>
        ) : (
          campaigns.map((campaign) => (
            <Card key={campaign.id} className="hover:shadow-md transition-shadow">
              <div className="space-y-4">
                {/* Header */}
                <div className="flex items-start justify-between">
                  <div className="flex items-center">
                    {getStatusIcon(campaign.status)}
                    <div className="ml-3">
                      <h3 className="text-lg font-semibold text-gray-900">{campaign.name}</h3>
                      {getStatusBadge(campaign.status)}
                    </div>
                  </div>
                </div>

                {/* Description */}
                <p className="text-sm text-gray-600 line-clamp-2">{campaign.description}</p>

                {/* Stats */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <div className="text-lg font-semibold text-gray-900">{campaign.total_leads}</div>
                    <div className="text-xs text-gray-600">Leads</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <div className="text-lg font-semibold text-gray-900">{campaign.emails_sent}</div>
                    <div className="text-xs text-gray-600">Envoyés</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <div className="text-lg font-semibold text-gray-900">{campaign.emails_opened}</div>
                    <div className="text-xs text-gray-600">Ouverts</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <div className="text-lg font-semibold text-gray-900">{campaign.emails_replied}</div>
                    <div className="text-xs text-gray-600">Réponses</div>
                  </div>
                </div>

                {/* Performance */}
                {campaign.emails_sent > 0 && (
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Taux d'ouverture</span>
                      <span className="font-medium">{((campaign.emails_opened / campaign.emails_sent) * 100).toFixed(1)}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full" 
                        style={{ width: `${(campaign.emails_opened / campaign.emails_sent) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                )}

                {/* Dates */}
                <div className="text-xs text-gray-500 space-y-1">
                  <div className="flex items-center">
                    <Calendar className="h-3 w-3 mr-1" />
                    Créée le {new Date(campaign.created_at).toLocaleDateString('fr-FR')}
                  </div>
                  {campaign.started_at && (
                    <div className="flex items-center">
                      <Play className="h-3 w-3 mr-1" />
                      Démarrée le {new Date(campaign.started_at).toLocaleDateString('fr-FR')}
                    </div>
                  )}
                  {campaign.completed_at && (
                    <div className="flex items-center">
                      <BarChart3 className="h-3 w-3 mr-1" />
                      Terminée le {new Date(campaign.completed_at).toLocaleDateString('fr-FR')}
                    </div>
                  )}
                </div>

                {/* Actions */}
                <div className="flex space-x-2">
                  <Button
                    variant="secondary"
                    size="sm"
                    icon={<Eye className="h-4 w-4" />}
                    onClick={() => setSelectedCampaign(campaign)}
                  >
                    Voir
                  </Button>
                  {campaign.status === 'draft' && (
                    <Button
                      variant="primary"
                      size="sm"
                      icon={<Play className="h-4 w-4" />}
                    >
                      Démarrer
                    </Button>
                  )}
                  {campaign.status === 'running' && (
                    <Button
                      variant="warning"
                      size="sm"
                      icon={<Pause className="h-4 w-4" />}
                    >
                      Pause
                    </Button>
                  )}
                </div>
              </div>
            </Card>
          ))
        )}
      </div>

      {/* Create Campaign Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Nouvelle Campagne</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Nom de la campagne
                </label>
                <input
                  type="text"
                  value={newCampaign.name}
                  onChange={(e) => setNewCampaign({ ...newCampaign, name: e.target.value })}
                  className="input-field"
                  placeholder="Ex: Campagne Digitalisation Q2 2024"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  value={newCampaign.description}
                  onChange={(e) => setNewCampaign({ ...newCampaign, description: e.target.value })}
                  className="input-field h-24 resize-none"
                  placeholder="Décrivez l'objectif de cette campagne..."
                />
              </div>
            </div>
            
            <div className="flex space-x-3 mt-6">
              <Button
                variant="secondary"
                onClick={() => setShowCreateModal(false)}
                className="flex-1"
              >
                Annuler
              </Button>
              <Button
                variant="primary"
                onClick={createCampaign}
                className="flex-1"
                disabled={!newCampaign.name.trim()}
              >
                Créer
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
