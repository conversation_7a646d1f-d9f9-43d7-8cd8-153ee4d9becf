import React, { useState, useEffect } from 'react';
import { 
  Mail, 
  Users, 
  TrendingUp, 
  Eye, 
  MousePointer, 
  Reply,
  Send,
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react';
import Card from '../components/UI/Card';
import Badge from '../components/UI/Badge';
import ApiService from '../services/api';
import type { DashboardOverview } from '../types/api';

interface StatCardProps {
  title: string;
  value: string | number;
  change?: string;
  trend?: 'up' | 'down' | 'stable';
  icon: React.ReactNode;
  color: 'blue' | 'green' | 'yellow' | 'red';
}

function StatCard({ title, value, change, trend, icon, color }: StatCardProps) {
  const colorClasses = {
    blue: 'bg-blue-500',
    green: 'bg-green-500',
    yellow: 'bg-yellow-500',
    red: 'bg-red-500',
  };

  const trendColors = {
    up: 'text-green-600',
    down: 'text-red-600',
    stable: 'text-gray-600',
  };

  return (
    <Card>
      <div className="flex items-center">
        <div className={`p-3 rounded-lg ${colorClasses[color]} text-white`}>
          {icon}
        </div>
        <div className="ml-4 flex-1">
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <div className="flex items-center">
            <p className="text-2xl font-semibold text-gray-900">{value}</p>
            {change && trend && (
              <span className={`ml-2 text-sm ${trendColors[trend]}`}>
                {change}
              </span>
            )}
          </div>
        </div>
      </div>
    </Card>
  );
}

export default function Dashboard() {
  const [overview, setOverview] = useState<DashboardOverview | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // Charger les données du dashboard
      const dashboardResponse = await ApiService.getDashboardOverview(30);

      if (dashboardResponse.success && dashboardResponse.data) {
        setOverview(dashboardResponse.data);
      } else {
        setError(dashboardResponse.message || 'Erreur lors du chargement des données');
      }

      // Charger les métriques temps réel en arrière-plan (optionnel)
      ApiService.getRealtimeMetrics().then(metricsResponse => {
        if (metricsResponse.success && metricsResponse.data) {
          console.log('Métriques temps réel:', metricsResponse.data);
        }
      }).catch(err => {
        console.warn('Erreur métriques temps réel:', err);
      });

    } catch (err) {
      setError('Erreur de connexion à l\'API');
      console.error('Dashboard error:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  if (!overview) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-600">Aucune donnée disponible</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600 mt-2">
          Vue d'ensemble de votre système de prospection B2B
        </p>
      </div>

      {/* Mode Test Alert */}
      <Card className="border-orange-200 bg-orange-50">
        <div className="flex items-center">
          <AlertCircle className="h-5 w-5 text-orange-600 mr-3" />
          <div>
            <p className="text-sm font-medium text-orange-800">🔧 Mode Test Activé</p>
            <p className="text-xs text-orange-600">
              Tous les emails sont redirigé<NAME_EMAIL> pour des raisons de sécurité.
              Aucun email ne sera envoyé aux vrais clients.
            </p>
          </div>
        </div>
      </Card>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Emails Envoyés"
          value={overview.campaign.emails_sent}
          change="0%" // Pas de données historiques pour calculer la tendance
          trend={overview.trends.emails_trend}
          icon={<Send className="h-6 w-6" />}
          color="blue"
        />
        
        <StatCard
          title="Taux d'Ouverture"
          value={`${overview.campaign.open_rate.toFixed(1)}%`}
          change="0%" // Pas de données historiques pour calculer la tendance
          trend={overview.trends.open_rate_trend}
          icon={<Eye className="h-6 w-6" />}
          color="green"
        />
        
        <StatCard
          title="Taux de Clic"
          value={`${overview.campaign.click_rate.toFixed(1)}%`}
          change="0%" // Pas de données historiques pour calculer la tendance
          trend="stable"
          icon={<MousePointer className="h-6 w-6" />}
          color="yellow"
        />
        
        <StatCard
          title="Taux de Réponse"
          value={`${overview.campaign.reply_rate.toFixed(1)}%`}
          change="0%" // Pas de données historiques pour calculer la tendance
          trend={overview.trends.reply_rate_trend}
          icon={<Reply className="h-6 w-6" />}
          color="red"
        />
      </div>

      {/* Recent Activity & Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activity */}
        <Card title="Activité Récente" subtitle="Dernières actions du système">
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                <CheckCircle className="h-5 w-5 text-green-500" />
              </div>
              <div className="flex-1">
                <p className="text-sm text-gray-900">
                  {overview.campaign.emails_sent} emails envoyés avec succès
                </p>
                <p className="text-xs text-gray-500">Il y a 2 heures</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                <Eye className="h-5 w-5 text-blue-500" />
              </div>
              <div className="flex-1">
                <p className="text-sm text-gray-900">
                  {overview.campaign.emails_opened} emails ouverts
                </p>
                <p className="text-xs text-gray-500">Il y a 4 heures</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                <Reply className="h-5 w-5 text-purple-500" />
              </div>
              <div className="flex-1">
                <p className="text-sm text-gray-900">
                  {overview.campaign.emails_replied} nouvelles réponses
                </p>
                <p className="text-xs text-gray-500">Il y a 6 heures</p>
              </div>
            </div>
          </div>
        </Card>

        {/* Quick Actions */}
        <Card title="Actions Rapides" subtitle="Raccourcis vers les fonctionnalités principales">
          <div className="grid grid-cols-2 gap-4">
            <a href="/leads" className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors block">
              <Users className="h-8 w-8 text-primary-600 mx-auto mb-2" />
              <p className="text-sm font-medium text-gray-900">Générer des Leads</p>
            </a>

            <a href="/emails" className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors block">
              <Mail className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <p className="text-sm font-medium text-gray-900">Créer des Emails</p>
            </a>

            <a href="/sent" className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors block">
              <Send className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <p className="text-sm font-medium text-gray-900">Envoyer Campagne</p>
            </a>

            <a href="/statistics" className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors block">
              <TrendingUp className="h-8 w-8 text-purple-600 mx-auto mb-2" />
              <p className="text-sm font-medium text-gray-900">Voir Statistiques</p>
            </a>
          </div>
        </Card>
      </div>

      {/* System Status */}
      <Card title="État du Système" subtitle="Statut des services">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center space-x-3">
            <Badge variant="success">Opérationnel</Badge>
            <span className="text-sm text-gray-600">API Backend</span>
          </div>
          
          <div className="flex items-center space-x-3">
            <Badge variant="success">Connecté</Badge>
            <span className="text-sm text-gray-600">Base de données</span>
          </div>
          
          <div className="flex items-center space-x-3">
            <Badge variant="success">Actif</Badge>
            <span className="text-sm text-gray-600">Service Email</span>
          </div>
        </div>
      </Card>
    </div>
  );
}
