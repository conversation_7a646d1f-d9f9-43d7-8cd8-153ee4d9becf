import React, { useState, useEffect } from 'react';
import { <PERSON>, Eye, MousePointer, <PERSON>ly, AlertCircle, CheckCircle, Clock, Filter, Download, Search } from 'lucide-react';
import Card from '../components/UI/Card';
import Button from '../components/UI/Button';
import Badge from '../components/UI/Badge';
import ApiService from '../services/api';
import type { SentEmail, EmailSendingRequest } from '../types/api';

export default function Sent() {
  const [sentEmails, setSentEmails] = useState<SentEmail[]>([]);
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [selectedEmail, setSelectedEmail] = useState<SentEmail | null>(null);
  const [filters, setFilters] = useState({
    status: 'all',
    dateRange: '7',
    search: '',
  });

  useEffect(() => {
    loadSentEmails();
  }, []);

  const loadSentEmails = async () => {
    try {
      setLoading(true);
      const response = await ApiService.getSentEmails(100, 0);
      if (response.success && response.data) {
        // Les données sont directement dans response.data (array)
        setSentEmails(Array.isArray(response.data) ? response.data : []);
      }
    } catch (error) {
      console.error('Error loading sent emails:', error);
      setSentEmails([]); // Assurer qu'on a toujours un array
    } finally {
      setLoading(false);
    }
  };

  const sendEmails = async () => {
    try {
      setSending(true);
      const request: EmailSendingRequest = {
        use_sendgrid: false,
        dry_run: false,
        max_concurrent: 3, // Maximum autorisé par l'API
      };
      const response = await ApiService.sendEmails(request);
      if (response.success && response.data) {
        await loadSentEmails(); // Recharger la liste
        alert(`Emails envoyés avec succès ! ${response.data.sent || 0} emails traités.`);
      }
    } catch (error) {
      console.error('Error sending emails:', error);
      alert('Erreur lors de l\'envoi des emails. Vérifiez la console pour plus de détails.');
    } finally {
      setSending(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'sent':
        return <Badge variant="success">Envoyé</Badge>;
      case 'failed':
        return <Badge variant="danger">Échec</Badge>;
      case 'pending':
        return <Badge variant="warning">En attente</Badge>;
      default:
        return <Badge variant="info">{status}</Badge>;
    }
  };

  const filteredEmails = sentEmails.filter(email => {
    if (filters.status !== 'all' && email.status !== filters.status) return false;
    if (filters.search && !email.company_name.toLowerCase().includes(filters.search.toLowerCase()) && 
        !email.email_to.toLowerCase().includes(filters.search.toLowerCase())) return false;
    return true;
  });

  const stats = {
    total: sentEmails.length,
    sent: sentEmails.filter(e => e.status === 'sent').length,
    failed: sentEmails.filter(e => e.status === 'failed').length,
    opened: sentEmails.filter(e => e.opened).length,
    clicked: sentEmails.filter(e => e.clicked).length,
    replied: sentEmails.filter(e => e.replied).length,
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Emails Envoyés</h1>
          <p className="text-gray-600 mt-2">
            Suivez et analysez vos campagnes d'emails envoyés
          </p>
        </div>
        <div className="flex space-x-3">
          <Button variant="secondary" icon={<Download className="h-4 w-4" />}>
            Exporter
          </Button>
          <Button 
            variant="primary" 
            icon={<Send className="h-4 w-4" />}
            onClick={sendEmails}
            loading={sending}
          >
            Envoyer Nouveaux Emails
          </Button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
        <Card>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
            <div className="text-sm text-gray-600">Total</div>
          </div>
        </Card>
        
        <Card>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{stats.sent}</div>
            <div className="text-sm text-gray-600">Envoyés</div>
          </div>
        </Card>
        
        <Card>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">{stats.failed}</div>
            <div className="text-sm text-gray-600">Échecs</div>
          </div>
        </Card>
        
        <Card>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{stats.opened}</div>
            <div className="text-sm text-gray-600">Ouverts</div>
          </div>
        </Card>
        
        <Card>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">{stats.clicked}</div>
            <div className="text-sm text-gray-600">Cliqués</div>
          </div>
        </Card>
        
        <Card>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">{stats.replied}</div>
            <div className="text-sm text-gray-600">Réponses</div>
          </div>
        </Card>
      </div>

      {/* Filters */}
      <Card title="Filtres">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Statut</label>
            <select
              value={filters.status}
              onChange={(e) => setFilters({ ...filters, status: e.target.value })}
              className="input-field"
            >
              <option value="all">Tous les statuts</option>
              <option value="sent">Envoyés</option>
              <option value="failed">Échecs</option>
              <option value="pending">En attente</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Période</label>
            <select
              value={filters.dateRange}
              onChange={(e) => setFilters({ ...filters, dateRange: e.target.value })}
              className="input-field"
            >
              <option value="1">Aujourd'hui</option>
              <option value="7">7 derniers jours</option>
              <option value="30">30 derniers jours</option>
              <option value="90">90 derniers jours</option>
            </select>
          </div>
          
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">Recherche</label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Rechercher par entreprise ou email..."
                value={filters.search}
                onChange={(e) => setFilters({ ...filters, search: e.target.value })}
                className="input-field pl-10"
              />
            </div>
          </div>
        </div>
      </Card>

      {/* Emails List */}
      <Card title="Liste des Emails" subtitle={`${filteredEmails.length} emails trouvés`}>
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
          </div>
        ) : filteredEmails.length === 0 ? (
          <div className="text-center py-12">
            <Send className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">Aucun email trouvé avec ces filtres.</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Destinataire
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Sujet
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Statut
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Interactions
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date d'envoi
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredEmails.map((email) => (
                  <tr key={email.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{email.company_name}</div>
                        <div className="text-sm text-gray-500">{email.email_to}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900 max-w-xs truncate">{email.subject}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(email.status)}
                      {email.error_message && (
                        <div className="text-xs text-red-600 mt-1">{email.error_message}</div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex space-x-2">
                        {email.opened && (
                          <div className="flex items-center text-blue-600">
                            <Eye className="h-4 w-4 mr-1" />
                            <span className="text-xs">Ouvert</span>
                          </div>
                        )}
                        {email.clicked && (
                          <div className="flex items-center text-purple-600">
                            <MousePointer className="h-4 w-4 mr-1" />
                            <span className="text-xs">Cliqué</span>
                          </div>
                        )}
                        {email.replied && (
                          <div className="flex items-center text-green-600">
                            <Reply className="h-4 w-4 mr-1" />
                            <span className="text-xs">Répondu</span>
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(email.sent_at).toLocaleDateString('fr-FR')}
                      <div className="text-xs text-gray-400">
                        {new Date(email.sent_at).toLocaleTimeString('fr-FR')}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <Button
                        variant="secondary"
                        size="sm"
                        icon={<Eye className="h-4 w-4" />}
                        onClick={() => setSelectedEmail(email)}
                      >
                        Voir
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </Card>
    </div>
  );
}
