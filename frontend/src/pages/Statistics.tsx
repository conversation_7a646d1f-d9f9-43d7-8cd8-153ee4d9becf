import React, { useState, useEffect } from 'react';
import { BarChart3, TrendingUp, TrendingDown, Calendar, Download, Filter, Eye, MousePointer, Reply, Send } from 'lucide-react';
import Card from '../components/UI/Card';
import Button from '../components/UI/Button';
import Badge from '../components/UI/Badge';
import ApiService from '../services/api';
import type { Statistics } from '../types/api';

export default function StatisticsPage() {
  const [stats, setStats] = useState<Statistics | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('30');
  const [chartType, setChartType] = useState('line');

  useEffect(() => {
    loadStatistics();
  }, [timeRange]);

  const loadStatistics = async () => {
    try {
      setLoading(true);
      const response = await ApiService.getStatistics(parseInt(timeRange));
      if (response.success && response.data) {
        setStats(response.data);
      }
    } catch (error) {
      console.error('Error loading statistics:', error);
      setStats(null);
    } finally {
      setLoading(false);
    }
  };

  const getTrendIcon = (current: number, previous: number) => {
    if (current > previous) return <TrendingUp className="h-4 w-4 text-green-500" />;
    if (current < previous) return <TrendingDown className="h-4 w-4 text-red-500" />;
    return <div className="h-4 w-4" />;
  };

  const getTrendPercentage = (current: number, previous: number) => {
    if (previous === 0) return 0;
    return ((current - previous) / previous * 100);
  };

  // Simuler des données de comparaison
  const previousPeriodStats = {
    total_sent: 380,
    opened: 145,
    clicked: 38,
    replied: 18,
    success_rate: 91.2,
    open_rate: 38.2,
    click_rate: 10.0,
    reply_rate: 4.7,
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Statistiques Détaillées</h1>
          <p className="text-gray-600 mt-2">
            Analysez les performances de vos campagnes de prospection
          </p>
        </div>
        <div className="flex space-x-3">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="input-field"
          >
            <option value="7">7 derniers jours</option>
            <option value="30">30 derniers jours</option>
            <option value="90">90 derniers jours</option>
            <option value="365">1 an</option>
          </select>
          <Button variant="secondary" icon={<Download className="h-4 w-4" />}>
            Exporter
          </Button>
        </div>
      </div>

      {loading ? (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        </div>
      ) : stats ? (
        <>
          {/* KPI Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Emails Envoyés</p>
                  <p className="text-2xl font-semibold text-gray-900">{stats.total_sent}</p>
                  <div className="flex items-center mt-1">
                    {getTrendIcon(stats.total_sent, previousPeriodStats.total_sent)}
                    <span className={`text-sm ml-1 ${
                      stats.total_sent > previousPeriodStats.total_sent ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {Math.abs(getTrendPercentage(stats.total_sent, previousPeriodStats.total_sent)).toFixed(1)}%
                    </span>
                  </div>
                </div>
                <div className="p-3 rounded-lg bg-blue-500 text-white">
                  <Send className="h-6 w-6" />
                </div>
              </div>
            </Card>

            <Card>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Taux d'Ouverture</p>
                  <p className="text-2xl font-semibold text-gray-900">{stats.open_rate.toFixed(1)}%</p>
                  <div className="flex items-center mt-1">
                    {getTrendIcon(stats.open_rate, previousPeriodStats.open_rate)}
                    <span className={`text-sm ml-1 ${
                      stats.open_rate > previousPeriodStats.open_rate ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {Math.abs(getTrendPercentage(stats.open_rate, previousPeriodStats.open_rate)).toFixed(1)}%
                    </span>
                  </div>
                </div>
                <div className="p-3 rounded-lg bg-green-500 text-white">
                  <Eye className="h-6 w-6" />
                </div>
              </div>
            </Card>

            <Card>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Taux de Clic</p>
                  <p className="text-2xl font-semibold text-gray-900">{stats.click_rate.toFixed(1)}%</p>
                  <div className="flex items-center mt-1">
                    {getTrendIcon(stats.click_rate, previousPeriodStats.click_rate)}
                    <span className={`text-sm ml-1 ${
                      stats.click_rate > previousPeriodStats.click_rate ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {Math.abs(getTrendPercentage(stats.click_rate, previousPeriodStats.click_rate)).toFixed(1)}%
                    </span>
                  </div>
                </div>
                <div className="p-3 rounded-lg bg-purple-500 text-white">
                  <MousePointer className="h-6 w-6" />
                </div>
              </div>
            </Card>

            <Card>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Taux de Réponse</p>
                  <p className="text-2xl font-semibold text-gray-900">{stats.reply_rate.toFixed(1)}%</p>
                  <div className="flex items-center mt-1">
                    {getTrendIcon(stats.reply_rate, previousPeriodStats.reply_rate)}
                    <span className={`text-sm ml-1 ${
                      stats.reply_rate > previousPeriodStats.reply_rate ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {Math.abs(getTrendPercentage(stats.reply_rate, previousPeriodStats.reply_rate)).toFixed(1)}%
                    </span>
                  </div>
                </div>
                <div className="p-3 rounded-lg bg-yellow-500 text-white">
                  <Reply className="h-6 w-6" />
                </div>
              </div>
            </Card>
          </div>

          {/* Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card title="Évolution des Envois" subtitle="Emails envoyés par jour">
              <div className="mb-4">
                <select
                  value={chartType}
                  onChange={(e) => setChartType(e.target.value)}
                  className="input-field w-32"
                >
                  <option value="line">Ligne</option>
                  <option value="bar">Barres</option>
                  <option value="area">Aire</option>
                </select>
              </div>
              <div className="h-64 bg-gray-50 rounded-lg p-4">
                {stats?.daily_breakdown && stats.daily_breakdown.length > 0 ? (
                  <div className="h-full flex items-end space-x-1">
                    {stats.daily_breakdown.slice(-14).map((day, index) => {
                      const maxCount = Math.max(...stats.daily_breakdown.map(d => d.count));
                      const height = maxCount > 0 ? (day.count / maxCount) * 100 : 0;
                      return (
                        <div key={index} className="flex-1 flex flex-col items-center">
                          <div
                            className="w-full bg-blue-500 rounded-t transition-all duration-300 hover:bg-blue-600"
                            style={{ height: `${height}%`, minHeight: day.count > 0 ? '4px' : '0px' }}
                            title={`${day.date}: ${day.count} emails`}
                          ></div>
                          <div className="text-xs text-gray-500 mt-1 transform rotate-45 origin-left">
                            {new Date(day.date).toLocaleDateString('fr-FR', { day: '2-digit', month: '2-digit' })}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <div className="h-full flex items-center justify-center">
                    <div className="text-center">
                      <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                      <p className="text-gray-600">Aucune donnée disponible</p>
                    </div>
                  </div>
                )}
              </div>
            </Card>

            <Card title="Répartition des Performances" subtitle="Comparaison des taux">
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-gray-600">Taux de Succès</span>
                    <span className="font-medium">{stats.success_rate.toFixed(1)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-green-600 h-2 rounded-full" 
                      style={{ width: `${stats.success_rate}%` }}
                    ></div>
                  </div>
                </div>

                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-gray-600">Taux d'Ouverture</span>
                    <span className="font-medium">{stats.open_rate.toFixed(1)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full" 
                      style={{ width: `${stats.open_rate}%` }}
                    ></div>
                  </div>
                </div>

                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-gray-600">Taux de Clic</span>
                    <span className="font-medium">{stats.click_rate.toFixed(1)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-purple-600 h-2 rounded-full" 
                      style={{ width: `${stats.click_rate}%` }}
                    ></div>
                  </div>
                </div>

                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-gray-600">Taux de Réponse</span>
                    <span className="font-medium">{stats.reply_rate.toFixed(1)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-yellow-600 h-2 rounded-full" 
                      style={{ width: `${stats.reply_rate}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            </Card>
          </div>

          {/* Detailed Stats */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card title="Résumé de la Période" subtitle={`Derniers ${stats.period_days} jours`}>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Total envoyés</span>
                  <span className="font-medium">{stats.total_sent}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Succès</span>
                  <span className="font-medium text-green-600">{stats.successful}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Échecs</span>
                  <span className="font-medium text-red-600">{stats.failed}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Ouverts</span>
                  <span className="font-medium text-blue-600">{stats.opened}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Cliqués</span>
                  <span className="font-medium text-purple-600">{stats.clicked}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Réponses</span>
                  <span className="font-medium text-yellow-600">{stats.replied}</span>
                </div>
              </div>
            </Card>

            <Card title="Benchmarks Secteur" subtitle="Comparaison avec la moyenne">
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Taux d'ouverture</span>
                  <div className="flex items-center">
                    <span className="font-medium mr-2">{stats.open_rate.toFixed(1)}%</span>
                    <Badge variant={stats.open_rate > 25 ? "success" : "warning"}>
                      {stats.open_rate > 25 ? "Au-dessus" : "En-dessous"}
                    </Badge>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Taux de clic</span>
                  <div className="flex items-center">
                    <span className="font-medium mr-2">{stats.click_rate.toFixed(1)}%</span>
                    <Badge variant={stats.click_rate > 3 ? "success" : "warning"}>
                      {stats.click_rate > 3 ? "Au-dessus" : "En-dessous"}
                    </Badge>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Taux de réponse</span>
                  <div className="flex items-center">
                    <span className="font-medium mr-2">{stats.reply_rate.toFixed(1)}%</span>
                    <Badge variant={stats.reply_rate > 1 ? "success" : "warning"}>
                      {stats.reply_rate > 1 ? "Au-dessus" : "En-dessous"}
                    </Badge>
                  </div>
                </div>
              </div>
            </Card>

            <Card title="Objectifs" subtitle="Progression vers les objectifs">
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-gray-600">Emails mensuels</span>
                    <span className="font-medium">{stats.total_sent}/1000</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full" 
                      style={{ width: `${Math.min((stats.total_sent / 1000) * 100, 100)}%` }}
                    ></div>
                  </div>
                </div>

                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-gray-600">Taux d'ouverture cible</span>
                    <span className="font-medium">{stats.open_rate.toFixed(1)}%/30%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-green-600 h-2 rounded-full" 
                      style={{ width: `${Math.min((stats.open_rate / 30) * 100, 100)}%` }}
                    ></div>
                  </div>
                </div>

                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-gray-600">Réponses mensuelles</span>
                    <span className="font-medium">{stats.replied}/50</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-yellow-600 h-2 rounded-full" 
                      style={{ width: `${Math.min((stats.replied / 50) * 100, 100)}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </>
      ) : (
        <div className="text-center py-12">
          <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">Aucune donnée statistique disponible</p>
        </div>
      )}
    </div>
  );
}
