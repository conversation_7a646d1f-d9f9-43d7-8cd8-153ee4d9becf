import React, { useState, useEffect } from 'react';
import { Mail, Wand2, Eye, Download, Filter, Plus, Search, Bot, FileText } from 'lucide-react';
import Card from '../components/UI/Card';
import Button from '../components/UI/Button';
import Badge from '../components/UI/Badge';
import ApiService from '../services/api';
import type { EmailTemplate, EmailGenerationRequest } from '../types/api';

export default function Emails() {
  const [templates, setTemplates] = useState<EmailTemplate[]>([]);
  const [loading, setLoading] = useState(false);
  const [generating, setGenerating] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<EmailTemplate | null>(null);
  const [tokenUsage, setTokenUsage] = useState<any>(null);
  const [filters, setFilters] = useState<EmailGenerationRequest>({
    skip_good_websites: true,
    max_concurrent: 5,
  });

  const generateEmails = async () => {
    try {
      setGenerating(true);
      const response = await ApiService.generateEmails(filters);
      if (response.success && response.data) {
        // Les templates sont dans response.data.templates ou directement dans response.data
        const templates = response.data.templates || response.data || [];
        setTemplates(Array.isArray(templates) ? templates : []);

        if (templates.length > 0) {
          alert(`${templates.length} templates d'emails générés avec succès !`);
        }
      }
    } catch (error) {
      console.error('Error generating emails:', error);
      alert('Erreur lors de la génération des emails. Vérifiez la console pour plus de détails.');
      setTemplates([]);
    } finally {
      setGenerating(false);
    }
  };

  const loadTokenUsage = async () => {
    try {
      const response = await ApiService.getTokenUsage();
      if (response.success && response.data) {
        setTokenUsage(response.data);
      }
    } catch (error) {
      console.error('Erreur chargement tokens:', error);
    }
  };

  // Charger les données de tokens au démarrage
  React.useEffect(() => {
    loadTokenUsage();
  }, []);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Gestion des Emails</h1>
          <p className="text-gray-600 mt-2">
            Générez et gérez vos templates d'emails personnalisés avec l'IA
          </p>
        </div>
        <div className="flex space-x-3">
          <Button variant="secondary" icon={<Download className="h-4 w-4" />}>
            Exporter Templates
          </Button>
          <Button 
            variant="primary" 
            icon={<Wand2 className="h-4 w-4" />}
            onClick={generateEmails}
            loading={generating}
          >
            Générer avec IA
          </Button>
        </div>
      </div>

      {/* Configuration */}
      <Card title="Configuration de Génération" className="mb-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Concurrence maximale
            </label>
            <input
              type="number"
              value={filters.max_concurrent}
              onChange={(e) => setFilters({ ...filters, max_concurrent: parseInt(e.target.value) })}
              className="input-field"
              min="1"
              max="20"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Filtrage des sites web
            </label>
            <select
              value={filters.skip_good_websites ? 'skip' : 'include'}
              onChange={(e) => setFilters({ ...filters, skip_good_websites: e.target.value === 'skip' })}
              className="input-field"
            >
              <option value="skip">Ignorer les bons sites</option>
              <option value="include">Inclure tous les sites</option>
            </select>
          </div>
          
          <div className="flex items-end">
            <Button 
              variant="secondary" 
              icon={<Filter className="h-4 w-4" />}
              onClick={generateEmails}
              loading={generating}
              className="w-full"
            >
              Appliquer
            </Button>
          </div>
        </div>
      </Card>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <div className="flex items-center">
            <div className="p-3 rounded-lg bg-blue-500 text-white">
              <Mail className="h-6 w-6" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Templates Générés</p>
              <p className="text-2xl font-semibold text-gray-900">{templates.length}</p>
            </div>
          </div>
        </Card>
        
        <Card>
          <div className="flex items-center">
            <div className="p-3 rounded-lg bg-green-500 text-white">
              <Bot className="h-6 w-6" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">IA Utilisée</p>
              <p className="text-2xl font-semibold text-gray-900">GPT-4</p>
            </div>
          </div>
        </Card>
        
        <Card>
          <div className="flex items-center">
            <div className="p-3 rounded-lg bg-purple-500 text-white">
              <FileText className="h-6 w-6" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Personnalisations</p>
              <p className="text-2xl font-semibold text-gray-900">
                {templates.reduce((acc, t) => acc + Object.keys(t.personalization_tokens).length, 0)}
              </p>
            </div>
          </div>
        </Card>
        
        <Card>
          <div className="flex items-center">
            <div className="p-3 rounded-lg bg-yellow-500 text-white">
              <Wand2 className="h-6 w-6" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Tokens Utilisés</p>
              <p className="text-2xl font-semibold text-gray-900">
                {tokenUsage ? `${(tokenUsage.total_tokens / 1000).toFixed(1)}K` : '0'}
              </p>
              <p className="text-xs text-gray-500">
                {tokenUsage ? `$${tokenUsage.cost_usd}` : '$0.00'}
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* Templates List */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card title="Templates d'Emails" subtitle={`${templates.length} templates générés`}>
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
            </div>
          ) : templates.length === 0 ? (
            <div className="text-center py-12">
              <Mail className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">Aucun template généré. Cliquez sur "Générer avec IA" pour commencer.</p>
            </div>
          ) : (
            <div className="space-y-4 max-h-96 overflow-y-auto">
              {templates.map((template, index) => (
                <div 
                  key={index} 
                  className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                    selectedTemplate === template ? 'border-primary-500 bg-primary-50' : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setSelectedTemplate(template)}
                >
                  <div className="flex items-start justify-between mb-2">
                    <h4 className="font-medium text-gray-900 truncate">{template.subject}</h4>
                    <Badge variant="info">{template.model_used}</Badge>
                  </div>
                  <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                    {template.body.substring(0, 100)}...
                  </p>
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span>{Object.keys(template.personalization_tokens).length} personnalisations</span>
                    <span>{new Date(template.generated_at).toLocaleDateString()}</span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </Card>

        {/* Template Preview */}
        <Card title="Aperçu du Template" subtitle="Prévisualisation et édition">
          {selectedTemplate ? (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Sujet</label>
                <input
                  type="text"
                  value={selectedTemplate.subject}
                  className="input-field"
                  readOnly
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Corps du message</label>
                <textarea
                  value={selectedTemplate.body}
                  className="input-field h-48 resize-none"
                  readOnly
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Tokens de personnalisation</label>
                <div className="space-y-2">
                  {Object.entries(selectedTemplate.personalization_tokens).map(([key, value]) => (
                    <div key={key} className="flex items-center space-x-2">
                      <Badge variant="info">{key}</Badge>
                      <span className="text-sm text-gray-600">{value}</span>
                    </div>
                  ))}
                </div>
              </div>
              
              <div className="flex space-x-2">
                <Button variant="primary" icon={<Eye className="h-4 w-4" />} size="sm">
                  Prévisualiser
                </Button>
                <Button variant="secondary" icon={<Download className="h-4 w-4" />} size="sm">
                  Exporter
                </Button>
              </div>
            </div>
          ) : (
            <div className="text-center py-12">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">Sélectionnez un template pour le prévisualiser</p>
            </div>
          )}
        </Card>
      </div>
    </div>
  );
}
