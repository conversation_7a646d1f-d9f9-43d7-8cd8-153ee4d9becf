import React, { useState } from 'react';
import { Play, Brain, Mail, Send, CheckCircle, AlertCircle, Clock, Target, Settings } from 'lucide-react';
import { api } from '../services/api';

interface CampaignStep {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'running' | 'completed' | 'error';
  icon: React.ReactNode;
  results?: any;
}

interface CampaignConfig {
  maxLeads: number;
  recentOnly: boolean;
  nafCodes: string[];
  regions: string[];
  skipGoodWebsites: boolean;
  dryRun: boolean;
  maxConcurrent: number;
}

const Campaign: React.FC = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [config, setConfig] = useState<CampaignConfig>({
    maxLeads: 3,  // Réduit à 3 pour des tests plus rapides
    recentOnly: true,
    nafCodes: [],
    regions: [],
    skipGoodWebsites: true,
    dryRun: true,
    maxConcurrent: 3
  });

  const [steps, setSteps] = useState<CampaignStep[]>([
    {
      id: 'generate',
      name: 'Génération de Leads',
      description: 'Recherche d\'entreprises ciblées',
      status: 'pending',
      icon: <Target className="w-5 h-5" />
    },
    {
      id: 'analyze',
      name: 'Analyse Intelligente',
      description: 'Analyse des sites web et détection des pain points',
      status: 'pending',
      icon: <Brain className="w-5 h-5" />
    },
    {
      id: 'generate-emails',
      name: 'Génération d\'Emails',
      description: 'Création d\'emails personnalisés avec IA',
      status: 'pending',
      icon: <Mail className="w-5 h-5" />
    },
    {
      id: 'send',
      name: 'Envoi Conditionnel',
      description: 'Envoi seulement aux prospects qualifiés',
      status: 'pending',
      icon: <Send className="w-5 h-5" />
    }
  ]);

  const updateStepStatus = (stepId: string, status: CampaignStep['status'], results?: any) => {
    setSteps(prev => prev.map(step => 
      step.id === stepId ? { ...step, status, results } : step
    ));
  };

  const runCompleteWorkflow = async () => {
    setIsRunning(true);
    setCurrentStep(0);

    try {
      // Étape 1: Génération de Leads
      console.log('🚀 Étape 1: Génération de Leads');
      updateStepStatus('generate', 'running');
      
      const leadsResponse = await api.post('/api/v1/leads/generate', {
        max_results: config.maxLeads,
        recent_only: config.recentOnly,
        naf_codes: config.nafCodes.length > 0 ? config.nafCodes : null,
        regions: config.regions.length > 0 ? config.regions : null
      });

      const leads = leadsResponse.data.data?.leads || [];
      updateStepStatus('generate', 'completed', {
        count: leads.length,
        leads: leads
      });
      setCurrentStep(1);

      if (leads.length === 0) {
        throw new Error('Aucun lead généré');
      }

      // Étape 2: Analyse Intelligente
      console.log('🧠 Étape 2: Analyse Intelligente');
      updateStepStatus('analyze', 'running');
      
      const analysisResponse = await api.post('/api/v1/leads/analyze', {
        company_ids: leads.map((lead: any) => lead.siren),
        max_concurrent: config.maxConcurrent
      });

      const contexts = analysisResponse.data.data?.contexts || [];
      updateStepStatus('analyze', 'completed', {
        analyzed: contexts.length,
        highPriority: contexts.filter((c: any) => c.priority === 'high').length,
        avgScore: contexts.length > 0 ? contexts.reduce((sum: number, c: any) => sum + (c.lead_score || 0), 0) / contexts.length : 0
      });
      setCurrentStep(2);

      // Étape 3: Génération d'Emails
      console.log('✉️ Étape 3: Génération d\'Emails');
      updateStepStatus('generate-emails', 'running');
      
      const emailsResponse = await api.post('/api/v1/emails/generate', {
        max_concurrent: config.maxConcurrent,
        skip_good_websites: config.skipGoodWebsites
      });

      const emails = emailsResponse.data.data?.emails || [];
      const skippedCount = emailsResponse.data.data?.skipped_count || 0;
      updateStepStatus('generate-emails', 'completed', {
        generated: emails.length,
        skipped: skippedCount
      });
      setCurrentStep(3);

      // Étape 4: Envoi Conditionnel
      console.log('📤 Étape 4: Envoi Conditionnel');
      updateStepStatus('send', 'running');
      
      const sendResponse = await api.post('/api/v1/emails/send', {
        dry_run: config.dryRun,
        max_concurrent: 1,
        use_sendgrid: false
      });

      const sendResults = sendResponse.data.data || {};
      updateStepStatus('send', 'completed', {
        sent: sendResults.successful || 0,
        failed: sendResults.failed || 0,
        duplicates: sendResults.duplicates || 0
      });

      console.log('✅ Campagne terminée avec succès !');

    } catch (error: any) {
      console.error('❌ Erreur campagne:', error);
      const currentStepId = steps[currentStep]?.id;
      if (currentStepId) {
        updateStepStatus(currentStepId, 'error');
      }

      let errorMessage = 'Erreur inconnue';
      if (error.response?.status === 409) {
        errorMessage = 'Une génération est déjà en cours. Attendez quelques secondes et réessayez.';
      } else if (error.response?.data?.detail) {
        errorMessage = error.response.data.detail;
      } else if (error.message) {
        errorMessage = error.message;
      }

      alert(`Erreur à l'étape ${currentStep + 1}: ${errorMessage}`);
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (status: CampaignStep['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'running':
        return <Clock className="w-5 h-5 text-blue-500 animate-spin" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      default:
        return <div className="w-5 h-5 rounded-full border-2 border-gray-300" />;
    }
  };

  const getStatusColor = (status: CampaignStep['status']) => {
    switch (status) {
      case 'completed':
        return 'bg-green-50 border-green-200';
      case 'running':
        return 'bg-blue-50 border-blue-200';
      case 'error':
        return 'bg-red-50 border-red-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };

  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <Brain className="w-8 h-8 text-blue-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Campagne Intelligente</h1>
              <p className="text-gray-600 mt-1">
                Workflow complet : Génération → Analyse → Emails → Envoi
              </p>
            </div>
          </div>
          <button
            onClick={runCompleteWorkflow}
            disabled={isRunning}
            className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-colors ${
              isRunning
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'bg-blue-600 text-white hover:bg-blue-700'
            }`}
          >
            <Play className="w-5 h-5" />
            <span>
              {isRunning
                ? `Étape ${currentStep + 1}/4 en cours...`
                : 'Lancer la Campagne'
              }
            </span>
          </button>
        </div>

        {/* Configuration */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8 p-4 bg-gray-50 rounded-lg">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Nombre de leads
            </label>
            <input
              type="number"
              value={config.maxLeads}
              onChange={(e) => setConfig(prev => ({ ...prev, maxLeads: parseInt(e.target.value) || 1 }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
              min="1"
              max="20"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Concurrence max
            </label>
            <input
              type="number"
              value={config.maxConcurrent}
              onChange={(e) => setConfig(prev => ({ ...prev, maxConcurrent: parseInt(e.target.value) || 1 }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
              min="1"
              max="5"
            />
          </div>
          <div className="flex items-center space-x-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={config.recentOnly}
                onChange={(e) => setConfig(prev => ({ ...prev, recentOnly: e.target.checked }))}
                className="mr-2"
              />
              <span className="text-sm text-gray-700">Récents uniquement</span>
            </label>
          </div>
          <div className="flex items-center space-x-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={config.dryRun}
                onChange={(e) => setConfig(prev => ({ ...prev, dryRun: e.target.checked }))}
                className="mr-2"
              />
              <span className="text-sm text-gray-700">Mode test</span>
            </label>
          </div>
        </div>

        {/* Étapes du workflow */}
        <div className="space-y-4">
          {steps.map((step, index) => (
            <div
              key={step.id}
              className={`p-4 rounded-lg border-2 transition-all ${getStatusColor(step.status)} ${
                index === currentStep && isRunning ? 'ring-2 ring-blue-300' : ''
              }`}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {getStatusIcon(step.status)}
                  <div className="flex items-center space-x-2 text-gray-600">
                    {step.icon}
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">{step.name}</h3>
                    <p className="text-sm text-gray-600">{step.description}</p>
                  </div>
                </div>
                
                {step.results && (
                  <div className="text-right">
                    {step.id === 'generate' && (
                      <div className="text-sm">
                        <div className="font-medium text-green-600">{step.results.count} leads générés</div>
                      </div>
                    )}
                    {step.id === 'analyze' && (
                      <div className="text-sm">
                        <div className="font-medium text-blue-600">{step.results.analyzed} analysés</div>
                        <div className="text-gray-600">{step.results.highPriority} haute priorité</div>
                        <div className="text-gray-600">Score moyen: {step.results.avgScore?.toFixed(1)}</div>
                      </div>
                    )}
                    {step.id === 'generate-emails' && (
                      <div className="text-sm">
                        <div className="font-medium text-purple-600">{step.results.generated} emails générés</div>
                        <div className="text-gray-600">{step.results.skipped} ignorés</div>
                      </div>
                    )}
                    {step.id === 'send' && (
                      <div className="text-sm">
                        <div className="font-medium text-green-600">{step.results.sent} envoyés</div>
                        <div className="text-gray-600">{step.results.failed} échecs</div>
                        <div className="text-gray-600">{step.results.duplicates} doublons</div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Informations sur le système */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Système de Prospection Intelligente</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex items-center space-x-2 mb-2">
              <Target className="w-5 h-5 text-blue-600" />
              <h3 className="font-medium text-blue-900">Génération de Leads</h3>
            </div>
            <p className="text-sm text-blue-700">API Sirene officielle</p>
            <p className="text-xs text-blue-600 mt-1">Enrichissement automatique</p>
          </div>

          <div className="p-4 bg-purple-50 rounded-lg border border-purple-200">
            <div className="flex items-center space-x-2 mb-2">
              <Brain className="w-5 h-5 text-purple-600" />
              <h3 className="font-medium text-purple-900">Analyse IA</h3>
            </div>
            <p className="text-sm text-purple-700">Scraping + Pain Points</p>
            <p className="text-xs text-purple-600 mt-1">Scoring 0-100</p>
          </div>

          <div className="p-4 bg-green-50 rounded-lg border border-green-200">
            <div className="flex items-center space-x-2 mb-2">
              <Mail className="w-5 h-5 text-green-600" />
              <h3 className="font-medium text-green-900">Emails IA</h3>
            </div>
            <p className="text-sm text-green-700">OpenAI GPT-4</p>
            <p className="text-xs text-green-600 mt-1">Personnalisation unique</p>
          </div>

          <div className="p-4 bg-orange-50 rounded-lg border border-orange-200">
            <div className="flex items-center space-x-2 mb-2">
              <Send className="w-5 h-5 text-orange-600" />
              <h3 className="font-medium text-orange-900">Envoi Intelligent</h3>
            </div>
            <p className="text-sm text-orange-700">Anti-spam + Tracking</p>
            <p className="text-xs text-orange-600 mt-1">Prospects qualifiés</p>
          </div>
        </div>

        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center space-x-2 mb-2">
            <Settings className="w-5 h-5 text-gray-600" />
            <h3 className="font-medium text-gray-900">Configuration Recommandée</h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-700">Premier test :</span>
              <span className="text-gray-600 ml-1">5 leads, mode test activé</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">Production :</span>
              <span className="text-gray-600 ml-1">10-20 leads, mode test désactivé</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">Performance :</span>
              <span className="text-gray-600 ml-1">Concurrence 3-5 max</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Campaign;
