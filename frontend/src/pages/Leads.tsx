import React, { useState, useEffect } from 'react';
import { Target, Download, Filter, Plus, Search, Building2, MapPin, Mail } from 'lucide-react';
import Card from '../components/UI/Card';
import Button from '../components/UI/Button';
import Badge from '../components/UI/Badge';
import ApiService from '../services/api';
import type { Company, LeadGenerationRequest } from '../types/api';

export default function Leads() {
  const [leads, setLeads] = useState<Company[]>([]);
  const [loading, setLoading] = useState(false);
  const [generating, setGenerating] = useState(false);
  const [filters, setFilters] = useState<LeadGenerationRequest>({
    max_results: 50,
    recent_only: true,
    days_back: 30,
  });

  useEffect(() => {
    loadLeads();
  }, []);

  const loadLeads = async () => {
    try {
      setLoading(true);
      const response = await ApiService.getLeads(100, 0);
      if (response.success && response.data) {
        // Les données sont directement dans response.data (array)
        setLeads(Array.isArray(response.data) ? response.data : []);
      }
    } catch (error) {
      console.error('Error loading leads:', error);
      setLeads([]); // Assurer qu'on a toujours un array
    } finally {
      setLoading(false);
    }
  };

  const generateLeads = async () => {
    try {
      setGenerating(true);
      console.log('🚀 Génération de leads avec filtres:', filters);

      const response = await ApiService.generateLeads(filters);
      console.log('📥 Réponse API reçue:', response);

      if (response.success && response.data) {
        // Les leads peuvent être dans response.data.leads ou directement dans response.data
        const leads = response.data.leads || response.data || [];
        console.log('📊 Leads extraits:', leads);

        setLeads(Array.isArray(leads) ? leads : []);
        console.log('✅ Leads mis à jour dans l\'état');

        // Recharger aussi la liste existante
        await loadLeads();

        // Afficher un message de succès
        alert(`✅ ${leads.length} leads générés avec succès !`);
      } else {
        console.warn('⚠️ Réponse API sans succès ou sans données:', response);
        alert('Aucun lead généré. Vérifiez les filtres ou réessayez.');
      }
    } catch (error: any) {
      console.error('❌ Erreur génération leads:', error);

      // Gestion spécifique des erreurs
      if (error.response?.status === 409) {
        alert('⏳ Une génération de leads est déjà en cours. Veuillez attendre qu\'elle se termine avant de relancer.');
      } else if (error.response?.status === 408 || error.code === 'ECONNABORTED') {
        alert('⏱️ La génération de leads prend plus de temps que prévu. Elle continue en arrière-plan. Rechargez la page dans quelques minutes.');
      } else {
        alert('❌ Erreur lors de la génération des leads. Vérifiez la console pour plus de détails.');
      }
    } finally {
      setGenerating(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Génération de Leads</h1>
          <p className="text-gray-600 mt-2">
            Trouvez et générez des prospects qualifiés pour vos campagnes
          </p>
        </div>
        <div className="flex space-x-3">
          <Button variant="secondary" icon={<Download className="h-4 w-4" />}>
            Exporter
          </Button>
          <Button 
            variant="primary" 
            icon={<Plus className="h-4 w-4" />}
            onClick={generateLeads}
            loading={generating}
          >
            Générer des Leads
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card title="Filtres de Recherche" className="mb-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Nombre maximum
            </label>
            <input
              type="number"
              value={filters.max_results}
              onChange={(e) => setFilters({ ...filters, max_results: parseInt(e.target.value) })}
              className="input-field"
              min="1"
              max="1000"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Période (jours)
            </label>
            <input
              type="number"
              value={filters.days_back}
              onChange={(e) => setFilters({ ...filters, days_back: parseInt(e.target.value) })}
              className="input-field"
              min="1"
              max="365"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Type de recherche
            </label>
            <select
              value={filters.recent_only ? 'recent' : 'all'}
              onChange={(e) => setFilters({ ...filters, recent_only: e.target.value === 'recent' })}
              className="input-field"
            >
              <option value="recent">Entreprises récentes</option>
              <option value="all">Toutes les entreprises</option>
            </select>
          </div>
          
          <div className="flex items-end">
            <Button 
              variant="secondary" 
              icon={<Filter className="h-4 w-4" />}
              onClick={generateLeads}
              loading={generating}
              className="w-full"
            >
              Appliquer
            </Button>
          </div>
        </div>
      </Card>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <div className="flex items-center">
            <div className="p-3 rounded-lg bg-blue-500 text-white">
              <Target className="h-6 w-6" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Leads</p>
              <p className="text-2xl font-semibold text-gray-900">{leads.length}</p>
            </div>
          </div>
        </Card>
        
        <Card>
          <div className="flex items-center">
            <div className="p-3 rounded-lg bg-green-500 text-white">
              <Mail className="h-6 w-6" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Avec Email</p>
              <p className="text-2xl font-semibold text-gray-900">
                {leads.filter(lead => lead.email).length}
              </p>
            </div>
          </div>
        </Card>
        
        <Card>
          <div className="flex items-center">
            <div className="p-3 rounded-lg bg-purple-500 text-white">
              <Building2 className="h-6 w-6" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Avec Site Web</p>
              <p className="text-2xl font-semibold text-gray-900">
                {leads.filter(lead => lead.website).length}
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* Leads Table */}
      <Card title="Liste des Leads" subtitle={`${leads.length} prospects trouvés`}>
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
          </div>
        ) : leads.length === 0 ? (
          <div className="text-center py-12">
            <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">Aucun lead trouvé. Cliquez sur "Générer des Leads" pour commencer.</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Entreprise
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Localisation
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Contact
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Secteur
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Source
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {leads.map((lead, index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{lead.name}</div>
                        <div className="text-sm text-gray-500">SIREN: {lead.siren}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center text-sm text-gray-900">
                        <MapPin className="h-4 w-4 mr-1 text-gray-400" />
                        {lead.city}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="space-y-1">
                        {lead.email && (
                          <div className="flex items-center text-sm text-gray-900">
                            <Mail className="h-4 w-4 mr-1 text-gray-400" />
                            {lead.email}
                          </div>
                        )}
                        {lead.website && (
                          <div className="text-sm text-blue-600 hover:text-blue-800">
                            <a href={lead.website} target="_blank" rel="noopener noreferrer">
                              {lead.website}
                            </a>
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{lead.naf_label || 'Non spécifié'}</div>
                      <div className="text-sm text-gray-500">{lead.naf_code}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <Badge variant="info">{lead.source}</Badge>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </Card>
    </div>
  );
}
