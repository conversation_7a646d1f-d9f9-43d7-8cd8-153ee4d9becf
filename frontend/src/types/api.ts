// Types pour l'API de prospection B2B

export interface Company {
  siren: string;
  name: string;
  city: string;
  email: string;
  website?: string;
  naf_code?: string;
  naf_label?: string;
  employees?: number;
  size?: string;
  source: string;
  created_at: string;
}

export interface EmailTemplate {
  subject: string;
  body: string;
  personalization_tokens: Record<string, string>;
  generated_at: string;
  model_used: string;
}

export interface SentEmail {
  id: number;
  company_name: string;
  company_siren?: string;
  email_to: string;
  subject: string;
  body: string;
  status: 'sent' | 'failed' | 'pending';
  sent_at: string;
  error_message?: string;
  provider: string;
  tracking_id?: string;
  opened?: boolean;
  opened_at?: string;
  clicked?: boolean;
  clicked_at?: string;
  replied?: boolean;
  replied_at?: string;
}

export interface Campaign {
  id: string;
  name: string;
  description?: string;
  created_at: string;
  started_at?: string;
  completed_at?: string;
  total_leads: number;
  emails_sent: number;
  emails_opened: number;
  emails_replied: number;
  status: 'draft' | 'running' | 'completed' | 'paused';
}

export interface Statistics {
  period_days: number;
  total_sent: number;
  successful: number;
  failed: number;
  opened: number;
  clicked: number;
  replied: number;
  today_emails: number;
  success_rate: number;
  open_rate: number;
  click_rate: number;
  reply_rate: number;
  daily_breakdown: Array<{
    date: string;
    count: number;
  }>;
}

export interface DashboardOverview {
  period_days: number;
  campaign: {
    emails_sent: number;
    emails_opened: number;
    emails_clicked: number;
    emails_replied: number;
    followups_sent: number;
    open_rate: number;
    click_rate: number;
    reply_rate: number;
  };
  tracking: {
    total_events: number;
    open_rate: number;
    click_rate: number;
  };
  followups: {
    emails_needing_followup: number;
    followups_sent: number;
    followup_response_rate: number;
  };
  trends: {
    emails_trend: 'up' | 'down' | 'stable';
    open_rate_trend: 'up' | 'down' | 'stable';
    reply_rate_trend: 'up' | 'down' | 'stable';
  };
}

export interface ApiResponse<T> {
  success: boolean;
  message: string;
  data: T | null;
  error?: string;
}

export interface LeadGenerationRequest {
  max_results?: number;
  naf_codes?: string[];
  regions?: string[];
  recent_only?: boolean;
  days_back?: number;
}

export interface EmailGenerationRequest {
  skip_good_websites?: boolean;
  max_concurrent?: number;
}

export interface EmailSendingRequest {
  use_sendgrid?: boolean;
  dry_run?: boolean;
  max_concurrent?: number;
}
