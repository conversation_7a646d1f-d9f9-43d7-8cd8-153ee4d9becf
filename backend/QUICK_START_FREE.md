# 🚀 Démarrage Rapide - APIs Gratuites Uniquement

> **Prospection B2B 100% gratuite** (sauf OpenAI pour les emails)

## ⚡ Installation Express (5 minutes)

### 1. **Cloner et installer**
```bash
git clone <repository-url>
cd prospection_v2
make install
```

### 2. **Configuration minimale**
```bash
# Copier la configuration APIs gratuites
make setup-free

# Éditer les variables d'environnement
nano .env
```

### 3. **Variables obligatoires (.env)**
```env
# SEULE API PAYANTE (obligatoire)
OPENAI_API_KEY=your_openai_key_here

# Gmail gratuit (500 emails/jour)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=Votre Nom
```

### 4. **Test des APIs**
```bash
# Vérifier que tout fonctionne
make test-free-apis
```

### 5. **Premier pipeline**
```bash
# Démonstration complète (10 leads)
make demo-free
```

## 🎯 Utilisation Quotidienne

### Pipeline complet automatisé
```bash
# 50 leads → analyse → emails → test d'envoi
make pipeline-free
```

### Commandes individuelles
```bash
# 1. Générer 100 leads (API Sirene gratuite)
python cli.py generate-leads --max-results 100

# 2. Analyser les entreprises (scraping gratuit)
python cli.py analyze --concurrent 3

# 3. Générer emails personnalisés (OpenAI ~$1)
python cli.py generate-emails --concurrent 2

# 4. Test d'envoi (gratuit)
python cli.py send --dry-run

# 5. Envoi réel (Gmail gratuit jusqu'à 500/jour)
python cli.py send --real
```

## 📊 Sources Gratuites Utilisées

| Source | Type | Limite | Qualité | Coût |
|--------|------|--------|---------|------|
| **API Sirene v3** | Officielle | 7 req/s | ⭐⭐⭐⭐⭐ | 🆓 |
| **API Entreprise** | Officielle | Généreuse | ⭐⭐⭐⭐ | 🆓 |
| **OpenDataSoft** | Publique | Élevée | ⭐⭐⭐⭐ | 🆓 |
| **Recherche emails** | Scraping+DNS | Modérée | ⭐⭐⭐ | 🆓 |
| **Gmail SMTP** | Email | 500/jour | ⭐⭐⭐⭐ | 🆓 |
| **OpenAI GPT-4o** | IA | 60 req/min | ⭐⭐⭐⭐⭐ | 💰 |

## 💰 Coûts Réels

### Pour 100 prospects qualifiés :
- **APIs gratuites** : 0€
- **OpenAI (emails)** : ~1-2€
- **Gmail (envoi)** : 0€
- **Total** : **~2€ maximum**

### Comparaison avec solutions payantes :
- **Hunter.io** : ~20€/mois
- **Clearbit** : ~100€/mois
- **Apollo** : ~50€/mois
- **Notre solution** : **~10€/mois** (OpenAI uniquement)

## 🔧 Configuration Avancée

### Ciblage précis (config.yaml)
```yaml
search_filters:
  sirene:
    naf_codes: ["6201Z", "6202A"]  # IT uniquement
    regions: ["11", "84"]          # IDF + Rhône-Alpes
    min_employees: 10
    max_employees: 100
```

### Rate limits conservateurs
```yaml
rate_limits:
  sirene_api: 5      # Au lieu de 7 (sécurité)
  email_sending: 3   # Éviter le spam
  openai_api: 30     # Économiser les tokens
```

## 📈 Performances Attendues

### Vitesses typiques :
- **Génération leads** : 50-100/heure
- **Analyse entreprises** : 20-30/heure  
- **Génération emails** : 60/heure
- **Envoi emails** : 300/heure (5/min)

### Taux de succès :
- **Leads avec email** : 60-80%
- **Sites web analysés** : 70-90%
- **Emails générés** : 100%
- **Emails livrés** : 95%+

## 🎯 Exemples de Résultats

### Lead généré automatiquement :
```json
{
  "name": "DIGITAL SOLUTIONS SARL",
  "city": "Lyon",
  "employees": 25,
  "naf_code": "6201Z",
  "domain": "digital-solutions.fr",
  "email": "<EMAIL>",
  "lead_score": 78,
  "priority": "high"
}
```

### Email généré par IA :
```
Objet: Modernisation de votre infrastructure web - DIGITAL SOLUTIONS

Bonjour,

En analysant le site de DIGITAL SOLUTIONS, j'ai remarqué votre 
expertise en développement informatique sur Lyon.

Opportunités identifiées :
• Site non responsive (40% trafic mobile perdu)
• Absence d'analytics (pilotage à l'aveugle)
• Processus manuels détectés

Notre proposition : refonte responsive + analytics + automatisation

Bénéfices : +40% conversion, visibilité complète, +25% productivité

Disponible pour 15min d'échange cette semaine ?

Cordialement,
Votre Nom - 06 XX XX XX XX
```

## 🔍 Dépannage Express

### "API Sirene inaccessible"
```bash
# Test manuel
curl "https://api.insee.fr/entreprises/sirene/V3/siren?q=siren:552032534"
```

### "Pas d'emails trouvés"
```bash
# Vérifier la recherche d'emails
python test_free_apis.py
```

### "Rate limit atteint"
```bash
# Réduire les limites dans config.yaml
rate_limits:
  sirene_api: 3  # Au lieu de 5
```

### "OpenAI trop cher"
```bash
# Réduire les tokens
openai:
  max_tokens: 1500  # Au lieu de 2200
  temperature: 0.5  # Plus déterministe
```

## 📚 Ressources Utiles

### Documentation :
- **README.md** : Guide complet
- **FREE_APIS_GUIDE.md** : Détails techniques APIs
- **ARCHITECTURE.md** : Architecture système

### Commandes utiles :
```bash
# Aide complète
make help

# Variables d'environnement
make env-help

# Statistiques
python cli.py stats --days 7

# Logs en temps réel
tail -f logs/prospection.log
```

## 🚀 Prochaines Étapes

### 1. **Optimiser le ciblage**
- Affiner les codes NAF
- Cibler des régions spécifiques
- Ajuster les tailles d'entreprise

### 2. **Améliorer les emails**
- Tester différents templates
- A/B tester les objets
- Personnaliser par secteur

### 3. **Automatiser**
- Cron job quotidien
- Monitoring des performances
- Alertes sur erreurs

### 4. **Scaler**
- Augmenter les volumes
- Paralléliser les traitements
- Optimiser les coûts OpenAI

## 💡 Conseils Pro

### Maximiser le ROI :
1. **Ciblage laser** : Mieux vaut 50 prospects parfaits que 500 moyens
2. **Personnalisation** : L'IA excelle dans la personnalisation
3. **Timing** : Envoyer mardi-jeudi 10h-16h
4. **Suivi** : Relancer après 1 semaine si pas de réponse

### Éviter les erreurs :
1. **Pas de spam** : Respecter les 90 jours entre contacts
2. **Rate limiting** : Ne pas surcharger les APIs
3. **Qualité > Quantité** : Préférer moins mais mieux
4. **Test d'abord** : Toujours tester avec `--dry-run`

---

## 🎉 Félicitations !

Vous avez maintenant un système de prospection B2B professionnel pour **moins de 10€/mois** !

**Prêt à commencer ?**
```bash
make pipeline-free
```
