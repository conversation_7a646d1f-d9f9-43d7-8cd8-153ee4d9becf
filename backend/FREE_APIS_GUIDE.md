# 🆓 Guide des APIs Gratuites - Prospection B2B

## 📋 Vue d'ensemble

Ce système utilise **uniquement des APIs gratuites** (sauf OpenAI pour la génération d'emails). Voici la liste complète des sources utilisées et leurs limitations.

## 🎯 Sources de Leads Gratuites

### 1. **API Sirene v3** ⭐⭐⭐⭐⭐
- **URL** : https://api.insee.fr/entreprises/sirene/V3
- **Gratuit** : ✅ Complètement gratuit
- **Limite** : 7 requêtes/seconde
- **Données** : SIREN, SIRET, nom, adresse, NAF, effectifs
- **Qualité** : Excellente (données officielles INSEE)

```python
# Utilisation
async for company in sirene_api.search_companies(
    naf_codes=["6201Z", "6202A"],
    regions=["11"],  # Île-de-France
    max_results=100
):
    print(f"{company.name} - {company.city}")
```

### 2. **API Entreprise (gouv.fr)** ⭐⭐⭐⭐
- **URL** : https://entreprise.api.gouv.fr/
- **Gratuit** : ✅ Usage personnel autorisé
- **Limite** : Raisonnable (non spécifiée)
- **Données** : Informations légales, dirigeants
- **Qualité** : Très bonne (données officielles)

### 3. **OpenDataSoft** ⭐⭐⭐⭐
- **URL** : https://public.opendatasoft.com/
- **Gratuit** : ✅ Complètement gratuit
- **Limite** : Généreuse
- **Données** : Base Sirene publique
- **Qualité** : Bonne (même source que Sirene)

### 4. **Recherche d'emails intelligente** ⭐⭐⭐
- **Méthode** : Scraping web + patterns + validation DNS
- **Gratuit** : ✅ Complètement gratuit
- **Limite** : Dépend des sites web
- **Données** : Emails professionnels
- **Qualité** : Variable selon les sites

## 🔍 Sources Alternatives (Scraping)

### 5. **Pages Jaunes** ⭐⭐⭐
- **Méthode** : Scraping respectueux
- **Gratuit** : ✅ Mais à utiliser avec modération
- **Limite** : 0.5 requête/seconde (très conservateur)
- **Données** : Nom, adresse, téléphone, site web
- **Qualité** : Bonne pour les entreprises locales

### 6. **Societe.com** ⭐⭐
- **Méthode** : Scraping léger
- **Gratuit** : ✅ Données publiques
- **Limite** : 1 requête/seconde
- **Données** : SIREN, nom, adresse, NAF
- **Qualité** : Correcte

## 📧 Recherche d'Emails Gratuite

### Méthodes utilisées :

#### 1. **Scraping Web Intelligent**
```python
# Pages analysées automatiquement
pages = [
    "/",           # Accueil
    "/contact",    # Contact
    "/about",      # À propos
    "/equipe",     # Équipe
    "/mentions-legales"  # Mentions légales
]
```

#### 2. **Génération de Patterns**
```python
# Emails génériques (confiance: 70%)
patterns_generiques = [
    "contact@{domain}",
    "info@{domain}",
    "commercial@{domain}"
]

# Emails dirigeants (confiance: 60%)
patterns_dirigeants = [
    "direction@{domain}",
    "president@{domain}",
    "ceo@{domain}"
]

# Emails techniques (confiance: 50%)
patterns_tech = [
    "tech@{domain}",
    "it@{domain}",
    "webmaster@{domain}"
]
```

#### 3. **Validation DNS (MX Records)**
- Vérification que le domaine accepte les emails
- Augmente la confiance de +20 points
- Utilise `dnspython` (gratuit)

## ⚙️ Configuration Optimisée

### Variables d'environnement (.env)
```env
# OBLIGATOIRE - Seule API payante
OPENAI_API_KEY=your_openai_key_here

# SMTP pour l'envoi (Gmail gratuit jusqu'à 500/jour)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=Votre Nom

# Optionnel - Rate limiting
EMAIL_RATE_LIMIT=5
```

### Configuration (config.yaml)
```yaml
# Sources gratuites activées
free_sources:
  enabled:
    - "sirene"
    - "opendatasoft" 
    - "api_entreprise"
  
  # Sources alternatives (scraping modéré)
  alternative:
    - "pages_jaunes"
    - "societe_com"

# Rate limits conservateurs
rate_limits:
  sirene_api: 7
  pages_jaunes: 0.5
  societe_com: 1
  email_sending: 5
```

## 🚀 Utilisation Pratique

### Pipeline complet gratuit
```bash
# 1. Génération de leads (100% gratuit)
python cli.py generate-leads --max-results 50

# 2. Analyse des entreprises (gratuit)
python cli.py analyze --concurrent 3

# 3. Génération d'emails (OpenAI payant ~$0.01/email)
python cli.py generate-emails --concurrent 2

# 4. Test d'envoi (gratuit)
python cli.py send --dry-run

# 5. Envoi réel (Gmail gratuit jusqu'à 500/jour)
python cli.py send --real
```

### Commandes spécialisées
```bash
# Recherche par région spécifique
python cli.py generate-leads --source sirene --max-results 100

# Analyse avec sources alternatives
python cli.py analyze --use-alternative-sources

# Statistiques d'utilisation
python cli.py stats --days 30
```

## 📊 Performances et Limites

### Vitesses typiques (APIs gratuites)
- **Sirene** : ~50 leads/minute
- **Enrichissement** : ~10 entreprises/minute
- **Recherche emails** : ~5 entreprises/minute
- **Génération emails** : ~20 emails/minute (OpenAI)
- **Envoi emails** : 5 emails/minute (configurable)

### Quotas et limites
| Source | Limite quotidienne | Limite par seconde |
|--------|-------------------|-------------------|
| Sirene | Illimitée | 7 req/s |
| API Entreprise | ~1000 req | Raisonnable |
| OpenDataSoft | Très élevée | Généreuse |
| Pages Jaunes | ~100 req | 0.5 req/s |
| Gmail SMTP | 500 emails | 5 emails/min |

## 💡 Optimisations Recommandées

### 1. **Ciblage Précis**
```yaml
# Filtres optimisés dans config.yaml
search_filters:
  sirene:
    naf_codes: ["6201Z", "6202A"]  # Codes spécifiques
    min_employees: 5
    max_employees: 100
    regions: ["11", "84"]  # Régions ciblées
```

### 2. **Cache Intelligent**
- Déduplication automatique par SIREN/domaine
- Cache des domaines vérifiés
- Évite les requêtes redondantes

### 3. **Rate Limiting Respectueux**
```python
# Configuration conservative
rate_limits = {
    "sirene_api": 5,      # Au lieu de 7 (marge de sécurité)
    "pages_jaunes": 0.3,  # Très conservateur
    "email_sending": 3    # Éviter le spam
}
```

## 🔒 Bonnes Pratiques

### Respect des APIs
1. **Rate limiting strict** : Respecter les limites
2. **User-Agent approprié** : Identifier votre application
3. **Gestion d'erreurs** : Retry avec backoff exponentiel
4. **Cache local** : Éviter les requêtes inutiles

### Conformité légale
1. **Usage personnel** : Système non conforme RGPD
2. **Données publiques** : Utiliser uniquement des sources publiques
3. **Opt-out** : Respecter les demandes de suppression
4. **Modération** : Pas de spam, envois ciblés

## 🆘 Dépannage

### Problèmes courants

#### "API Sirene inaccessible"
```bash
# Vérifier la connectivité
curl "https://api.insee.fr/entreprises/sirene/V3/siren?q=siren:*********"
```

#### "Pas d'emails trouvés"
```python
# Vérifier manuellement
python -c "
from lead_generator.email_finder_free import SmartEmailFinder
import asyncio
finder = SmartEmailFinder()
emails = asyncio.run(finder.find_emails_for_company(company))
print(emails)
"
```

#### "Rate limit atteint"
- Réduire les valeurs dans `config.yaml`
- Ajouter des délais avec `time.sleep()`
- Utiliser moins de sources simultanément

### Logs utiles
```bash
# Voir les détails des requêtes
tail -f logs/prospection.log | grep "API"

# Statistiques d'utilisation
python cli.py stats --days 7
```

## 🎯 Résultats Attendus

### Avec 100% APIs gratuites
- **50-100 leads/heure** (selon filtres)
- **Taux d'emails trouvés** : 60-80%
- **Coût total** : ~$1-2 (OpenAI uniquement)
- **Qualité** : Bonne à très bonne

### ROI typique
- **Coût** : <$5 pour 100 prospects qualifiés
- **Temps** : 2-3 heures pour pipeline complet
- **Taux de réponse** : 5-15% (selon qualité du ciblage)

## 🔄 Évolutions Futures

### APIs gratuites à explorer
1. **Data.gouv.fr** : Plus de datasets publics
2. **API Géo** : Enrichissement géographique
3. **Wikidata** : Informations entreprises publiques
4. **GitHub API** : Entreprises tech avec repos publics

### Améliorations prévues
1. **Cache Redis** : Performance améliorée
2. **ML scoring** : Meilleur lead scoring
3. **Validation emails** : Tests SMTP légers
4. **Export CRM** : Intégration directe
