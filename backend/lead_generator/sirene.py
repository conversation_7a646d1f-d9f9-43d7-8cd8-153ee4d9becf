"""
API Sirene v3 pour la génération de leads français
"""

import asyncio
import logging
from typing import Any, AsyncGenerator, Dict, List, Optional

import httpx

from models import Company, CompanySize, LeadSource

logger = logging.getLogger(__name__)


class SireneAPI:
    """Client pour l'API Sirene v3"""

    BASE_URL = "https://api.insee.fr/entreprises/sirene/V3.11"

    def __init__(self, rate_limit: int = 7):
        """
        Args:
            rate_limit: Nombre de requêtes par seconde (limite API Sirene)
        """
        self.rate_limit = rate_limit
        self._semaphore = asyncio.Semaphore(rate_limit)
        self._last_request_time = 0

    async def _make_request(self, endpoint: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Effectue une requête à l'API Sirene avec rate limiting"""
        async with self._semaphore:
            # Rate limiting
            current_time = asyncio.get_event_loop().time()
            time_since_last = current_time - self._last_request_time
            if time_since_last < (1.0 / self.rate_limit):
                await asyncio.sleep((1.0 / self.rate_limit) - time_since_last)

            self._last_request_time = asyncio.get_event_loop().time()

            async with httpx.AsyncClient() as client:
                try:
                    response = await client.get(f"{self.BASE_URL}/{endpoint}", params=params, timeout=30.0)
                    response.raise_for_status()
                    return response.json()
                except httpx.HTTPError as e:
                    logger.error(f"Erreur API Sirene: {e}")
                    raise

    def _map_company_size(self, employees: Optional[str]) -> Optional[CompanySize]:
        """Mappe les effectifs Sirene vers notre enum CompanySize"""
        if not employees:
            return None

        # Codes effectifs Sirene
        size_mapping = {
            "00": None,  # Non renseigné
            "01": CompanySize.MICRO,  # 0 salarié
            "02": CompanySize.MICRO,  # 1 ou 2 salariés
            "03": CompanySize.MICRO,  # 3 à 5 salariés
            "11": CompanySize.MICRO,  # 6 à 9 salariés
            "12": CompanySize.SMALL,  # 10 à 19 salariés
            "21": CompanySize.SMALL,  # 20 à 49 salariés
            "22": CompanySize.MEDIUM,  # 50 à 99 salariés
            "31": CompanySize.MEDIUM,  # 100 à 199 salariés
            "32": CompanySize.MEDIUM,  # 200 à 249 salariés
            "41": CompanySize.LARGE,  # 250 à 499 salariés
            "42": CompanySize.LARGE,  # 500 à 999 salariés
            "51": CompanySize.LARGE,  # 1000 à 1999 salariés
            "52": CompanySize.LARGE,  # 2000 à 4999 salariés
            "53": CompanySize.LARGE,  # 5000 à 9999 salariés
            "54": CompanySize.LARGE,  # 10000 salariés et plus
        }
        return size_mapping.get(employees)

    def _parse_company(self, etablissement: Dict[str, Any]) -> Company:
        """Parse les données Sirene en objet Company"""
        unite_legale = etablissement.get("uniteLegale", {})
        adresse = etablissement.get("adresseEtablissement", {})

        return Company(
            siren=unite_legale.get("siren"),
            siret=etablissement.get("siret"),
            name=unite_legale.get("denominationUniteLegale")
            or etablissement.get("denominationUsuelleEtablissement")
            or f"{unite_legale.get('prenom1UniteLegale', '')} {unite_legale.get('nomUniteLegale', '')}".strip(),
            legal_name=unite_legale.get("denominationUniteLegale"),
            # Adresse
            address=f"{adresse.get('numeroVoieEtablissement', '')} {adresse.get('typeVoieEtablissement', '')} {adresse.get('libelleVoieEtablissement', '')}".strip(),
            city=adresse.get("libelleCommuneEtablissement"),
            postal_code=adresse.get("codePostalEtablissement"),
            region=adresse.get("libelleRegionEtablissement"),
            # Informations business
            naf_code=etablissement.get("activitePrincipaleEtablissement"),
            employees=None,  # Pas directement disponible dans l'API établissements
            size=self._map_company_size(unite_legale.get("trancheEffectifsUniteLegale")),
            source=LeadSource.SIRENE,
        )

    async def search_companies(
        self, naf_codes: List[str], regions: Optional[List[str]] = None, min_employees: Optional[int] = None, limit: int = 100
    ) -> AsyncGenerator[Company, None]:
        """
        Recherche d'entreprises selon les critères

        Args:
            naf_codes: Codes NAF/APE à cibler
            regions: Codes région INSEE (optionnel)
            min_employees: Effectif minimum
            max_employees: Effectif maximum
            limit: Nombre maximum de résultats par requête
        """
        for naf_code in naf_codes:
            cursor = "*"
            total_processed = 0

            while cursor and total_processed < 1000:  # Limite de sécurité
                # Construction de la requête avec syntaxe V3.11
                query_parts = [f"periode(activitePrincipaleEtablissement:{naf_code})"]

                # Filtres géographiques
                if regions:
                    region_filter = " OR ".join([f"codeRegionEtablissement:{r}" for r in regions])
                    query_parts.append(f"({region_filter})")

                # Filtre sur les établissements actifs
                query_parts.append("periode(etatAdministratifEtablissement:A)")

                params = {
                    "q": " AND ".join(query_parts),
                    "nombre": min(limit, 1000 - total_processed),
                }

                if cursor != "*":
                    params["curseur"] = cursor

                try:
                    data = await self._make_request("siret", params)
                    etablissements = data.get("etablissements", [])

                    if not etablissements:
                        break

                    for etab in etablissements:
                        try:
                            company = self._parse_company(etab)

                            # Filtrage post-traitement (effectifs)
                            if min_employees and company.size:
                                size_min_map = {
                                    CompanySize.MICRO: 1,
                                    CompanySize.SMALL: 10,
                                    CompanySize.MEDIUM: 50,
                                    CompanySize.LARGE: 250,
                                }
                                if size_min_map.get(company.size, 0) < min_employees:
                                    continue

                            yield company
                            total_processed += 1

                        except Exception as e:
                            logger.warning(f"Erreur parsing entreprise: {e}")
                            continue

                    # Pagination
                    cursor = data.get("header", {}).get("curseurSuivant")
                    if not cursor:
                        break

                except Exception as e:
                    logger.error(f"Erreur recherche NAF {naf_code}: {e}")
                    break

    async def get_company_details(self, siren: str) -> Optional[Company]:
        """Récupère les détails d'une entreprise par SIREN"""
        try:
            data = await self._make_request("siren", {"q": f"siren:{siren}"})
            unites_legales = data.get("unitesLegales", [])

            if not unites_legales:
                return None

            unite_legale = unites_legales[0]

            # Récupérer l'établissement principal
            siret_siege = unite_legale.get("siretUniteLegale")
            if siret_siege:
                etab_data = await self._make_request("siret", {"q": f"siret:{siret_siege}"})
                etablissements = etab_data.get("etablissements", [])
                if etablissements:
                    return self._parse_company(etablissements[0])

            return None

        except Exception as e:
            logger.error(f"Erreur récupération détails SIREN {siren}: {e}")
            return None
