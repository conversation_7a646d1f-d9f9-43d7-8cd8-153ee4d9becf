"""
Recherche d'emails gratuite et intelligente
"""

import asyncio
import logging
import re
from typing import Any, Dict, List, Optional, Tuple
from urllib.parse import urljoin, urlparse

import dns.resolver
import httpx
from bs4 import BeautifulSoup

from models import Company

logger = logging.getLogger(__name__)


class SmartEmailFinder:
    """Recherche d'emails intelligente et gratuite"""

    def __init__(self):
        self.common_prefixes = [
            "contact",
            "info",
            "hello",
            "bonjour",
            "commercial",
            "vente",
            "direction",
            "admin",
            "support",
            "service",
            "accueil",
        ]

        self.executive_prefixes = ["direction", "directeur", "ceo", "president", "pdg", "gerant", "manager", "responsable"]

        self.tech_prefixes = ["tech", "it", "informatique", "dev", "technique", "cto", "dsi", "webmaster"]

    async def find_emails_for_company(self, company: Company) -> List[Dict[str, Any]]:
        """
        Trouve des emails pour une entreprise

        Args:
            company: Entreprise cible

        Returns:
            Liste d'emails avec métadonnées
        """
        emails_found = []

        if not company.domain:
            return emails_found

        try:
            # 1. Vérifier que le domaine existe
            if not await self._verify_domain_exists(company.domain):
                logger.warning(f"Domaine {company.domain} inaccessible")
                return emails_found

            # 2. Scraper le site web pour trouver des emails
            scraped_emails = await self._scrape_website_emails(company.domain)
            emails_found.extend(scraped_emails)

            # 3. Générer des emails probables
            generated_emails = await self._generate_probable_emails(company)
            emails_found.extend(generated_emails)

            # 4. Vérifier les MX records pour validation
            valid_emails = await self._validate_emails_mx(emails_found, company.domain)

            return valid_emails

        except Exception as e:
            logger.error(f"Erreur recherche emails pour {company.name}: {e}")
            return emails_found

    async def _verify_domain_exists(self, domain: str) -> bool:
        """Vérifie qu'un domaine existe et répond"""
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                for protocol in ["https://", "http://"]:
                    try:
                        response = await client.head(f"{protocol}{domain}")
                        if response.status_code < 500:
                            return True
                    except:
                        continue
            return False
        except:
            return False

    async def _scrape_website_emails(self, domain: str) -> List[Dict[str, Any]]:
        """Scrape le site web pour trouver des emails"""
        emails = []

        try:
            # Pages à scraper
            pages_to_check = [
                "",  # Page d'accueil
                "/contact",
                "/contact-us",
                "/nous-contacter",
                "/about",
                "/a-propos",
                "/equipe",
                "/team",
                "/mentions-legales",
            ]

            async with httpx.AsyncClient(timeout=15.0) as client:
                for page in pages_to_check:
                    try:
                        url = f"https://{domain}{page}"
                        response = await client.get(url)

                        if response.status_code == 200:
                            soup = BeautifulSoup(response.text, "html.parser")

                            # Chercher des emails dans le texte
                            text_content = soup.get_text()
                            found_emails = re.findall(r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b", text_content)

                            # Chercher des emails dans les liens mailto
                            mailto_links = soup.find_all("a", href=re.compile(r"^mailto:"))
                            for link in mailto_links:
                                email = link["href"].replace("mailto:", "").split("?")[0]
                                found_emails.append(email)

                            # Filtrer et ajouter les emails du bon domaine
                            for email in found_emails:
                                if domain.lower() in email.lower():
                                    emails.append(
                                        {
                                            "email": email.lower(),
                                            "source": "website_scraping",
                                            "page": page or "homepage",
                                            "confidence": 90,
                                        }
                                    )

                        # Délai entre les requêtes
                        await asyncio.sleep(0.5)

                    except Exception as e:
                        logger.debug(f"Erreur scraping {domain}{page}: {e}")
                        continue

        except Exception as e:
            logger.error(f"Erreur scraping général {domain}: {e}")

        return emails

    async def _generate_probable_emails(self, company: Company) -> List[Dict[str, Any]]:
        """Génère des emails probables basés sur des patterns"""
        emails = []

        if not company.domain:
            return emails

        domain = company.domain.lower()

        # Emails génériques
        for prefix in self.common_prefixes:
            emails.append({"email": f"{prefix}@{domain}", "source": "pattern_generation", "type": "generic", "confidence": 70})

        # Emails dirigeants
        for prefix in self.executive_prefixes:
            emails.append(
                {"email": f"{prefix}@{domain}", "source": "pattern_generation", "type": "executive", "confidence": 60}
            )

        # Emails techniques
        for prefix in self.tech_prefixes:
            emails.append(
                {"email": f"{prefix}@{domain}", "source": "pattern_generation", "type": "technical", "confidence": 50}
            )

        return emails

    async def _validate_emails_mx(self, emails: List[Dict[str, Any]], domain: str) -> List[Dict[str, Any]]:
        """Valide les emails en vérifiant les MX records"""
        valid_emails = []

        try:
            # Vérifier les MX records du domaine
            mx_records = await self._get_mx_records(domain)

            if not mx_records:
                logger.warning(f"Pas de MX records pour {domain}")
                # Retourner quand même les emails avec confiance réduite
                for email in emails:
                    email["confidence"] = max(20, email.get("confidence", 50) - 30)
                    valid_emails.append(email)
                return valid_emails

            # Si MX records existent, augmenter la confiance
            for email in emails:
                email["mx_valid"] = True
                email["confidence"] = min(95, email.get("confidence", 50) + 20)
                valid_emails.append(email)

        except Exception as e:
            logger.error(f"Erreur validation MX pour {domain}: {e}")
            # En cas d'erreur, retourner les emails sans validation
            valid_emails = emails

        # Trier par confiance décroissante
        valid_emails.sort(key=lambda x: x.get("confidence", 0), reverse=True)

        # Déduplication
        seen_emails = set()
        unique_emails = []
        for email_data in valid_emails:
            email = email_data["email"]
            if email not in seen_emails:
                seen_emails.add(email)
                unique_emails.append(email_data)

        return unique_emails

    async def _get_mx_records(self, domain: str) -> List[str]:
        """Récupère les MX records d'un domaine"""
        try:
            # Utiliser dnspython pour résoudre les MX records
            mx_records = []

            try:
                answers = dns.resolver.resolve(domain, "MX")
                for rdata in answers:
                    mx_records.append(str(rdata.exchange))
            except dns.resolver.NXDOMAIN:
                logger.debug(f"Domaine {domain} n'existe pas")
            except dns.resolver.NoAnswer:
                logger.debug(f"Pas de MX records pour {domain}")
            except Exception as e:
                logger.debug(f"Erreur DNS pour {domain}: {e}")

            return mx_records

        except Exception as e:
            logger.error(f"Erreur récupération MX {domain}: {e}")
            return []

    def get_best_emails(self, emails: List[Dict[str, Any]], max_emails: int = 3) -> List[str]:
        """
        Retourne les meilleurs emails triés par pertinence

        Args:
            emails: Liste d'emails avec métadonnées
            max_emails: Nombre maximum d'emails à retourner

        Returns:
            Liste des meilleurs emails
        """
        if not emails:
            return []

        # Trier par confiance et type
        def email_score(email_data):
            confidence = email_data.get("confidence", 0)
            source = email_data.get("source", "")
            email_type = email_data.get("type", "")

            # Bonus pour les emails scrapés (plus fiables)
            if source == "website_scraping":
                confidence += 20

            # Bonus pour les emails génériques (plus susceptibles d'être lus)
            if email_type == "generic":
                confidence += 10

            return confidence

        # Trier et prendre les meilleurs
        sorted_emails = sorted(emails, key=email_score, reverse=True)
        best_emails = [email_data["email"] for email_data in sorted_emails[:max_emails]]

        return best_emails

    async def find_best_email_for_company(self, company: Company) -> Optional[str]:
        """
        Trouve le meilleur email pour une entreprise

        Args:
            company: Entreprise cible

        Returns:
            Meilleur email trouvé ou None
        """
        emails_data = await self.find_emails_for_company(company)
        best_emails = self.get_best_emails(emails_data, max_emails=1)

        return best_emails[0] if best_emails else None


class EmailValidator:
    """Validateur d'emails gratuit"""

    @staticmethod
    def is_valid_format(email: str) -> bool:
        """Vérifie le format d'un email"""
        pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
        return bool(re.match(pattern, email))

    @staticmethod
    def is_business_email(email: str) -> bool:
        """Vérifie si c'est un email professionnel"""
        personal_domains = [
            "gmail.com",
            "yahoo.fr",
            "yahoo.com",
            "hotmail.com",
            "hotmail.fr",
            "outlook.com",
            "outlook.fr",
            "free.fr",
            "orange.fr",
            "wanadoo.fr",
            "laposte.net",
            "sfr.fr",
        ]

        domain = email.split("@")[-1].lower()
        return domain not in personal_domains

    @staticmethod
    def get_email_confidence(email: str, company_domain: str) -> int:
        """Calcule un score de confiance pour un email"""
        if not EmailValidator.is_valid_format(email):
            return 0

        confidence = 50  # Base

        # Bonus si même domaine que l'entreprise
        if company_domain and company_domain.lower() in email.lower():
            confidence += 30

        # Bonus si email professionnel
        if EmailValidator.is_business_email(email):
            confidence += 20

        # Bonus pour certains préfixes
        email_prefix = email.split("@")[0].lower()
        if email_prefix in ["contact", "info", "commercial"]:
            confidence += 15
        elif email_prefix in ["direction", "ceo", "president"]:
            confidence += 10

        return min(100, confidence)
