"""
API gratuite utilisant les fichiers CSV de data.gouv.fr
Alternative à l'API Sirene qui nécessite maintenant une authentification
"""

import asyncio
import csv
import io
import logging
import zipfile
from datetime import datetime
from typing import Any, AsyncGenerator, Dict, List, Optional

import httpx

from models import Company, CompanySize, LeadSource

logger = logging.getLogger(__name__)


class DataGouvSireneAPI:
    """
    API gratuite utilisant les fichiers CSV Sirene de data.gouv.fr
    Alternative à l'API Sirene officielle qui nécessite maintenant une authentification
    """

    # URLs des fichiers CSV sur data.gouv.fr (mis à jour mensuellement)
    ETABLISSEMENTS_URL = "https://www.data.gouv.fr/fr/datasets/r/0651fb76-bcf3-4f6a-a38d-bc04fa708576"
    UNITES_LEGALES_URL = "https://www.data.gouv.fr/fr/datasets/r/825f4199-cadd-486c-ac46-a65a8ea1a047"

    def __init__(self, cache_dir: str = "cache"):
        """
        Args:
            cache_dir: Répertoire pour mettre en cache les fichiers téléchargés
        """
        self.cache_dir = cache_dir
        self._etablissements_cache = None
        self._unites_legales_cache = None
        self._last_download = None

    async def _download_and_extract_csv(self, url: str, filename: str) -> Optional[str]:
        """
        Télécharge et extrait un fichier CSV depuis un ZIP

        Args:
            url: URL du fichier ZIP
            filename: Nom du fichier CSV à extraire

        Returns:
            Contenu CSV ou None si erreur
        """
        try:
            logger.info(f"Téléchargement de {filename} depuis data.gouv.fr...")

            async with httpx.AsyncClient(timeout=300.0) as client:
                response = await client.get(url)

                if response.status_code == 200:
                    # Extraire le CSV du ZIP
                    with zipfile.ZipFile(io.BytesIO(response.content)) as zip_file:
                        # Trouver le fichier CSV dans le ZIP
                        csv_files = [f for f in zip_file.namelist() if f.endswith(".csv")]

                        if csv_files:
                            csv_filename = csv_files[0]  # Prendre le premier CSV
                            with zip_file.open(csv_filename) as csv_file:
                                content = csv_file.read().decode("utf-8")
                                logger.info(f"✅ {filename} téléchargé avec succès ({len(content)} caractères)")
                                return content
                        else:
                            logger.error(f"Aucun fichier CSV trouvé dans {filename}")
                            return None
                else:
                    logger.error(f"Erreur téléchargement {filename}: {response.status_code}")
                    return None

        except Exception as e:
            logger.error(f"Erreur téléchargement {filename}: {e}")
            return None

    async def _load_etablissements_sample(self, max_lines: int = 10000) -> List[Dict[str, str]]:
        """
        Charge un échantillon du fichier des établissements

        Args:
            max_lines: Nombre maximum de lignes à charger

        Returns:
            Liste des établissements
        """
        if self._etablissements_cache is not None:
            return self._etablissements_cache

        try:
            csv_content = await self._download_and_extract_csv(self.ETABLISSEMENTS_URL, "etablissements")

            if not csv_content:
                return []

            # Parser le CSV
            etablissements = []
            csv_reader = csv.DictReader(io.StringIO(csv_content))

            for i, row in enumerate(csv_reader):
                if i >= max_lines:
                    break
                etablissements.append(row)

            self._etablissements_cache = etablissements
            logger.info(f"✅ {len(etablissements)} établissements chargés en cache")
            return etablissements

        except Exception as e:
            logger.error(f"Erreur chargement établissements: {e}")
            return []

    def _map_company_size(self, effectif_code: Optional[str]) -> Optional[CompanySize]:
        """Mappe les codes effectifs Sirene vers CompanySize"""
        if not effectif_code:
            return None

        # Codes effectifs Sirene
        size_mapping = {
            "00": None,  # Non renseigné
            "01": CompanySize.MICRO,  # 1 ou 2 salariés
            "02": CompanySize.MICRO,  # 3 à 5 salariés
            "03": CompanySize.MICRO,  # 6 à 9 salariés
            "11": CompanySize.SMALL,  # 10 à 19 salariés
            "12": CompanySize.SMALL,  # 20 à 49 salariés
            "21": CompanySize.MEDIUM,  # 50 à 99 salariés
            "22": CompanySize.MEDIUM,  # 100 à 199 salariés
            "31": CompanySize.MEDIUM,  # 200 à 249 salariés
            "32": CompanySize.LARGE,  # 250 à 499 salariés
            "41": CompanySize.LARGE,  # 500 à 999 salariés
            "42": CompanySize.LARGE,  # 1000 à 1999 salariés
            "51": CompanySize.LARGE,  # 2000 à 4999 salariés
            "52": CompanySize.LARGE,  # 5000 à 9999 salariés
            "53": CompanySize.LARGE,  # 10000 salariés et plus
        }

        return size_mapping.get(effectif_code)

    def _parse_etablissement(self, row: Dict[str, str]) -> Optional[Company]:
        """
        Parse une ligne du fichier établissements en objet Company

        Args:
            row: Ligne CSV sous forme de dictionnaire

        Returns:
            Objet Company ou None si erreur
        """
        try:
            # Vérifier que l'établissement est actif
            etat_admin = row.get("etatAdministratifEtablissement", "")
            if etat_admin != "A":  # A = Actif
                return None

            # Construire l'adresse
            address_parts = []
            if row.get("numeroVoieEtablissement"):
                address_parts.append(row["numeroVoieEtablissement"])
            if row.get("typeVoieEtablissement"):
                address_parts.append(row["typeVoieEtablissement"])
            if row.get("libelleVoieEtablissement"):
                address_parts.append(row["libelleVoieEtablissement"])

            address = " ".join(address_parts) if address_parts else None

            # Mapper la taille de l'entreprise
            effectif_code = row.get("trancheEffectifsEtablissement")
            company_size = self._map_company_size(effectif_code)

            # Estimer le nombre d'employés depuis le code effectif
            employees = None
            if effectif_code:
                employees_mapping = {
                    "01": 1,
                    "02": 4,
                    "03": 7,
                    "11": 15,
                    "12": 35,
                    "21": 75,
                    "22": 150,
                    "31": 225,
                    "32": 375,
                    "41": 750,
                    "42": 1500,
                    "51": 3500,
                    "52": 7500,
                    "53": 15000,
                }
                employees = employees_mapping.get(effectif_code)

            company = Company(
                siren=row.get("siren"),
                siret=row.get("siret"),
                name=row.get("denominationUsuelleEtablissement") or row.get("enseigne1Etablissement", ""),
                legal_name=row.get("denominationUsuelleEtablissement"),
                address=address,
                city=row.get("libelleCommuneEtablissement"),
                postal_code=row.get("codePostalEtablissement"),
                region=row.get("libelleRegionEtablissement"),
                country="FR",
                naf_code=row.get("activitePrincipaleEtablissement"),
                employees=employees,
                size=company_size,
                source=LeadSource.SIRENE,
            )

            return company

        except Exception as e:
            logger.debug(f"Erreur parsing établissement: {e}")
            return None

    async def search_companies(
        self,
        naf_codes: Optional[List[str]] = None,
        regions: Optional[List[str]] = None,
        departments: Optional[List[str]] = None,
        min_employees: Optional[int] = None,
        max_employees: Optional[int] = None,
        limit: int = 100,
    ) -> AsyncGenerator[Company, None]:
        """
        Recherche d'entreprises dans les données Sirene

        Args:
            naf_codes: Codes NAF/APE à filtrer
            regions: Codes région à filtrer
            departments: Codes département à filtrer
            min_employees: Nombre minimum d'employés
            max_employees: Nombre maximum d'employés
            limit: Nombre maximum de résultats

        Yields:
            Objets Company correspondant aux critères
        """
        logger.info(f"Recherche d'entreprises avec filtres: NAF={naf_codes}, régions={regions}")

        # Charger les données
        etablissements = await self._load_etablissements_sample(max_lines=50000)  # Échantillon plus large

        if not etablissements:
            logger.warning("Aucune donnée d'établissement disponible")
            return

        count = 0
        processed = 0

        for row in etablissements:
            processed += 1

            # Filtrer par code NAF
            if naf_codes:
                naf_etab = row.get("activitePrincipaleEtablissement", "")
                if not any(naf in naf_etab for naf in naf_codes):
                    continue

            # Filtrer par région (code)
            if regions:
                region_code = row.get("codeRegionEtablissement", "")
                if region_code not in regions:
                    continue

            # Filtrer par département
            if departments:
                dept_code = row.get("codeCommuneEtablissement", "")[:2] if row.get("codeCommuneEtablissement") else ""
                if dept_code not in departments:
                    continue

            # Parser l'établissement
            company = self._parse_etablissement(row)
            if not company:
                continue

            # Filtrer par nombre d'employés
            if min_employees and (not company.employees or company.employees < min_employees):
                continue
            if max_employees and (not company.employees or company.employees > max_employees):
                continue

            yield company
            count += 1

            if count >= limit:
                break

            # Petit délai pour éviter de bloquer
            if count % 100 == 0:
                await asyncio.sleep(0.1)

        logger.info(f"Recherche terminée: {count} entreprises trouvées sur {processed} traitées")

    async def get_company_by_siren(self, siren: str) -> Optional[Company]:
        """
        Récupère une entreprise par son SIREN

        Args:
            siren: SIREN de l'entreprise

        Returns:
            Objet Company ou None si non trouvé
        """
        etablissements = await self._load_etablissements_sample()

        for row in etablissements:
            if row.get("siren") == siren:
                return self._parse_etablissement(row)

        return None

    async def get_statistics(self) -> Dict[str, Any]:
        """
        Récupère des statistiques sur les données disponibles

        Returns:
            Statistiques des données
        """
        etablissements = await self._load_etablissements_sample()

        if not etablissements:
            return {"error": "Aucune donnée disponible"}

        # Analyser les données
        total = len(etablissements)
        actifs = len([e for e in etablissements if e.get("etatAdministratifEtablissement") == "A"])

        # Compter par NAF
        naf_counts = {}
        for etab in etablissements:
            naf = etab.get("activitePrincipaleEtablissement", "Inconnu")[:4]  # 4 premiers caractères
            naf_counts[naf] = naf_counts.get(naf, 0) + 1

        # Top 10 des NAF
        top_naf = sorted(naf_counts.items(), key=lambda x: x[1], reverse=True)[:10]

        return {
            "total_etablissements": total,
            "etablissements_actifs": actifs,
            "taux_activite": (actifs / total * 100) if total > 0 else 0,
            "top_naf_codes": top_naf,
            "source": "data.gouv.fr CSV files",
            "last_update": datetime.now().isoformat(),
        }
