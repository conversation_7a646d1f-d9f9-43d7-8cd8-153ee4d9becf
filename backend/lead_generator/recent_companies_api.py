"""
API pour récupérer les entreprises récemment créées
Utilise les données d'immatriculations du Registre du Commerce et des Sociétés (RCS)
"""

import asyncio
import json
import logging
import os
from datetime import datetime, timedelta
from typing import Any, AsyncGenerator, Dict, List, Optional

import httpx

from models import Company, CompanySize, LeadSource

logger = logging.getLogger(__name__)


class RecentCompaniesAPI:
    """
    API pour récupérer les entreprises récemment créées
    Utilise plusieurs sources publiques d'immatriculations
    """

    # URLs des APIs publiques d'immatriculations
    BODACC_API = "https://www.bodacc.fr/api/explore/v2.1/catalog/datasets/annonces-commerciales/records"
    # Infogreffe n'a pas d'API publique gratuite, on se concentre sur BODACC

    def __init__(self, rate_limit: int = 10):
        """
        Args:
            rate_limit: Nombre de requêtes par minute
        """
        self.rate_limit = rate_limit
        self._semaphore = asyncio.Semaphore(rate_limit)
        self._last_request_time = 0

    async def _make_request(self, url: str, params: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Effectue une requête HTTP avec rate limiting"""
        async with self._semaphore:
            # Rate limiting
            current_time = asyncio.get_event_loop().time()
            time_since_last = current_time - self._last_request_time
            if time_since_last < (60.0 / self.rate_limit):
                await asyncio.sleep((60.0 / self.rate_limit) - time_since_last)

            self._last_request_time = asyncio.get_event_loop().time()

            async with httpx.AsyncClient() as client:
                try:
                    response = await client.get(url, params=params, timeout=30.0)

                    if response.status_code == 200:
                        return response.json()
                    else:
                        logger.warning(f"Erreur API {url}: {response.status_code}")
                        return None

                except httpx.HTTPError as e:
                    logger.error(f"Erreur HTTP {url}: {e}")
                    return None

    def _parse_infogreffe_company(self, record: Dict[str, Any]) -> Optional[Company]:
        """Parse une entreprise depuis les données Infogreffe"""
        try:
            fields = record.get("fields", {})

            # Informations de base
            name = fields.get("denomination") or fields.get("nom_commercial")
            if not name:
                return None

            # Adresse
            address_parts = []
            if fields.get("adresse_ligne_1"):
                address_parts.append(fields["adresse_ligne_1"])
            if fields.get("adresse_ligne_2"):
                address_parts.append(fields["adresse_ligne_2"])

            address = " ".join(address_parts) if address_parts else None

            # Date de création
            created_at = None
            if fields.get("date_immatriculation"):
                try:
                    created_at = datetime.fromisoformat(fields["date_immatriculation"].replace("Z", "+00:00"))
                except:
                    pass

            return Company(
                siren=fields.get("siren"),
                siret=fields.get("siret"),
                name=name,
                legal_name=fields.get("denomination"),
                address=address,
                city=fields.get("ville"),
                postal_code=fields.get("code_postal"),
                region=fields.get("region"),
                country="FR",
                naf_code=fields.get("code_ape"),
                naf_label=fields.get("libelle_ape"),
                source=LeadSource.RCS,
                created_at=created_at,
            )

        except Exception as e:
            logger.debug(f"Erreur parsing Infogreffe: {e}")
            return None

    def _parse_bodacc_company(self, record: Dict[str, Any]) -> Optional[Company]:
        """Parse une entreprise depuis les données BODACC v2.1"""
        try:
            # Dans l'API v2.1, les données sont directement dans record

            # Informations de base
            name = record.get("commercant")
            if not name:
                logger.debug("Pas de nom d'entreprise trouvé")
                return None

            # Date de création depuis dateparution
            created_at = None
            if record.get("dateparution"):
                try:
                    created_at = datetime.strptime(record["dateparution"], "%Y-%m-%d")
                except:
                    pass

            # Extraire SIREN depuis registre si disponible
            siren = None
            registre = record.get("registre")
            if registre and isinstance(registre, list) and len(registre) > 0:
                # Le SIREN est souvent le premier élément sans espaces
                siren = registre[0].replace(" ", "") if registre[0] else None

            # Extraire l'adresse depuis listeetablissements
            address = None
            try:
                import json

                listeetablissements = record.get("listeetablissements")
                if listeetablissements:
                    etablissements_data = json.loads(listeetablissements)
                    if isinstance(etablissements_data, dict) and "etablissement" in etablissements_data:
                        etablissement = etablissements_data["etablissement"]
                        if isinstance(etablissement, dict) and "adresse" in etablissement:
                            addr = etablissement["adresse"]
                            if isinstance(addr, dict):
                                parts = []
                                if addr.get("numeroVoie"):
                                    parts.append(addr["numeroVoie"])
                                if addr.get("typeVoie"):
                                    parts.append(addr["typeVoie"])
                                if addr.get("nomVoie"):
                                    parts.append(addr["nomVoie"])
                                address = " ".join(parts) if parts else None
            except Exception as e:
                logger.debug(f"Erreur extraction adresse: {e}")

            company = Company(
                siren=siren,
                siret=None,  # Pas toujours disponible dans BODACC
                name=name,
                legal_name=name,
                address=address,
                city=record.get("ville"),
                postal_code=record.get("cp"),
                region=record.get("region_nom_officiel"),
                country="FR",
                naf_code=None,  # Pas de code NAF dans BODACC
                naf_label=None,
                source=LeadSource.RCS,
                created_at=created_at,
            )

            logger.debug(f"Entreprise parsée: {name} - {siren} - {record.get('ville')}")
            return company

        except Exception as e:
            logger.error(f"Erreur parsing BODACC: {e}")
            return None

    async def search_recent_companies(
        self, days_back: int = 30, naf_codes: Optional[List[str]] = None, regions: Optional[List[str]] = None, limit: int = 100
    ) -> AsyncGenerator[Company, None]:
        """
        Recherche d'entreprises récemment créées

        Args:
            days_back: Nombre de jours en arrière pour la recherche
            naf_codes: Codes NAF/APE à filtrer (optionnel)
            regions: Codes région à filtrer (optionnel)
            limit: Nombre maximum de résultats

        Yields:
            Entreprises récemment créées
        """
        logger.info(f"Recherche d'entreprises créées dans les {days_back} derniers jours")

        # Date limite
        date_limit = datetime.now() - timedelta(days=days_back)
        date_str = date_limit.strftime("%Y-%m-%d")

        count = 0

        # Recherche BODACC (créations d'entreprises)
        try:
            params = {
                "limit": min(limit, 100),
                "where": f"dateparution >= '{date_str}' AND familleavis = 'creation'",
                "order_by": "dateparution DESC",
            }

            data = await self._make_request(self.BODACC_API, params)

            if data and data.get("results"):
                logger.info(f"BODACC: {len(data['results'])} créations trouvées")

                for record in data["results"]:
                    if count >= limit:
                        break

                    company = self._parse_bodacc_company(record)
                    if company:
                        # Filtrage par région
                        if regions and company.region:
                            # Convertir nom de région en code si nécessaire
                            if company.region not in regions:
                                continue

                        # Filtrage par NAF (si spécifié)
                        if naf_codes and company.naf_code:
                            if company.naf_code not in naf_codes:
                                continue

                        yield company
                        count += 1

        except Exception as e:
            logger.warning(f"Erreur BODACC: {e}")

        # Note: Infogreffe n'a pas d'API publique gratuite
        # On se concentre uniquement sur BODACC qui est officiel et gratuit

        logger.info(f"Recherche terminée: {count} entreprises récentes trouvées")

    async def get_creation_statistics(self, days_back: int = 30) -> Dict[str, Any]:
        """
        Récupère des statistiques sur les créations d'entreprises

        Args:
            days_back: Nombre de jours en arrière

        Returns:
            Statistiques des créations
        """
        date_limit = datetime.now() - timedelta(days=days_back)
        date_str = date_limit.strftime("%Y-%m-%d")

        stats = {
            "period_days": days_back,
            "date_from": date_str,
            "total_creations": 0,
            "by_region": {},
            "by_naf": {},
            "sources": [],
        }

        # Statistiques BODACC
        try:
            params = {"limit": 0, "where": f"dateparution >= '{date_str}' AND familleavis = 'creation'"}  # Juste pour le total

            data = await self._make_request(self.BODACC_API, params)

            if data:
                stats["total_creations"] = data.get("total_count", 0)
                stats["sources"].append("BODACC")

        except Exception as e:
            logger.error(f"Erreur statistiques BODACC: {e}")

        return stats

    async def test_connection(self) -> bool:
        """
        Teste la connexion aux APIs

        Returns:
            True si au moins une API fonctionne
        """
        # Test BODACC
        try:
            params = {"limit": 1}

            data = await self._make_request(self.BODACC_API, params)
            if data and data.get("results"):
                logger.info("✅ BODACC accessible")
                return True

        except Exception as e:
            logger.warning(f"BODACC inaccessible: {e}")

        return False
