"""
Gestionnaire principal pour la génération et l'enrichissement de leads
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Any, AsyncGenerator, Dict, List, Optional

from models import Company, CompanySize, LeadSource

from .data_gouv_api import DataGouvSireneAPI
from .free_apis import FreeEnrichmentManager
from .recent_companies_api import RecentCompaniesAPI
from .sirene import SireneAP<PERSON>
from .sirene_auth import SireneAuthAPI

logger = logging.getLogger(__name__)


class LeadManager:
    """Gestionnaire principal pour la génération de leads"""

    def __init__(self, enable_enrichment: bool = True):
        """
        Args:
            enable_enrichment: Activer l'enrichissement automatique avec APIs gratuites
        """
        self.sirene_api = SireneAPI()
        self.sirene_auth_api = SireneAuthAPI()  # API officielle avec authentification
        self.data_gouv_api = DataGouvSireneAPI()  # Alternative gratuite
        self.recent_companies_api = RecentCompaniesAPI()  # Entreprises récentes
        self.free_enrichment = FreeEnrichmentManager()
        self.enable_enrichment = enable_enrichment

        # Cache pour éviter les doublons
        self._processed_companies = set()
        self._domain_cache = {}

    def _generate_company_key(self, company: Company) -> str:
        """Génère une clé unique pour identifier une entreprise"""
        if company.siren:
            return f"siren:{company.siren}"
        elif company.domain:
            return f"domain:{company.domain}"
        else:
            return f"name:{company.name.lower().strip()}"

    def _is_duplicate(self, company: Company) -> bool:
        """Vérifie si une entreprise a déjà été traitée"""
        key = self._generate_company_key(company)
        if key in self._processed_companies:
            return True
        self._processed_companies.add(key)
        return False

    async def _enrich_company(self, company: Company) -> Company:
        """Enrichit une entreprise avec les APIs gratuites disponibles"""
        if not self.enable_enrichment:
            return company

        try:
            # Enrichissement avec APIs gratuites
            enriched_company = await self.free_enrichment.enrich_company(company)

            # Mise à jour du timestamp
            enriched_company.updated_at = datetime.now()

            return enriched_company

        except Exception as e:
            logger.warning(f"Erreur enrichissement pour {company.name}: {e}")
            return company

    async def generate_leads_from_sirene(
        self,
        naf_codes: List[str],
        regions: Optional[List[str]] = None,
        min_employees: Optional[int] = None,
        max_employees: Optional[int] = None,
        max_results: int = 500,
    ) -> AsyncGenerator[Company, None]:
        """
        Génère des leads depuis l'API Sirene

        Args:
            naf_codes: Codes NAF/APE à cibler
            regions: Codes région INSEE
            min_employees: Effectif minimum
            max_employees: Effectif maximum
            max_results: Nombre maximum de résultats
        """
        count = 0

        # Essayer d'abord l'API authentifiée (plus fiable)
        try:
            logger.info("🔑 Utilisation de l'API Sirene authentifiée...")
            async for company in self.sirene_auth_api.search_companies(
                naf_codes=naf_codes,
                regions=regions,
                min_employees=min_employees,
                max_employees=max_employees,
                limit=max_results,
            ):
                if count >= max_results:
                    break

                # Vérifier les doublons
                if self._is_duplicate(company):
                    continue

                # Essayer de deviner le domaine web
                if not company.domain and company.name:
                    domain_guess = self._guess_domain(company.name)
                    if domain_guess:
                        company.domain = domain_guess

                # Enrichissement
                enriched_company = await self._enrich_company(company)

                yield enriched_company
                count += 1

            if count > 0:
                logger.info(f"✅ API Sirene authentifiée: {count} leads générés")
                return

        except Exception as e:
            logger.warning(f"⚠️ API Sirene authentifiée indisponible: {e}")

        # Fallback sur l'ancienne API (sans auth)
        logger.info("🔄 Fallback sur API Sirene sans authentification...")
        try:
            async for company in self.sirene_api.search_companies(
                naf_codes=naf_codes, regions=regions, min_employees=min_employees, max_employees=max_employees
            ):
                if count >= max_results:
                    break

                # Vérifier les doublons
                if self._is_duplicate(company):
                    continue

                # Essayer de deviner le domaine web
                if not company.domain and company.name:
                    domain_guess = self._guess_domain(company.name)
                    if domain_guess:
                        company.domain = domain_guess

                # Enrichissement
                enriched_company = await self._enrich_company(company)

                yield enriched_company
                count += 1

            logger.info(f"✅ API Sirene sans auth: {count} leads générés")

        except Exception as e:
            logger.error(f"❌ Erreur API Sirene: {e}")
            logger.warning("Aucune source Sirene disponible")

    def _guess_domain(self, company_name: str) -> Optional[str]:
        """
        Essaie de deviner le domaine web d'une entreprise

        Args:
            company_name: Nom de l'entreprise

        Returns:
            Domaine deviné ou None
        """
        if not company_name:
            return None

        # Nettoyer le nom
        clean_name = company_name.lower()

        # Supprimer les mots courants
        stop_words = [
            "sarl",
            "sas",
            "sa",
            "eurl",
            "sci",
            "snc",
            "scp",
            "selarl",
            "et",
            "le",
            "la",
            "les",
            "de",
            "du",
            "des",
            "et",
            "cie",
            "company",
            "ltd",
            "limited",
            "inc",
            "corp",
            "llc",
        ]

        words = clean_name.split()
        filtered_words = [w for w in words if w not in stop_words and len(w) > 2]

        if not filtered_words:
            return None

        # Prendre les premiers mots significatifs
        domain_base = "".join(filtered_words[:2])

        # Nettoyer les caractères spéciaux
        domain_base = "".join(c for c in domain_base if c.isalnum())

        if len(domain_base) < 3:
            return None

        # Extensions courantes à tester
        extensions = [".fr", ".com", ".net"]

        # Pour l'instant, on retourne juste le .fr
        # Dans une vraie implémentation, on pourrait tester la résolution DNS
        return f"{domain_base}.fr"

    async def generate_leads_from_data_gouv(
        self,
        naf_codes: List[str],
        regions: Optional[List[str]] = None,
        departments: Optional[List[str]] = None,
        min_employees: Optional[int] = None,
        max_employees: Optional[int] = None,
        max_results: int = 100,
    ) -> AsyncGenerator[Company, None]:
        """
        Génère des leads depuis les fichiers CSV de data.gouv.fr

        Args:
            naf_codes: Codes NAF/APE à rechercher
            regions: Codes région (optionnel)
            departments: Codes département (optionnel)
            min_employees: Nombre minimum d'employés
            max_employees: Nombre maximum d'employés
            max_results: Nombre maximum de résultats

        Yields:
            Entreprises trouvées et enrichies
        """
        logger.info(f"Génération de leads data.gouv.fr: NAF={naf_codes}, max={max_results}")

        count = 0
        async for company in self.data_gouv_api.search_companies(
            naf_codes=naf_codes,
            regions=regions,
            departments=departments,
            min_employees=min_employees,
            max_employees=max_employees,
            limit=max_results,
        ):
            # Vérifier les doublons
            if self._is_duplicate(company):
                continue

            # Essayer de deviner le domaine web
            if not company.domain and company.name:
                domain_guess = self._guess_domain(company.name)
                if domain_guess:
                    company.domain = domain_guess

            # Enrichissement
            enriched_company = await self._enrich_company(company)

            yield enriched_company
            count += 1

            if count >= max_results:
                break

        logger.info(f"Génération data.gouv.fr terminée: {count} leads générés")

    async def search_companies_flexible(
        self,
        naf_codes: Optional[List[str]] = None,
        regions: Optional[List[str]] = None,
        departments: Optional[List[str]] = None,
        min_employees: Optional[int] = None,
        max_employees: Optional[int] = None,
        recent_only: bool = False,
        days_back: int = 30,
        max_results: int = 100,
        sources: Optional[List[str]] = None,
    ) -> AsyncGenerator[Company, None]:
        """
        Recherche flexible d'entreprises avec filtres optionnels

        Args:
            naf_codes: Codes NAF/APE à cibler (optionnel - si vide, tous secteurs)
            regions: Codes région INSEE (optionnel)
            departments: Codes département (optionnel)
            min_employees: Effectif minimum (optionnel)
            max_employees: Effectif maximum (optionnel)
            recent_only: Si True, cherche uniquement les entreprises récentes
            days_back: Nombre de jours en arrière pour recent_only
            max_results: Nombre maximum de résultats
            sources: Sources à utiliser ['sirene', 'recent', 'data_gouv'] (optionnel)

        Yields:
            Entreprises correspondant aux critères
        """
        logger.info(f"Recherche flexible: NAF={naf_codes or 'TOUS'}, régions={regions}, récent={recent_only}")

        # Sources par défaut
        if sources is None:
            if recent_only:
                sources = ["recent", "sirene"]
            else:
                sources = ["sirene", "data_gouv"]

        count = 0

        # 1. Entreprises récentes (si demandé)
        if "recent" in sources and (recent_only or not naf_codes):
            try:
                logger.info(f"🆕 Recherche d'entreprises récentes ({days_back} jours)...")
                async for company in self.recent_companies_api.search_recent_companies(
                    days_back=days_back, naf_codes=naf_codes, regions=regions, limit=max_results - count
                ):
                    if count >= max_results:
                        break

                    # Vérifier les doublons
                    if self._is_duplicate(company):
                        continue

                    # Filtrage par effectifs
                    if min_employees and company.employees and company.employees < min_employees:
                        continue
                    if max_employees and company.employees and company.employees > max_employees:
                        continue

                    # Enrichissement
                    enriched_company = await self._enrich_company(company)

                    yield enriched_company
                    count += 1

                logger.info(f"✅ Entreprises récentes: {count} trouvées")

            except Exception as e:
                logger.warning(f"⚠️ Erreur entreprises récentes: {e}")

        # 2. API Sirene (si pas assez de résultats)
        if "sirene" in sources and count < max_results:
            try:
                remaining = max_results - count
                logger.info(f"🔍 Recherche Sirene (reste {remaining} à trouver)...")

                # Si aucun NAF spécifié, recherche générale
                search_naf_codes = naf_codes if naf_codes else []

                async for company in self.sirene_auth_api.search_companies(
                    naf_codes=search_naf_codes,
                    regions=regions,
                    min_employees=min_employees,
                    max_employees=max_employees,
                    limit=remaining,
                ):
                    if count >= max_results:
                        break

                    # Vérifier les doublons
                    if self._is_duplicate(company):
                        continue

                    # Enrichissement
                    enriched_company = await self._enrich_company(company)

                    yield enriched_company
                    count += 1

                logger.info(f"✅ API Sirene: {count} total trouvées")

            except Exception as e:
                logger.warning(f"⚠️ Erreur API Sirene: {e}")

        # 3. Data.gouv.fr (fallback)
        if "data_gouv" in sources and count < max_results:
            try:
                remaining = max_results - count
                logger.info(f"📊 Recherche data.gouv.fr (reste {remaining} à trouver)...")

                async for company in self.generate_leads_from_data_gouv(
                    naf_codes=naf_codes or [],
                    regions=regions,
                    departments=departments,
                    min_employees=min_employees,
                    max_employees=max_employees,
                    max_results=remaining,
                ):
                    if count >= max_results:
                        break

                    # Vérifier les doublons
                    if self._is_duplicate(company):
                        continue

                    yield company  # Déjà enrichi dans la méthode
                    count += 1

                logger.info(f"✅ Data.gouv.fr: {count} total trouvées")

            except Exception as e:
                logger.warning(f"⚠️ Erreur data.gouv.fr: {e}")

        logger.info(f"🎯 Recherche terminée: {count} entreprises trouvées au total")

    async def search_leads_opendatasoft(
        self, naf_codes: List[str], departments: Optional[List[str]] = None, max_results: int = 100
    ) -> List[Company]:
        """
        Recherche de leads via OpenDataSoft (gratuit)

        Args:
            naf_codes: Codes NAF/APE à rechercher
            departments: Codes département (optionnel)
            max_results: Nombre maximum de résultats
        """
        try:
            companies = await self.free_enrichment.search_companies_opendatasoft(
                naf_codes=naf_codes, departments=departments, limit=max_results
            )

            # Filtrer les doublons et enrichir
            enriched_companies = []
            for company in companies:
                if not self._is_duplicate(company):
                    enriched_company = await self._enrich_company(company)
                    enriched_companies.append(enriched_company)

            return enriched_companies

        except Exception as e:
            logger.error(f"Erreur recherche OpenDataSoft: {e}")
            return []

    async def export_leads(self, companies: List[Company], filename: str) -> None:
        """
        Exporte les leads vers un fichier JSON

        Args:
            companies: Liste des entreprises
            filename: Nom du fichier de sortie
        """
        try:
            # Convertir en dictionnaires pour la sérialisation
            companies_data = []
            for company in companies:
                company_dict = company.dict()
                # Convertir les dates en ISO format
                for key, value in company_dict.items():
                    if isinstance(value, datetime):
                        company_dict[key] = value.isoformat()
                companies_data.append(company_dict)

            # Métadonnées d'export
            export_data = {
                "export_date": datetime.now().isoformat(),
                "total_companies": len(companies),
                "companies": companies_data,
            }

            with open(filename, "w", encoding="utf-8") as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)

            logger.info(f"Export de {len(companies)} entreprises vers {filename}")

        except Exception as e:
            logger.error(f"Erreur export vers {filename}: {e}")
            raise

    async def load_leads(self, filename: str) -> List[Company]:
        """
        Charge des leads depuis un fichier JSON

        Args:
            filename: Nom du fichier à charger

        Returns:
            Liste des entreprises chargées
        """
        try:
            with open(filename, "r", encoding="utf-8") as f:
                data = json.load(f)

            companies = []
            companies_data = data.get("companies", [])

            for company_dict in companies_data:
                # Reconvertir les dates
                for key, value in company_dict.items():
                    if key in ["created_at", "updated_at"] and isinstance(value, str):
                        try:
                            company_dict[key] = datetime.fromisoformat(value)
                        except ValueError:
                            pass

                company = Company(**company_dict)
                companies.append(company)

            logger.info(f"Chargement de {len(companies)} entreprises depuis {filename}")
            return companies

        except Exception as e:
            logger.error(f"Erreur chargement depuis {filename}: {e}")
            return []
