"""
Sources alternatives gratuites pour la génération de leads
"""

import asyncio
import json
import logging
import re
from typing import Any, AsyncGenerator, Dict, List, Optional

import httpx
from bs4 import BeautifulSoup

from models import Company, LeadSource

logger = logging.getLogger(__name__)


class PagesJaunesAPI:
    """
    Scraper pour Pages Jaunes (gratuit mais à utiliser avec modération)
    """

    BASE_URL = "https://www.pagesjaunes.fr"

    def __init__(self):
        self.session = None
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }

    async def search_companies(self, activity: str, location: str = "France", max_results: int = 50) -> List[Company]:
        """
        Recherche d'entreprises sur Pages Jaunes

        Args:
            activity: Activité recherchée (ex: "développement web")
            location: Localisation
            max_results: Nombre maximum de résultats

        Returns:
            Liste d'entreprises trouvées
        """
        companies = []

        try:
            # Construire l'URL de recherche
            search_url = f"{self.BASE_URL}/pagesblanches/recherche"
            params = {"quoiqui": activity, "ou": location, "proximite": "0"}

            async with httpx.AsyncClient(headers=self.headers, timeout=30.0) as client:
                response = await client.get(search_url, params=params)

                if response.status_code == 200:
                    soup = BeautifulSoup(response.text, "html.parser")

                    # Parser les résultats
                    results = soup.find_all("div", class_="bi-bloc")

                    for result in results[:max_results]:
                        company = self._parse_pages_jaunes_result(result)
                        if company:
                            companies.append(company)

                        # Délai pour éviter d'être bloqué
                        await asyncio.sleep(0.5)

        except Exception as e:
            logger.error(f"Erreur recherche Pages Jaunes: {e}")

        return companies

    def _parse_pages_jaunes_result(self, result_div) -> Optional[Company]:
        """Parse un résultat Pages Jaunes"""
        try:
            # Nom de l'entreprise
            name_elem = result_div.find("h3", class_="bi-raison-sociale")
            if not name_elem:
                return None

            name = name_elem.get_text(strip=True)

            # Adresse
            address_elem = result_div.find("div", class_="bi-adresse")
            address = address_elem.get_text(strip=True) if address_elem else ""

            # Téléphone
            phone_elem = result_div.find("div", class_="bi-telephone")
            phone = phone_elem.get_text(strip=True) if phone_elem else ""

            # Site web
            website_elem = result_div.find("a", class_="bi-website")
            website = website_elem.get("href") if website_elem else None

            # Extraire ville et code postal de l'adresse
            city = ""
            postal_code = ""
            if address:
                # Pattern pour code postal français
                postal_match = re.search(r"\b(\d{5})\b", address)
                if postal_match:
                    postal_code = postal_match.group(1)
                    # La ville est généralement après le code postal
                    city_part = address.split(postal_code)[-1].strip()
                    city = city_part.split(",")[0].strip()

            # Deviner le domaine depuis le site web
            domain = None
            if website:
                domain_match = re.search(r"https?://(?:www\.)?([^/]+)", website)
                if domain_match:
                    domain = domain_match.group(1)

            company = Company(
                name=name,
                address=address,
                city=city,
                postal_code=postal_code,
                phone=phone,
                website=website,
                domain=domain,
                country="FR",
                source=LeadSource.MANUAL,  # Pages Jaunes = source manuelle
            )

            return company

        except Exception as e:
            logger.error(f"Erreur parsing Pages Jaunes: {e}")
            return None


class SocieteComAPI:
    """
    API gratuite de Societe.com pour les données d'entreprises
    """

    BASE_URL = "https://www.societe.com"

    def __init__(self):
        self.headers = {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"}

    async def search_by_activity(
        self, activity_keywords: List[str], department: Optional[str] = None, max_results: int = 50
    ) -> List[Company]:
        """
        Recherche d'entreprises par mots-clés d'activité

        Args:
            activity_keywords: Mots-clés d'activité
            department: Département (optionnel)
            max_results: Nombre maximum de résultats

        Returns:
            Liste d'entreprises
        """
        companies = []

        try:
            for keyword in activity_keywords:
                search_url = f"{self.BASE_URL}/recherche"
                params = {"q": keyword, "type": "entreprise"}

                if department:
                    params["departement"] = department

                async with httpx.AsyncClient(headers=self.headers, timeout=30.0) as client:
                    response = await client.get(search_url, params=params)

                    if response.status_code == 200:
                        soup = BeautifulSoup(response.text, "html.parser")

                        # Parser les résultats
                        results = soup.find_all("div", class_="company-result")

                        for result in results[: max_results // len(activity_keywords)]:
                            company = self._parse_societe_com_result(result)
                            if company:
                                companies.append(company)

                        # Délai entre les requêtes
                        await asyncio.sleep(1)

        except Exception as e:
            logger.error(f"Erreur recherche Societe.com: {e}")

        return companies

    def _parse_societe_com_result(self, result_div) -> Optional[Company]:
        """Parse un résultat Societe.com"""
        try:
            # Nom de l'entreprise
            name_elem = result_div.find("h2") or result_div.find("h3")
            if not name_elem:
                return None

            name = name_elem.get_text(strip=True)

            # SIREN (si disponible)
            siren_elem = result_div.find(text=re.compile(r"SIREN"))
            siren = None
            if siren_elem:
                siren_match = re.search(r"(\d{9})", str(siren_elem))
                if siren_match:
                    siren = siren_match.group(1)

            # Adresse
            address_elem = result_div.find("div", class_="address")
            address = address_elem.get_text(strip=True) if address_elem else ""

            # Code NAF
            naf_elem = result_div.find(text=re.compile(r"NAF|APE"))
            naf_code = None
            if naf_elem:
                naf_match = re.search(r"(\d{4}[A-Z])", str(naf_elem))
                if naf_match:
                    naf_code = naf_match.group(1)

            company = Company(
                siren=siren, name=name, address=address, naf_code=naf_code, country="FR", source=LeadSource.MANUAL
            )

            return company

        except Exception as e:
            logger.error(f"Erreur parsing Societe.com: {e}")
            return None


class LinkedInPublicAPI:
    """
    Recherche d'entreprises via les données publiques LinkedIn
    (Attention aux limites d'usage)
    """

    def __init__(self):
        self.headers = {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"}

    async def search_companies_by_industry(
        self, industry_keywords: List[str], location: str = "France", max_results: int = 30
    ) -> List[Company]:
        """
        Recherche d'entreprises par secteur d'activité

        Args:
            industry_keywords: Mots-clés du secteur
            location: Localisation
            max_results: Nombre maximum de résultats

        Returns:
            Liste d'entreprises
        """
        companies = []

        # Note: Cette méthode est très limitée sans API officielle
        # Elle ne devrait être utilisée qu'occasionnellement

        try:
            for keyword in industry_keywords:
                # Recherche Google avec site:linkedin.com
                search_query = f"site:linkedin.com/company {keyword} {location}"

                # Utiliser une API de recherche gratuite comme DuckDuckGo
                companies_found = await self._search_via_duckduckgo(search_query)
                companies.extend(companies_found[: max_results // len(industry_keywords)])

                # Délai important pour éviter d'être bloqué
                await asyncio.sleep(2)

        except Exception as e:
            logger.error(f"Erreur recherche LinkedIn: {e}")

        return companies

    async def _search_via_duckduckgo(self, query: str) -> List[Company]:
        """Recherche via DuckDuckGo (gratuit, pas de clé API)"""
        companies = []

        try:
            # API DuckDuckGo Instant Answer (gratuite)
            search_url = "https://api.duckduckgo.com/"
            params = {"q": query, "format": "json", "no_html": "1", "skip_disambig": "1"}

            async with httpx.AsyncClient(headers=self.headers, timeout=30.0) as client:
                response = await client.get(search_url, params=params)

                if response.status_code == 200:
                    data = response.json()

                    # Parser les résultats
                    for result in data.get("RelatedTopics", [])[:5]:
                        if isinstance(result, dict) and "Text" in result:
                            company = self._parse_duckduckgo_result(result)
                            if company:
                                companies.append(company)

        except Exception as e:
            logger.error(f"Erreur DuckDuckGo: {e}")

        return companies

    def _parse_duckduckgo_result(self, result: Dict[str, Any]) -> Optional[Company]:
        """Parse un résultat DuckDuckGo"""
        try:
            text = result.get("Text", "")
            url = result.get("FirstURL", "")

            # Extraire le nom de l'entreprise depuis l'URL LinkedIn
            if "linkedin.com/company/" in url:
                company_slug = url.split("/company/")[-1].split("/")[0]
                name = company_slug.replace("-", " ").title()

                # Extraire le domaine si mentionné dans le texte
                domain_match = re.search(r"https?://(?:www\.)?([^/\s]+)", text)
                domain = domain_match.group(1) if domain_match else None

                company = Company(
                    name=name, domain=domain, website=f"https://{domain}" if domain else None, source=LeadSource.MANUAL
                )

                return company

        except Exception as e:
            logger.error(f"Erreur parsing DuckDuckGo: {e}")

        return None


class FreeLeadAggregator:
    """Agrégateur de toutes les sources gratuites"""

    def __init__(self):
        self.pages_jaunes = PagesJaunesAPI()
        self.societe_com = SocieteComAPI()
        self.linkedin_public = LinkedInPublicAPI()

    async def search_all_sources(
        self, activity_keywords: List[str], location: str = "France", max_results_per_source: int = 20
    ) -> List[Company]:
        """
        Recherche sur toutes les sources gratuites

        Args:
            activity_keywords: Mots-clés d'activité
            location: Localisation
            max_results_per_source: Résultats max par source

        Returns:
            Liste agrégée d'entreprises
        """
        all_companies = []

        # Pages Jaunes
        try:
            for keyword in activity_keywords[:2]:  # Limiter pour éviter le spam
                pj_companies = await self.pages_jaunes.search_companies(
                    keyword, location, max_results_per_source // len(activity_keywords)
                )
                all_companies.extend(pj_companies)
                await asyncio.sleep(1)
        except Exception as e:
            logger.error(f"Erreur Pages Jaunes: {e}")

        # Societe.com
        try:
            sc_companies = await self.societe_com.search_by_activity(activity_keywords[:2], None, max_results_per_source)
            all_companies.extend(sc_companies)
        except Exception as e:
            logger.error(f"Erreur Societe.com: {e}")

        # LinkedIn (très limité)
        try:
            li_companies = await self.linkedin_public.search_companies_by_industry(
                activity_keywords[:1], location, max_results_per_source // 2
            )
            all_companies.extend(li_companies)
        except Exception as e:
            logger.error(f"Erreur LinkedIn: {e}")

        # Déduplication basique par nom
        unique_companies = []
        seen_names = set()

        for company in all_companies:
            name_key = company.name.lower().strip()
            if name_key not in seen_names:
                seen_names.add(name_key)
                unique_companies.append(company)

        logger.info(f"Agrégation terminée: {len(unique_companies)} entreprises uniques")
        return unique_companies
