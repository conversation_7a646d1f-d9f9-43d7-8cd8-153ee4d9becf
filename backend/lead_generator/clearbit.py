"""
API Clearbit pour l'enrichissement de données et la recherche d'emails
"""

import asyncio
import logging
from typing import Any, Dict, List, Optional

import httpx

from models import Company, LeadSource

logger = logging.getLogger(__name__)


class ClearbitAPI:
    """Client pour l'API Clearbit"""

    BASE_URL = "https://person-stream.clearbit.com/v2"
    ENRICHMENT_URL = "https://company-stream.clearbit.com/v2"
    PROSPECTOR_URL = "https://prospector.clearbit.com/v1"

    def __init__(self, api_key: str, rate_limit: int = 10):
        """
        Args:
            api_key: Clé API Clearbit
            rate_limit: Nombre de requêtes par minute
        """
        self.api_key = api_key
        self.rate_limit = rate_limit
        self._semaphore = asyncio.Semaphore(rate_limit)
        self._last_request_time = 0

    async def _make_request(self, url: str, params: Optional[Dict[str, Any]] = None) -> Optional[Dict[str, Any]]:
        """Effectue une requête à l'API Clearbit avec rate limiting"""
        async with self._semaphore:
            # Rate limiting (par minute)
            current_time = asyncio.get_event_loop().time()
            time_since_last = current_time - self._last_request_time
            if time_since_last < (60.0 / self.rate_limit):
                await asyncio.sleep((60.0 / self.rate_limit) - time_since_last)

            self._last_request_time = asyncio.get_event_loop().time()

            async with httpx.AsyncClient() as client:
                try:
                    response = await client.get(url, params=params or {}, auth=(self.api_key, ""), timeout=30.0)

                    if response.status_code == 202:
                        # Requête en cours de traitement
                        logger.info("Clearbit: Requête en cours de traitement")
                        return None
                    elif response.status_code == 404:
                        # Pas de données trouvées
                        return None

                    response.raise_for_status()
                    return response.json()

                except httpx.HTTPError as e:
                    logger.error(f"Erreur API Clearbit: {e}")
                    return None

    async def enrich_company(self, domain: str) -> Optional[Dict[str, Any]]:
        """
        Enrichit les données d'une entreprise via son domaine

        Args:
            domain: Domaine de l'entreprise (ex: "example.com")

        Returns:
            Données enrichies de l'entreprise ou None
        """
        url = f"{self.ENRICHMENT_URL}/companies/find"
        params = {"domain": domain}

        try:
            data = await self._make_request(url, params)
            return data
        except Exception as e:
            logger.error(f"Erreur enrichissement Clearbit pour {domain}: {e}")
            return None

    async def find_emails(
        self, domain: str, roles: Optional[List[str]] = None, seniorities: Optional[List[str]] = None, limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        Trouve des emails professionnels pour un domaine

        Args:
            domain: Domaine de l'entreprise
            roles: Rôles ciblés (ex: ["engineering", "marketing"])
            seniorities: Niveaux hiérarchiques (ex: ["manager", "director"])
            limit: Nombre maximum d'emails à retourner

        Returns:
            Liste des contacts trouvés
        """
        url = f"{self.PROSPECTOR_URL}/people/search"
        params = {"domain": domain, "limit": limit}

        if roles:
            params["role"] = roles
        if seniorities:
            params["seniority"] = seniorities

        try:
            data = await self._make_request(url, params)
            if data and "results" in data:
                return data["results"]
            return []
        except Exception as e:
            logger.error(f"Erreur recherche emails Clearbit pour {domain}: {e}")
            return []

    async def enrich_company_data(self, company: Company) -> Company:
        """
        Enrichit les données d'une entreprise avec Clearbit

        Args:
            company: Objet Company à enrichir

        Returns:
            Company enrichie
        """
        if not company.domain:
            return company

        # Enrichissement des données entreprise
        enrichment_data = await self.enrich_company(company.domain)

        if enrichment_data:
            # Mise à jour des informations manquantes
            if not company.website and enrichment_data.get("domain"):
                company.website = f"https://{enrichment_data['domain']}"

            if not company.employees and enrichment_data.get("metrics", {}).get("employees"):
                company.employees = enrichment_data["metrics"]["employees"]

            if not company.revenue and enrichment_data.get("metrics", {}).get("annualRevenue"):
                company.revenue = enrichment_data["metrics"]["annualRevenue"]

            # Informations géographiques
            geo = enrichment_data.get("geo", {})
            if not company.city and geo.get("city"):
                company.city = geo["city"]
            if not company.country and geo.get("country"):
                company.country = geo["country"]

        # Recherche d'emails si pas d'email principal
        if not company.email:
            contacts = await self.find_emails(
                company.domain,
                roles=["engineering", "it", "cto", "ceo"],
                seniorities=["manager", "director", "vp", "c_level"],
                limit=5,
            )

            if contacts:
                # Prendre le premier contact avec un email valide
                for contact in contacts:
                    if contact.get("email"):
                        company.email = contact["email"]
                        break

        return company

    async def search_companies(
        self,
        query: str,
        location: Optional[str] = None,
        industry: Optional[str] = None,
        employees_range: Optional[str] = None,
        limit: int = 50,
    ) -> List[Company]:
        """
        Recherche d'entreprises via Clearbit Prospector

        Args:
            query: Terme de recherche
            location: Localisation (ex: "France")
            industry: Secteur d'activité
            employees_range: Fourchette d'employés (ex: "1-50")
            limit: Nombre maximum de résultats

        Returns:
            Liste d'entreprises trouvées
        """
        url = f"{self.PROSPECTOR_URL}/companies/search"
        params = {"query": query, "limit": limit}

        if location:
            params["location"] = location
        if industry:
            params["industry"] = industry
        if employees_range:
            params["employees_range"] = employees_range

        try:
            data = await self._make_request(url, params)
            companies = []

            if data and "results" in data:
                for result in data["results"]:
                    company = Company(
                        name=result.get("name", ""),
                        domain=result.get("domain"),
                        website=f"https://{result['domain']}" if result.get("domain") else None,
                        city=result.get("geo", {}).get("city"),
                        country=result.get("geo", {}).get("country", "FR"),
                        employees=result.get("metrics", {}).get("employees"),
                        revenue=result.get("metrics", {}).get("annualRevenue"),
                        source=LeadSource.CLEARBIT,
                    )
                    companies.append(company)

            return companies

        except Exception as e:
            logger.error(f"Erreur recherche entreprises Clearbit: {e}")
            return []
