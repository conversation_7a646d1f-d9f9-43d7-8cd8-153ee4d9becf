"""
API Hunter.io pour la recherche d'emails professionnels
"""

import asyncio
import logging
from typing import Any, Dict, List, Optional

import httpx

from models import Company

logger = logging.getLogger(__name__)


class HunterAPI:
    """Client pour l'API Hunter.io"""

    BASE_URL = "https://api.hunter.io/v2"

    def __init__(self, api_key: str, rate_limit: int = 10):
        """
        Args:
            api_key: Clé API Hunter.io
            rate_limit: Nombre de requêtes par minute
        """
        self.api_key = api_key
        self.rate_limit = rate_limit
        self._semaphore = asyncio.Semaphore(rate_limit)
        self._last_request_time = 0

    async def _make_request(self, endpoint: str, params: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Effectue une requête à l'API Hunter.io avec rate limiting"""
        async with self._semaphore:
            # Rate limiting (par minute)
            current_time = asyncio.get_event_loop().time()
            time_since_last = current_time - self._last_request_time
            if time_since_last < (60.0 / self.rate_limit):
                await asyncio.sleep((60.0 / self.rate_limit) - time_since_last)

            self._last_request_time = asyncio.get_event_loop().time()

            params["api_key"] = self.api_key

            async with httpx.AsyncClient() as client:
                try:
                    response = await client.get(f"{self.BASE_URL}/{endpoint}", params=params, timeout=30.0)
                    response.raise_for_status()
                    return response.json()

                except httpx.HTTPError as e:
                    logger.error(f"Erreur API Hunter.io: {e}")
                    return None

    async def domain_search(self, domain: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Recherche tous les emails d'un domaine

        Args:
            domain: Domaine à analyser (ex: "example.com")
            limit: Nombre maximum d'emails à retourner

        Returns:
            Liste des emails trouvés avec leurs métadonnées
        """
        params = {"domain": domain, "limit": limit, "type": "personal"}  # Emails personnalisés uniquement

        try:
            data = await self._make_request("domain-search", params)
            if data and data.get("data", {}).get("emails"):
                return data["data"]["emails"]
            return []
        except Exception as e:
            logger.error(f"Erreur recherche domaine Hunter.io pour {domain}: {e}")
            return []

    async def email_finder(self, domain: str, first_name: str, last_name: str) -> Optional[Dict[str, Any]]:
        """
        Trouve l'email d'une personne spécifique

        Args:
            domain: Domaine de l'entreprise
            first_name: Prénom
            last_name: Nom de famille

        Returns:
            Informations sur l'email trouvé ou None
        """
        params = {"domain": domain, "first_name": first_name, "last_name": last_name}

        try:
            data = await self._make_request("email-finder", params)
            if data and data.get("data"):
                return data["data"]
            return None
        except Exception as e:
            logger.error(f"Erreur recherche email Hunter.io: {e}")
            return None

    async def email_verifier(self, email: str) -> Dict[str, Any]:
        """
        Vérifie la validité d'un email

        Args:
            email: Email à vérifier

        Returns:
            Résultat de la vérification avec score de confiance
        """
        params = {"email": email}

        try:
            data = await self._make_request("email-verifier", params)
            if data and data.get("data"):
                return data["data"]
            return {"result": "unknown", "score": 0}
        except Exception as e:
            logger.error(f"Erreur vérification email Hunter.io: {e}")
            return {"result": "unknown", "score": 0}

    async def get_company_emails(self, company: Company, target_roles: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """
        Récupère les emails d'une entreprise avec filtrage par rôle

        Args:
            company: Entreprise cible
            target_roles: Rôles ciblés (ex: ["ceo", "cto", "manager"])

        Returns:
            Liste des emails trouvés avec métadonnées
        """
        if not company.domain:
            return []

        emails = await self.domain_search(company.domain, limit=25)

        if not target_roles:
            return emails

        # Filtrage par rôle/position
        filtered_emails = []
        target_keywords = set()

        for role in target_roles:
            if role.lower() in ["ceo", "directeur", "president", "pdg"]:
                target_keywords.update(["ceo", "directeur", "president", "pdg", "dirigeant"])
            elif role.lower() in ["cto", "it", "technique", "informatique"]:
                target_keywords.update(["cto", "it", "technique", "informatique", "tech", "systeme"])
            elif role.lower() in ["manager", "responsable"]:
                target_keywords.update(["manager", "responsable", "chef", "lead"])

        for email_data in emails:
            position = email_data.get("position", "").lower()
            department = email_data.get("department", "").lower()

            # Vérifier si le rôle correspond aux critères
            if any(keyword in position or keyword in department for keyword in target_keywords):
                filtered_emails.append(email_data)

        # Si pas de correspondance exacte, prendre les emails les plus pertinents
        if not filtered_emails and emails:
            # Prioriser les emails avec des positions définies
            filtered_emails = [e for e in emails if e.get("position")][:5]
            if not filtered_emails:
                filtered_emails = emails[:3]  # Fallback sur les premiers emails

        return filtered_emails

    async def enrich_company_with_emails(self, company: Company) -> Company:
        """
        Enrichit une entreprise avec des emails Hunter.io

        Args:
            company: Entreprise à enrichir

        Returns:
            Entreprise enrichie avec email principal
        """
        if not company.domain or company.email:
            return company

        # Rechercher des emails avec priorité sur les décideurs
        emails = await self.get_company_emails(company, target_roles=["ceo", "cto", "manager", "directeur"])

        if emails:
            # Prendre l'email avec le meilleur score de confiance
            best_email = max(emails, key=lambda x: x.get("confidence", 0))

            if best_email.get("value") and best_email.get("confidence", 0) >= 50:
                company.email = best_email["value"]

                # Vérifier la validité de l'email
                verification = await self.email_verifier(company.email)
                if verification.get("result") in ["undeliverable", "risky"]:
                    # Essayer le deuxième meilleur email
                    remaining_emails = [e for e in emails if e != best_email]
                    if remaining_emails:
                        second_best = max(remaining_emails, key=lambda x: x.get("confidence", 0))
                        if second_best.get("confidence", 0) >= 50:
                            company.email = second_best["value"]

        return company
