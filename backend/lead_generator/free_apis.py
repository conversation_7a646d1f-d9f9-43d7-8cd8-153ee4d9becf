"""
APIs gratuites pour l'enrichissement de données d'entreprises
"""

import asyncio
import logging
import re
from typing import Any, Dict, List, Optional

import httpx

from models import Company, LeadSource

from .email_finder_free import EmailValidator, SmartEmailFinder

logger = logging.getLogger(__name__)


class FreeEmailFinder:
    """Recherche d'emails gratuite basée sur des patterns communs"""

    def __init__(self):
        self.common_patterns = [
            "contact@{domain}",
            "info@{domain}",
            "hello@{domain}",
            "bonjour@{domain}",
            "commercial@{domain}",
            "vente@{domain}",
            "direction@{domain}",
            "admin@{domain}",
        ]

    async def find_emails_for_domain(self, domain: str) -> List[str]:
        """
        Génère des emails probables pour un domaine

        Args:
            domain: Domaine de l'entreprise

        Returns:
            Liste d'emails probables
        """
        if not domain:
            return []

        # Nettoyer le domaine
        domain = domain.lower().replace("www.", "").replace("http://", "").replace("https://", "")
        if "/" in domain:
            domain = domain.split("/")[0]

        emails = []
        for pattern in self.common_patterns:
            email = pattern.format(domain=domain)
            emails.append(email)

        return emails

    async def verify_email_format(self, email: str) -> bool:
        """Vérifie le format d'un email (basique)"""
        pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
        return bool(re.match(pattern, email))


class APIEntrepriseGouv:
    """
    API Entreprise du gouvernement français (gratuite)
    https://entreprise.api.gouv.fr/
    """

    BASE_URL = "https://entreprise.api.gouv.fr/v3"

    def __init__(self):
        self.session = None

    async def _make_request(self, endpoint: str, params: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """Effectue une requête à l'API Entreprise"""
        if params is None:
            params = {}

        # Ajouter les paramètres requis pour l'API Entreprise
        params.update({"context": "prospection", "recipient": "particulier", "object": "recherche_entreprise"})

        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(f"{self.BASE_URL}/{endpoint}", params=params)

                if response.status_code == 200:
                    return response.json()
                elif response.status_code == 429:
                    logger.warning("Rate limit atteint pour API Entreprise")
                    await asyncio.sleep(1)
                    return None
                else:
                    logger.warning(f"Erreur API Entreprise: {response.status_code}")
                    return None

        except Exception as e:
            logger.error(f"Erreur requête API Entreprise: {e}")
            return None

    async def get_company_info(self, siren: str) -> Optional[Dict[str, Any]]:
        """
        Récupère les informations d'une entreprise par SIREN

        Args:
            siren: SIREN de l'entreprise

        Returns:
            Informations de l'entreprise ou None
        """
        try:
            # Informations de base
            data = await self._make_request(f"insee/sirene/unites_legales/{siren}")

            if data and "data" in data:
                return data["data"]

            return None

        except Exception as e:
            logger.error(f"Erreur récupération info entreprise {siren}: {e}")
            return None


class OpenDataSoft:
    """
    API OpenDataSoft pour les données d'entreprises françaises (gratuite)
    """

    BASE_URL = "https://public.opendatasoft.com/api/records/1.0"

    async def search_companies_by_activity(
        self, activity_codes: List[str], department: Optional[str] = None, limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Recherche d'entreprises par code d'activité

        Args:
            activity_codes: Codes NAF/APE
            department: Code département (optionnel)
            limit: Nombre maximum de résultats

        Returns:
            Liste d'entreprises
        """
        try:
            # Construire la requête
            query_parts = []

            # Codes d'activité
            if activity_codes:
                activity_query = " OR ".join([f"activiteprincipaleetablissement:{code}" for code in activity_codes])
                query_parts.append(f"({activity_query})")

            # Département
            if department:
                query_parts.append(f"codedepartementetablissement:{department}")

            query = " AND ".join(query_parts) if query_parts else "*"

            params = {
                "dataset": "sirene_v3",
                "q": query,
                "rows": limit,
                "facet": ["activiteprincipaleetablissement", "codedepartementetablissement"],
            }

            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(f"{self.BASE_URL}/search", params=params)

                if response.status_code == 200:
                    data = response.json()
                    return data.get("records", [])
                else:
                    logger.warning(f"Erreur OpenDataSoft: {response.status_code}")
                    return []

        except Exception as e:
            logger.error(f"Erreur recherche OpenDataSoft: {e}")
            return []


class DomainGuesser:
    """Devinette de domaines web basée sur le nom d'entreprise"""

    def __init__(self):
        self.common_extensions = [".fr", ".com", ".net", ".org"]
        self.stop_words = [
            "sarl",
            "sas",
            "sa",
            "eurl",
            "sci",
            "snc",
            "scp",
            "selarl",
            "et",
            "le",
            "la",
            "les",
            "de",
            "du",
            "des",
            "cie",
            "company",
            "ltd",
            "limited",
            "inc",
            "corp",
            "llc",
            "groupe",
            "group",
        ]

    def clean_company_name(self, name: str) -> str:
        """Nettoie le nom d'entreprise pour créer un domaine"""
        if not name:
            return ""

        # Convertir en minuscules
        clean_name = name.lower()

        # Supprimer les caractères spéciaux
        clean_name = re.sub(r"[^a-z0-9\s]", "", clean_name)

        # Supprimer les mots vides
        words = clean_name.split()
        filtered_words = [w for w in words if w not in self.stop_words and len(w) > 2]

        # Prendre les 2 premiers mots significatifs
        if len(filtered_words) >= 2:
            domain_base = "".join(filtered_words[:2])
        elif len(filtered_words) == 1:
            domain_base = filtered_words[0]
        else:
            return ""

        return domain_base

    def guess_domains(self, company_name: str) -> List[str]:
        """
        Devine les domaines possibles pour une entreprise

        Args:
            company_name: Nom de l'entreprise

        Returns:
            Liste de domaines possibles
        """
        domain_base = self.clean_company_name(company_name)

        if not domain_base or len(domain_base) < 3:
            return []

        domains = []
        for ext in self.common_extensions:
            domains.append(f"{domain_base}{ext}")

        # Variantes avec tirets
        if len(domain_base) > 6:
            # Essayer de couper en deux mots
            mid = len(domain_base) // 2
            variant = f"{domain_base[:mid]}-{domain_base[mid:]}"
            for ext in self.common_extensions:
                domains.append(f"{variant}{ext}")

        return domains

    async def verify_domain_exists(self, domain: str) -> bool:
        """
        Vérifie si un domaine existe (test HTTP basique)

        Args:
            domain: Domaine à vérifier

        Returns:
            True si le domaine répond
        """
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                for protocol in ["https://", "http://"]:
                    try:
                        response = await client.get(f"{protocol}{domain}")
                        if response.status_code < 500:  # Tout sauf erreur serveur
                            return True
                    except:
                        continue
            return False
        except:
            return False


class FreeEnrichmentManager:
    """Gestionnaire d'enrichissement utilisant uniquement des APIs gratuites"""

    def __init__(self):
        self.email_finder = FreeEmailFinder()
        self.smart_email_finder = SmartEmailFinder()
        self.api_entreprise = APIEntrepriseGouv()
        self.opendatasoft = OpenDataSoft()
        self.domain_guesser = DomainGuesser()

    async def enrich_company(self, company: Company) -> Company:
        """
        Enrichit une entreprise avec les APIs gratuites

        Args:
            company: Entreprise à enrichir

        Returns:
            Entreprise enrichie
        """
        enriched_company = company.copy()

        try:
            # 1. Enrichir avec API Entreprise si on a un SIREN
            if company.siren and not company.email:
                api_data = await self.api_entreprise.get_company_info(company.siren)
                if api_data:
                    # Extraire les informations supplémentaires
                    if not enriched_company.legal_name and api_data.get("denomination"):
                        enriched_company.legal_name = api_data["denomination"]

            # 2. Deviner le domaine si pas présent
            if not enriched_company.domain and enriched_company.name:
                possible_domains = self.domain_guesser.guess_domains(enriched_company.name)

                for domain in possible_domains:
                    if await self.domain_guesser.verify_domain_exists(domain):
                        enriched_company.domain = domain
                        enriched_company.website = f"https://{domain}"
                        break

            # 3. Recherche d'emails intelligente
            if not enriched_company.email and enriched_company.domain:
                # Utiliser le nouveau système de recherche d'emails
                best_email = await self.smart_email_finder.find_best_email_for_company(enriched_company)
                if best_email and EmailValidator.is_valid_format(best_email):
                    enriched_company.email = best_email
                else:
                    # Fallback sur l'ancien système
                    possible_emails = await self.email_finder.find_emails_for_domain(enriched_company.domain)
                    for email in possible_emails:
                        if await self.email_finder.verify_email_format(email):
                            enriched_company.email = email
                            break

            logger.info(f"Entreprise enrichie: {enriched_company.name}")

        except Exception as e:
            logger.error(f"Erreur enrichissement {company.name}: {e}")

        return enriched_company

    async def search_companies_opendatasoft(
        self, naf_codes: List[str], departments: Optional[List[str]] = None, limit: int = 100
    ) -> List[Company]:
        """
        Recherche d'entreprises via OpenDataSoft

        Args:
            naf_codes: Codes NAF/APE
            departments: Codes département
            limit: Nombre maximum de résultats

        Returns:
            Liste d'entreprises
        """
        companies = []

        try:
            # Rechercher par département si spécifié
            if departments:
                for dept in departments:
                    records = await self.opendatasoft.search_companies_by_activity(naf_codes, dept, limit // len(departments))

                    for record in records:
                        company = self._parse_opendatasoft_record(record)
                        if company:
                            companies.append(company)
            else:
                # Recherche globale
                records = await self.opendatasoft.search_companies_by_activity(naf_codes, None, limit)

                for record in records:
                    company = self._parse_opendatasoft_record(record)
                    if company:
                        companies.append(company)

        except Exception as e:
            logger.error(f"Erreur recherche OpenDataSoft: {e}")

        return companies

    def _parse_opendatasoft_record(self, record: Dict[str, Any]) -> Optional[Company]:
        """Parse un enregistrement OpenDataSoft en objet Company"""
        try:
            fields = record.get("fields", {})

            company = Company(
                siren=fields.get("siren"),
                siret=fields.get("siret"),
                name=fields.get("denominationunitelegale") or fields.get("denominationusuelleunitelegale", ""),
                legal_name=fields.get("denominationunitelegale"),
                address=f"{fields.get('numerovoieetablissement', '')} {fields.get('typevoieetablissement', '')} {fields.get('libellevoieetablissement', '')}".strip(),
                city=fields.get("libellecommuneetablissement"),
                postal_code=fields.get("codepostaletablissement"),
                region=fields.get("libelleregioneunitelegale"),
                naf_code=fields.get("activiteprincipaleetablissement"),
                source=LeadSource.SIRENE,
            )

            return company

        except Exception as e:
            logger.error(f"Erreur parsing record OpenDataSoft: {e}")
            return None
