"""
API Sirene v3.11 avec authentification OAuth2
Utilise les nouvelles clés INSEE pour accéder aux données officielles
"""

import asyncio
import base64
import logging
import os
from datetime import datetime, timedelta
from typing import Any, AsyncGenerator, Dict, List, Optional

import httpx
from dotenv import load_dotenv

from models import Company, CompanySize, LeadSource

# Charger les variables d'environnement
load_dotenv()

logger = logging.getLogger(__name__)


class SireneAuthAPI:
    """
    Interface pour l'API Sirene de l'INSEE avec authentification OAuth2
    """

    def __init__(self, rate_limit: int = 30):
        """
        Args:
            rate_limit: Nombre de requêtes par minute (limite API Sirene)
        """
        self.rate_limit = rate_limit
        self._semaphore = asyncio.Semaphore(rate_limit)
        self._last_request_time = 0

        # Configuration depuis les variables d'environnement
        self.consumer_key = os.getenv("INSEE_CONSUMER_KEY")
        self.consumer_secret = os.getenv("INSEE_CONSUMER_SECRET")
        self.token_url = os.getenv("INSEE_TOKEN_URL", "https://api.insee.fr/token")
        self.base_url = os.getenv("INSEE_API_BASE_URL", "https://api.insee.fr/entreprises/sirene/V3.11")

        # Token management
        self._access_token = None
        self._token_expires_at = None

        if not self.consumer_key or not self.consumer_secret:
            logger.warning("Clés INSEE manquantes, API Sirene indisponible")

    async def _get_access_token(self) -> Optional[str]:
        """
        Obtient un token d'accès OAuth2 pour l'API INSEE

        Returns:
            Token d'accès ou None si erreur
        """
        # Vérifier si le token actuel est encore valide
        if self._access_token and self._token_expires_at and datetime.now() < self._token_expires_at - timedelta(minutes=5):
            return self._access_token

        if not self.consumer_key or not self.consumer_secret:
            logger.error("Clés INSEE manquantes pour l'authentification")
            return None

        try:
            # Créer l'en-tête d'autorisation Basic
            credentials = f"{self.consumer_key}:{self.consumer_secret}"
            encoded_credentials = base64.b64encode(credentials.encode()).decode()

            headers = {"Authorization": f"Basic {encoded_credentials}", "Content-Type": "application/x-www-form-urlencoded"}

            data = {"grant_type": "client_credentials"}

            async with httpx.AsyncClient() as client:
                response = await client.post(self.token_url, headers=headers, data=data, timeout=30.0)

                if response.status_code == 200:
                    token_data = response.json()
                    self._access_token = token_data.get("access_token")
                    expires_in = token_data.get("expires_in", 3600)  # Default 1 hour
                    self._token_expires_at = datetime.now() + timedelta(seconds=expires_in)

                    logger.info("✅ Token INSEE obtenu avec succès")
                    return self._access_token
                else:
                    logger.error(f"Erreur authentification INSEE: {response.status_code} - {response.text}")
                    return None

        except Exception as e:
            logger.error(f"Erreur lors de l'obtention du token INSEE: {e}")
            return None

    async def _make_request(self, endpoint: str, params: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Effectue une requête à l'API Sirene avec authentification"""
        token = await self._get_access_token()
        if not token:
            logger.error("Impossible d'obtenir un token d'accès")
            return None

        async with self._semaphore:
            # Rate limiting (30 requêtes par minute)
            current_time = asyncio.get_event_loop().time()
            time_since_last = current_time - self._last_request_time
            if time_since_last < (60.0 / self.rate_limit):
                await asyncio.sleep((60.0 / self.rate_limit) - time_since_last)

            self._last_request_time = asyncio.get_event_loop().time()

            headers = {"Authorization": f"Bearer {token}", "Accept": "application/json"}

            async with httpx.AsyncClient() as client:
                try:
                    response = await client.get(f"{self.base_url}/{endpoint}", params=params, headers=headers, timeout=30.0)

                    if response.status_code == 200:
                        return response.json()
                    elif response.status_code == 401:
                        # Token expiré, on le renouvelle
                        logger.warning("Token expiré, renouvellement...")
                        self._access_token = None
                        token = await self._get_access_token()
                        if token:
                            headers["Authorization"] = f"Bearer {token}"
                            response = await client.get(
                                f"{self.base_url}/{endpoint}", params=params, headers=headers, timeout=30.0
                            )
                            if response.status_code == 200:
                                return response.json()

                    logger.error(f"Erreur API Sirene: {response.status_code} - {response.text}")
                    return None

                except httpx.HTTPError as e:
                    logger.error(f"Erreur HTTP API Sirene: {e}")
                    return None

    def _format_naf_code(self, naf_code: str) -> str:
        """
        Formate un code NAF pour l'API Sirene (ajoute le point si nécessaire)

        Args:
            naf_code: Code NAF (ex: "6201Z" ou "62.01Z")

        Returns:
            Code NAF formaté (ex: "62.01Z")
        """
        if not naf_code:
            return naf_code

        # Si le code contient déjà un point, le retourner tel quel
        if "." in naf_code:
            return naf_code

        # Ajouter le point au bon endroit (ex: 6201Z -> 62.01Z)
        if len(naf_code) >= 4:
            return f"{naf_code[:2]}.{naf_code[2:]}"

        return naf_code

    def _map_company_size(self, employees: Optional[str]) -> Optional[CompanySize]:
        """Mappe les effectifs Sirene vers notre enum CompanySize"""
        if not employees:
            return None

        # Codes effectifs Sirene
        size_mapping = {
            "00": None,  # Non renseigné
            "01": CompanySize.MICRO,  # 0 salarié
            "02": CompanySize.MICRO,  # 1 ou 2 salariés
            "03": CompanySize.MICRO,  # 3 à 5 salariés
            "11": CompanySize.MICRO,  # 6 à 9 salariés
            "12": CompanySize.SMALL,  # 10 à 19 salariés
            "21": CompanySize.SMALL,  # 20 à 49 salariés
            "22": CompanySize.MEDIUM,  # 50 à 99 salariés
            "31": CompanySize.MEDIUM,  # 100 à 199 salariés
            "32": CompanySize.MEDIUM,  # 200 à 249 salariés
            "41": CompanySize.LARGE,  # 250 à 499 salariés
            "42": CompanySize.LARGE,  # 500 à 999 salariés
            "51": CompanySize.LARGE,  # 1000 à 1999 salariés
            "52": CompanySize.LARGE,  # 2000 à 4999 salariés
            "53": CompanySize.LARGE,  # 5000 à 9999 salariés
            "54": CompanySize.LARGE,  # 10000 salariés et plus
        }
        return size_mapping.get(employees)

    def _parse_company(self, etablissement: Dict[str, Any]) -> Company:
        """Parse les données Sirene en objet Company"""
        unite_legale = etablissement.get("uniteLegale", {})
        adresse = etablissement.get("adresseEtablissement", {})

        # Récupérer le SIREN depuis l'établissement ou l'unité légale
        siren = etablissement.get("siren") or unite_legale.get("siren")

        # Récupérer le nom de l'entreprise
        name = (
            unite_legale.get("denominationUniteLegale")
            or etablissement.get("denominationUsuelleEtablissement")
            or f"{unite_legale.get('prenom1UniteLegale', '')} {unite_legale.get('nomUniteLegale', '')}".strip()
        )

        # Si le nom est [ND] (non diffusé), essayer d'autres sources
        if name == "[ND]" or not name:
            # Essayer les enseignes
            enseigne = (
                etablissement.get("enseigne1Etablissement")
                or etablissement.get("enseigne2Etablissement")
                or etablissement.get("enseigne3Etablissement")
            )
            if enseigne and enseigne != "[ND]":
                name = enseigne
            else:
                name = f"Entreprise {siren}" if siren else "Entreprise inconnue"

        # Récupérer le code NAF depuis les périodes actuelles
        naf_code = None
        periodes = etablissement.get("periodesEtablissement", [])
        if periodes:
            # Prendre la période la plus récente (première dans la liste)
            periode_actuelle = periodes[0]
            naf_code = periode_actuelle.get("activitePrincipaleEtablissement")

        # Si pas trouvé, essayer depuis l'unité légale
        if not naf_code:
            naf_code = unite_legale.get("activitePrincipaleUniteLegale")

        # Construire l'adresse
        address_parts = []
        if adresse.get("numeroVoieEtablissement") and adresse.get("numeroVoieEtablissement") != "[ND]":
            address_parts.append(adresse["numeroVoieEtablissement"])
        if adresse.get("typeVoieEtablissement") and adresse.get("typeVoieEtablissement") != "[ND]":
            address_parts.append(adresse["typeVoieEtablissement"])
        if adresse.get("libelleVoieEtablissement") and adresse.get("libelleVoieEtablissement") != "[ND]":
            address_parts.append(adresse["libelleVoieEtablissement"])

        address = " ".join(address_parts) if address_parts else None

        return Company(
            siren=siren,
            siret=etablissement.get("siret"),
            name=name,
            legal_name=(
                unite_legale.get("denominationUniteLegale") if unite_legale.get("denominationUniteLegale") != "[ND]" else None
            ),
            # Adresse
            address=address,
            city=adresse.get("libelleCommuneEtablissement") if adresse.get("libelleCommuneEtablissement") != "[ND]" else None,
            postal_code=adresse.get("codePostalEtablissement") if adresse.get("codePostalEtablissement") != "[ND]" else None,
            region=adresse.get("libelleRegionEtablissement"),
            # Informations business
            naf_code=naf_code,
            employees=None,  # Pas directement disponible dans l'API établissements
            size=self._map_company_size(unite_legale.get("trancheEffectifsUniteLegale")),
            source=LeadSource.SIRENE,
        )

    async def search_companies(
        self,
        naf_codes: Optional[List[str]] = None,
        regions: Optional[List[str]] = None,
        min_employees: Optional[int] = None,
        max_employees: Optional[int] = None,
        limit: int = 100,
    ) -> AsyncGenerator[Company, None]:
        """
        Recherche d'entreprises créées récemment selon les critères

        Args:
            naf_codes: Codes NAF/APE à cibler (optionnel - tous secteurs si None)
            regions: Codes région INSEE (optionnel)
            min_employees: Effectif minimum
            max_employees: Effectif maximum
            limit: Nombre maximum de résultats par requête
        """
        if not self.consumer_key or not self.consumer_secret:
            logger.error("Clés INSEE manquantes, impossible de faire la recherche")
            return

        cursor = "*"
        total_processed = 0

        while cursor and total_processed < 1000:  # Limite de sécurité
            # Construction de la requête simplifiée - PRIORITÉ: entreprises du mois en cours
            query_parts = []

            # PRIORITÉ 1: Entreprises créées dans le mois en cours
            current_date = datetime.now()
            start_of_month = current_date.replace(day=1).strftime("%Y-%m-%d")
            end_of_month = current_date.strftime("%Y-%m-%d")
            query_parts.append(f"dateCreationUniteLegale:[{start_of_month} TO {end_of_month}]")

            # PRIORITÉ 2: Géographie par codes de commune (si spécifié)
            if regions:
                # Convertir les codes région en codes de commune
                commune_codes = []
                for region in regions:
                    if region == "11":  # Île-de-France
                        commune_codes.extend(["75*", "92*", "93*", "94*", "95*", "77*", "78*", "91*"])
                    elif region == "84":  # Auvergne-Rhône-Alpes
                        commune_codes.extend(["69*", "38*", "73*", "74*"])
                    elif region == "93":  # Provence-Alpes-Côte d'Azur
                        commune_codes.extend(["13*", "83*", "84*", "06*", "04*", "05*"])
                    elif region == "76":  # Occitanie
                        commune_codes.extend(["31*", "34*", "11*", "66*"])
                    elif region == "75":  # Nouvelle-Aquitaine
                        commune_codes.extend(["33*", "64*", "40*", "47*"])
                    else:
                        # Pour les autres régions, utiliser le code tel quel comme préfixe
                        commune_codes.append(f"{region}*")

                if commune_codes:
                    commune_query = " OR ".join([f"codeCommuneEtablissement:{code}" for code in commune_codes])
                    query_parts.append(f"({commune_query})")

            # État administratif actif
            query_parts.append("etatAdministratifUniteLegale:A")

            # Joindre avec AND
            query = " AND ".join(query_parts)

            params = {"q": query, "nombre": min(limit, 1000 - total_processed)}

            # Curseur pour la pagination
            if cursor != "*":
                params["curseur"] = cursor

            try:
                data = await self._make_request("siret", params)
                if not data:
                    break

                etablissements = data.get("etablissements", [])

                if not etablissements:
                    break

                for etab in etablissements:
                    try:
                        company = self._parse_company(etab)

                        # Filtrage post-traitement (effectifs)
                        if min_employees and company.size:
                            size_min_map = {
                                CompanySize.MICRO: 1,
                                CompanySize.SMALL: 10,
                                CompanySize.MEDIUM: 50,
                                CompanySize.LARGE: 250,
                            }
                            if size_min_map.get(company.size, 0) < min_employees:
                                continue

                        if max_employees and company.size:
                            size_max_map = {
                                CompanySize.MICRO: 9,
                                CompanySize.SMALL: 49,
                                CompanySize.MEDIUM: 249,
                                CompanySize.LARGE: 10000,
                            }
                            if size_max_map.get(company.size, 10000) > max_employees:
                                continue

                        # Filtrage post-traitement (NAF codes si spécifiés)
                        if naf_codes and company.naf_code:
                            # Vérifier si le code NAF de l'entreprise correspond à un des codes recherchés
                            naf_match = False
                            for naf in naf_codes:
                                # Normaliser les codes (enlever les points pour la comparaison)
                                company_naf = company.naf_code.replace(".", "")
                                search_naf = naf.replace(".", "")
                                if company_naf.startswith(search_naf) or search_naf.startswith(company_naf):
                                    naf_match = True
                                    break
                            if not naf_match:
                                continue

                        yield company
                        total_processed += 1

                    except Exception as e:
                        logger.warning(f"Erreur parsing entreprise: {e}")
                        continue

                # Pagination
                cursor = data.get("header", {}).get("curseurSuivant")
                if not cursor:
                    break

            except Exception as e:
                logger.error(f"Erreur recherche entreprises récentes: {e}")
                break

    async def test_connection(self) -> bool:
        """
        Teste la connexion à l'API Sirene

        Returns:
            True si la connexion fonctionne
        """
        try:
            token = await self._get_access_token()
            if not token:
                return False

            # Test simple avec une requête très basique
            data = await self._make_request("siret", {"q": "etatAdministratifUniteLegale:A", "nombre": 1})

            return data is not None

        except Exception as e:
            logger.error(f"Erreur test connexion: {e}")
            return False
