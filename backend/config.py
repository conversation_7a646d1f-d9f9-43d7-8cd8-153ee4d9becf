"""
Configuration centralisée pour le système de prospection B2B
"""

import os
from pathlib import Path
from dotenv import load_dotenv

# Charger les variables d'environnement depuis le fichier .env
env_path = Path(__file__).parent / '.env'
load_dotenv(env_path)

class Config:
    """Configuration centralisée"""
    
    # OpenAI
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
    
    # Email SMTP
    SMTP_HOST = os.getenv("SMTP_HOST", "smtp.gmail.com")
    SMTP_PORT = int(os.getenv("SMTP_PORT", 587))
    SMTP_USERNAME = os.getenv("SMTP_USERNAME")
    SMTP_PASSWORD = os.getenv("SMTP_PASSWORD")
    SMTP_FROM_EMAIL = os.getenv("SMTP_FROM_EMAIL")
    SMTP_FROM_NAME = os.getenv("SMTP_FROM_NAME", "Sami Rochdi")
    EMAIL_USE_TLS = os.getenv("EMAIL_USE_TLS", "True").lower() == "true"
    
    # SendGrid
    SENDGRID_API_KEY = os.getenv("SENDGRID_API_KEY")
    
    # INSEE API
    INSEE_CONSUMER_KEY = os.getenv("INSEE_CONSUMER_KEY")
    INSEE_CONSUMER_SECRET = os.getenv("INSEE_CONSUMER_SECRET")
    INSEE_TOKEN_URL = os.getenv("INSEE_TOKEN_URL", "https://api.insee.fr/token")
    INSEE_API_BASE_URL = os.getenv("INSEE_API_BASE_URL", "https://api.insee.fr/entreprises/sirene/V3.11")
    
    # Base de données
    DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///prospection.db")
    DATABASE_TYPE = os.getenv("DATABASE_TYPE", "sqlite").lower()
    
    # MySQL
    MYSQL_HOST = os.getenv("MYSQL_HOST", "127.0.0.1")
    MYSQL_PORT = int(os.getenv("MYSQL_PORT", 3306))
    MYSQL_USER = os.getenv("MYSQL_USER", "root")
    MYSQL_PASSWORD = os.getenv("MYSQL_PASSWORD", "")
    MYSQL_DATABASE = os.getenv("MYSQL_DATABASE", "prospection_b2b")
    
    # Rate Limiting
    API_RATE_LIMIT = int(os.getenv("API_RATE_LIMIT", 10))
    EMAIL_RATE_LIMIT = int(os.getenv("EMAIL_RATE_LIMIT", 5))
    
    # Logging
    LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
    LOG_FILE = os.getenv("LOG_FILE", "logs/prospection.log")
    
    # Informations entreprise
    COMPANY_NAME = os.getenv("COMPANY_NAME", "SL Conception")
    COMPANY_PHONE = os.getenv("COMPANY_PHONE", "06 23 31 58 39")
    COMPANY_EMAIL = os.getenv("COMPANY_EMAIL", "<EMAIL>")
    COMPANY_WEBSITE = os.getenv("COMPANY_WEBSITE", "www.slconception.fr")
    
    # Informations utilisateur
    USER_FIRST_NAME = os.getenv("USER_FIRST_NAME", "Sami")
    USER_LAST_NAME = os.getenv("USER_LAST_NAME", "Rochdi")
    USER_FULL_NAME = os.getenv("USER_FULL_NAME", "Sami Rochdi")
    USER_TITLE = os.getenv("USER_TITLE", "Développeur Web & Applications")
    USER_SIGNATURE = os.getenv("USER_SIGNATURE", 
        "Rochdi Sami\nDéveloppeur Web – Entreprise Rochdi Sami\n📞 06 23 31 58 39\n📧 <EMAIL>\n🌐 www.slconception.fr")
    
    @classmethod
    def get_mysql_url(cls) -> str:
        """Retourne l'URL de connexion MySQL"""
        return f"mysql+aiomysql://{cls.MYSQL_USER}:{cls.MYSQL_PASSWORD}@{cls.MYSQL_HOST}:{cls.MYSQL_PORT}/{cls.MYSQL_DATABASE}?charset=utf8mb4"
    
    @classmethod
    def validate_config(cls) -> dict:
        """Valide la configuration et retourne les erreurs"""
        errors = []
        warnings = []
        
        # Vérifications critiques
        if not cls.OPENAI_API_KEY:
            errors.append("OPENAI_API_KEY manquante")
        
        if not cls.SMTP_USERNAME or not cls.SMTP_PASSWORD:
            errors.append("Configuration SMTP incomplète (SMTP_USERNAME/SMTP_PASSWORD)")
        
        # Vérifications optionnelles
        if not cls.INSEE_CONSUMER_KEY or not cls.INSEE_CONSUMER_SECRET:
            warnings.append("Clés INSEE manquantes - API Sirene indisponible")
        
        if not cls.SENDGRID_API_KEY:
            warnings.append("Clé SendGrid manquante - seul SMTP disponible")
        
        if cls.DATABASE_TYPE == "mysql" and not cls.MYSQL_PASSWORD:
            warnings.append("Mot de passe MySQL manquant")
        
        return {
            "errors": errors,
            "warnings": warnings,
            "valid": len(errors) == 0
        }
    
    @classmethod
    def print_config_status(cls):
        """Affiche le statut de la configuration"""
        validation = cls.validate_config()
        
        print("🔧 Configuration du système:")
        print(f"   - OpenAI: {'✅' if cls.OPENAI_API_KEY else '❌'}")
        print(f"   - SMTP: {'✅' if cls.SMTP_USERNAME and cls.SMTP_PASSWORD else '❌'}")
        print(f"   - INSEE: {'✅' if cls.INSEE_CONSUMER_KEY and cls.INSEE_CONSUMER_SECRET else '⚠️'}")
        print(f"   - SendGrid: {'✅' if cls.SENDGRID_API_KEY else '⚠️'}")
        print(f"   - MySQL: {'✅' if cls.DATABASE_TYPE == 'mysql' and cls.MYSQL_PASSWORD else '⚠️'}")
        
        if validation["warnings"]:
            print("\n⚠️ Avertissements:")
            for warning in validation["warnings"]:
                print(f"   - {warning}")
        
        if validation["errors"]:
            print("\n❌ Erreurs:")
            for error in validation["errors"]:
                print(f"   - {error}")
        
        return validation["valid"]

# Instance globale de configuration
config = Config()

# Validation automatique au chargement
if __name__ == "__main__":
    config.print_config_status()
