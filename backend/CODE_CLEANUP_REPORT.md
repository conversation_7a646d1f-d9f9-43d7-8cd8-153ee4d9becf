# 🧹 RAPPORT DE NETTOYAGE DU CODE
==================================================

## 📊 RÉSUMÉ
- Fichiers analysés: 50
- Fichiers avec imports inutilisés: 0
- Code dupliqué détecté: 24 instances
- Fonctions trop longues: 19
- Problèmes de nommage: 0

## 📏 FONCTIONS TROP LONGUES
- /home/<USER>/rochdi/projetPerso/prospection_v2/test_free_apis.py:269 - display_results() (56 lignes)
- /home/<USER>/rochdi/projetPerso/prospection_v2/test_imap_tracker.py:116 - check_replies() (96 lignes)
- /home/<USER>/rochdi/projetPerso/prospection_v2/utils.py:223 - calculate_lead_score() (53 lignes)
- /home/<USER>/rochdi/projetPerso/prospection_v2/run_api.py:15 - main() (53 lignes)
- /home/<USER>/rochdi/projetPerso/prospection_v2/cleanup_code.py:285 - generate_report() (53 lignes)
- /home/<USER>/rochdi/projetPerso/prospection_v2/sender/email_sender.py:26 - __init__() (57 lignes)
- /home/<USER>/rochdi/projetPerso/prospection_v2/sender/followup_manager.py:161 - generate_followup_template() (52 lignes)
- /home/<USER>/rochdi/projetPerso/prospection_v2/sender/email_tracker.py:136 - _process_email() (61 lignes)
- /home/<USER>/rochdi/projetPerso/prospection_v2/mailer/template_manager.py:29 - _load_default_templates() (63 lignes)
- /home/<USER>/rochdi/projetPerso/prospection_v2/mailer/template_manager.py:93 - select_best_template() (51 lignes)
- /home/<USER>/rochdi/projetPerso/prospection_v2/mailer/email_generator.py:49 - _build_system_prompt() (59 lignes)
- /home/<USER>/rochdi/projetPerso/prospection_v2/lead_generator/sirene_auth.py:178 - _parse_company() (66 lignes)
- /home/<USER>/rochdi/projetPerso/prospection_v2/lead_generator/data_gouv_api.py:143 - _parse_etablissement() (62 lignes)
- /home/<USER>/rochdi/projetPerso/prospection_v2/lead_generator/recent_companies_api.py:109 - _parse_bodacc_company() (71 lignes)
- /home/<USER>/rochdi/projetPerso/prospection_v2/lead_generator/alternative_sources.py:79 - _parse_pages_jaunes_result() (58 lignes)
- /home/<USER>/rochdi/projetPerso/prospection_v2/collector/tech_analyzer.py:22 - _load_tech_signatures() (93 lignes)
- /home/<USER>/rochdi/projetPerso/prospection_v2/collector/company_analyzer.py:99 - _calculate_lead_score() (75 lignes)
- /home/<USER>/rochdi/projetPerso/prospection_v2/collector/pain_detector.py:19 - _load_pain_point_rules() (133 lignes)
- /home/<USER>/rochdi/projetPerso/prospection_v2/collector/pain_detector.py:169 - _analyze_website_quality() (56 lignes)

## 🔧 RÉSULTATS DU LINTING
### FLAKE8: ERREUR
```
[Errno 2] No such file or directory: 'flake8'
```

### BLACK: ERREUR
```
[Errno 2] No such file or directory: 'black'
```

### ISORT: ERREUR
```
[Errno 2] No such file or directory: 'isort'
```
