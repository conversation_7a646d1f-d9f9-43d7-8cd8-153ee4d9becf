# Configuration par défaut pour la prospection B2B

# Filtres de recherche par défaut
search_filters:
  sirene:
    # Codes NAF/APE ciblés (format avec points - réels dans l'API Sirene)
    naf_codes:
      - "62.01Z"  # Programmation informatique
      - "62.02A"  # Conseil en systèmes et logiciels informatiques
      - "62.02B"  # Tierce maintenance de systèmes et d'applications informatiques
      - "62.03Z"  # Gestion d'installations informatiques
      - "62.09Z"  # Autres activités informatiques
      - "70.22Z"  # Conseil pour les affaires et autres conseils de gestion
      - "73.11Z"  # Activités des agences de publicité
      - "82.11Z"  # Services administratifs combinés de bureau
    
    # Taille d'entreprise (effectifs)
    min_employees: 5
    max_employees: 500
    
    # Régions prioritaires (codes INSEE)
    regions:
      - "11"  # Île-de-France
      - "84"  # Auvergne-Rhône-Alpes
      - "93"  # Provence-Alpes-Côte d'Azur
      - "76"  # Occitanie
      - "75"  # Nouvelle-Aquitaine

# Configuration OpenAI
openai:
  model: "gpt-4o"
  temperature: 0.7
  max_tokens: 2200
  
# Templates d'emails
email_templates:
  system_prompt: |
    Tu es un copywriter B2B francophone expert.
    Rédige un e-mail de prospection court (moins de 2200 caractères), 
    ton professionnel et chaleureux, vouvoiement obligatoire.
    
    Contexte entreprise : {company_context}
    
    Structure attendue :
    1. Accroche personnalisée basée sur l'activité de l'entreprise
    2. Identification d'un pain point spécifique
    3. Proposition de solution adaptée (app métier, refonte site, intranet, etc.)
    4. 2-3 bénéfices concrets
    5. Call-to-action simple
    6. Signature avec nos coordonnées
    
    Signature obligatoire :
    Sami Rochdi – 06 23 31 58 39 – <EMAIL> – www.slconception.fr

# Configuration de scraping
scraping:
  timeout: 30
  max_retries: 3
  user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
  
# Pain points à détecter
pain_points:
  website:
    - "site obsolète"
    - "design dépassé"
    - "non responsive"
    - "lent"
  technology:
    - "Excel"
    - "fichiers partagés"
    - "processus manuels"
    - "pas de CRM"
  
# Limites de taux (APIs gratuites)
rate_limits:
  sirene_api: 7  # requêtes par seconde
  pages_jaunes: 0.5  # requêtes par seconde (très conservateur)
  societe_com: 1  # requêtes par seconde
  openai_api: 60  # requêtes par minute
  email_sending: 5  # emails par minute

# Sources de leads gratuites
free_sources:
  enabled:
    - "sirene"  # API Sirene (gratuite)
    - "opendatasoft"  # OpenDataSoft (gratuite)
    - "api_entreprise"  # API Entreprise gouv (gratuite)

  # Sources alternatives (à utiliser avec modération)
  alternative:
    - "pages_jaunes"  # Scraping Pages Jaunes
    - "societe_com"  # Scraping Societe.com

  # Mots-clés pour les recherches alternatives
  activity_keywords:
    - "développement web"
    - "création site internet"
    - "développement logiciel"
    - "conseil informatique"
    - "agence web"
