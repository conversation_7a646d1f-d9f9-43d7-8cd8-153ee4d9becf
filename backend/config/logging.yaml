version: 1
disable_existing_loggers: false

formatters:
  standard:
    format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    datefmt: '%Y-%m-%d %H:%M:%S'
  
  detailed:
    format: '%(asctime)s - %(name)s - %(levelname)s - %(module)s:%(funcName)s:%(lineno)d - %(message)s'
    datefmt: '%Y-%m-%d %H:%M:%S'
  
  json:
    format: '{"timestamp": "%(asctime)s", "logger": "%(name)s", "level": "%(levelname)s", "module": "%(module)s", "function": "%(funcName)s", "line": %(lineno)d, "message": "%(message)s"}'
    datefmt: '%Y-%m-%d %H:%M:%S'

handlers:
  console:
    class: logging.StreamHandler
    level: INFO
    formatter: standard
    stream: ext://sys.stdout
  
  file_info:
    class: logging.handlers.RotatingFileHandler
    level: INFO
    formatter: detailed
    filename: logs/prospection.log
    maxBytes: 10485760  # 10MB
    backupCount: 5
    encoding: utf8
  
  file_error:
    class: logging.handlers.RotatingFileHandler
    level: ERROR
    formatter: detailed
    filename: logs/errors.log
    maxBytes: 10485760  # 10MB
    backupCount: 5
    encoding: utf8
  
  file_debug:
    class: logging.handlers.RotatingFileHandler
    level: DEBUG
    formatter: json
    filename: logs/debug.log
    maxBytes: 10485760  # 10MB
    backupCount: 3
    encoding: utf8

loggers:
  # Logger principal
  prospection:
    level: INFO
    handlers: [console, file_info, file_error]
    propagate: false
  
  # Loggers par module
  lead_generator:
    level: INFO
    handlers: [file_info, file_error]
    propagate: false
  
  mailer:
    level: INFO
    handlers: [file_info, file_error]
    propagate: false
  
  sender:
    level: INFO
    handlers: [file_info, file_error]
    propagate: false
  
  collector:
    level: INFO
    handlers: [file_info, file_error]
    propagate: false
  
  # Logger pour les APIs externes
  httpx:
    level: WARNING
    handlers: [file_error]
    propagate: false
  
  openai:
    level: INFO
    handlers: [file_info, file_error]
    propagate: false
  
  # Logger pour les requêtes SQL
  sqlalchemy:
    level: WARNING
    handlers: [file_error]
    propagate: false

root:
  level: INFO
  handlers: [console, file_info, file_error]
