# Configuration optimisée pour les APIs gratuites uniquement
# Copier ce fichier vers config.yaml pour l'utiliser

# Filtres de recherche optimisés
search_filters:
  sirene:
    # Codes NAF ciblés (développement web et conseil IT)
    naf_codes:
      - "6201Z"  # Programmation informatique
      - "6202A"  # Conseil en systèmes et logiciels informatiques
      - "6202B"  # Tierce maintenance de systèmes et d'applications informatiques
      - "7022Z"  # Conseil pour les affaires et autres conseils de gestion
      - "7311Z"  # Activités des agences de publicité
      - "7312Z"  # Régie publicitaire de médias
    
    # Filtres géographiques (régions principales)
    regions:
      - "11"  # Île-de-France
      - "84"  # Auvergne-Rhône-Alpes
      - "93"  # Provence-Alpes-Côte d'Azur
      - "76"  # Occitanie
      - "52"  # Pays de la Loire
    
    # Filtres par taille (PME ciblées)
    min_employees: 5
    max_employees: 250
    
    # Départements spécifiques (optionnel)
    departments:
      - "75"  # Paris
      - "69"  # Rhône
      - "13"  # Bouches-du-Rhône
      - "31"  # Haute-Garonne
      - "44"  # Loire-Atlantique

# Configuration OpenAI
openai:
  model: "gpt-4o"
  temperature: 0.7
  max_tokens: 2200
  
  # Prompts optimisés pour les APIs gratuites
  system_prompt_additions:
    - "L'entreprise a été trouvée via des sources publiques gratuites"
    - "Mettre l'accent sur la valeur ajoutée et la personnalisation"
    - "Éviter les références à des outils payants"

# Sources de leads gratuites
free_sources:
  # Sources principales (toujours activées)
  primary:
    sirene:
      enabled: true
      rate_limit: 5  # req/s (conservateur)
      timeout: 30
    
    api_entreprise:
      enabled: true
      rate_limit: 2  # req/s
      timeout: 30
    
    opendatasoft:
      enabled: true
      rate_limit: 10  # req/s
      timeout: 30
  
  # Sources alternatives (scraping modéré)
  alternative:
    pages_jaunes:
      enabled: false  # Désactivé par défaut
      rate_limit: 0.3  # req/s (très conservateur)
      max_results_per_search: 20
      timeout: 45
    
    societe_com:
      enabled: false  # Désactivé par défaut
      rate_limit: 0.5  # req/s
      max_results_per_search: 30
      timeout: 45
  
  # Mots-clés pour recherches alternatives
  activity_keywords:
    - "développement web"
    - "création site internet"
    - "développement logiciel"
    - "conseil informatique"
    - "agence web"
    - "développement application"
    - "transformation digitale"

# Recherche d'emails gratuite
email_finding:
  # Scraping web intelligent
  web_scraping:
    enabled: true
    pages_to_check:
      - "/"
      - "/contact"
      - "/contact-us"
      - "/nous-contacter"
      - "/about"
      - "/a-propos"
      - "/equipe"
      - "/team"
      - "/mentions-legales"
    timeout: 15
    delay_between_pages: 0.5
  
  # Génération de patterns d'emails
  pattern_generation:
    enabled: true
    generic_prefixes:
      - "contact"
      - "info"
      - "hello"
      - "bonjour"
      - "commercial"
      - "vente"
    
    executive_prefixes:
      - "direction"
      - "directeur"
      - "president"
      - "ceo"
      - "pdg"
      - "gerant"
    
    technical_prefixes:
      - "tech"
      - "it"
      - "informatique"
      - "dev"
      - "webmaster"
  
  # Validation DNS
  dns_validation:
    enabled: true
    check_mx_records: true
    timeout: 10

# Rate limits conservateurs (respectueux des APIs)
rate_limits:
  # APIs officielles
  sirene_api: 5          # req/s (au lieu de 7 max)
  api_entreprise: 2      # req/s
  opendatasoft: 8        # req/s
  
  # Scraping (très conservateur)
  pages_jaunes: 0.3      # req/s
  societe_com: 0.5       # req/s
  web_scraping: 1        # req/s
  
  # OpenAI et envoi
  openai_api: 50         # req/min
  email_sending: 5       # emails/min
  
  # DNS et validation
  dns_queries: 10        # req/s
  domain_verification: 2  # req/s

# Configuration de l'enrichissement
enrichment:
  # Priorité des sources (ordre d'utilisation)
  source_priority:
    1: "sirene"
    2: "api_entreprise"
    3: "opendatasoft"
    4: "web_scraping"
    5: "pattern_generation"
  
  # Seuils de confiance
  confidence_thresholds:
    email_min_confidence: 60
    domain_min_confidence: 70
    company_data_min_confidence: 80
  
  # Timeouts
  timeouts:
    total_enrichment: 120  # secondes
    per_source: 30         # secondes
    email_finding: 45      # secondes

# Analyse et scoring
analysis:
  # Pondération du lead scoring (adapté aux APIs gratuites)
  scoring_weights:
    has_email: 20
    has_website: 15
    company_size_optimal: 15
    pain_points_detected: 25
    tech_stack_outdated: 10
    sector_match: 15
  
  # Seuils de priorité
  priority_thresholds:
    high: 75    # score >= 75
    medium: 50  # score >= 50
    low: 0      # score < 50

# Configuration de l'envoi
sending:
  # Limites anti-spam
  anti_spam:
    min_days_between_contacts: 90
    max_contacts_per_company: 3
    cache_ttl_hours: 24
  
  # Configuration SMTP (Gmail gratuit)
  smtp:
    rate_limit: 5  # emails/min
    batch_size: 10
    retry_attempts: 3
    retry_delay: 60  # secondes
  
  # Templates adaptés aux APIs gratuites
  templates:
    mention_free_research: true
    emphasize_personalization: true
    avoid_premium_references: true

# Logging et monitoring
logging:
  level: "INFO"
  
  # Métriques spécifiques aux APIs gratuites
  track_metrics:
    - "api_calls_per_source"
    - "rate_limit_hits"
    - "free_quota_usage"
    - "email_finding_success_rate"
    - "domain_verification_rate"
  
  # Alertes
  alerts:
    rate_limit_threshold: 0.8  # 80% du rate limit
    error_rate_threshold: 0.1  # 10% d'erreurs
    quota_usage_threshold: 0.9  # 90% du quota

# Optimisations performance
performance:
  # Cache
  cache:
    enabled: true
    ttl_hours: 24
    max_entries: 10000
  
  # Concurrence
  concurrency:
    max_concurrent_requests: 5
    max_concurrent_enrichments: 3
    max_concurrent_email_searches: 2
  
  # Batch processing
  batch_sizes:
    lead_generation: 50
    company_analysis: 20
    email_generation: 10
    email_sending: 5

# Développement et debug
development:
  # Mode debug pour les APIs gratuites
  debug_apis: false
  log_api_responses: false
  simulate_rate_limits: false
  
  # Tests
  test_mode:
    enabled: false
    mock_api_responses: false
    use_sample_data: false
