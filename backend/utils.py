"""
Fonctions utilitaires communes pour le projet de prospection B2B
"""

import asyncio
import csv
import json
import logging
import re
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional
from urllib.parse import urljoin, urlparse

from constants import (
    EMAIL_PATTERN,
    ERROR_MESSAGES,
    FRENCH_REGIONS,
    NAF_PATTERN,
    PHONE_PATTERN,
    SIREN_PATTERN,
    SIRET_PATTERN,
)

logger = logging.getLogger(__name__)


def validate_email(email: str) -> bool:
    """
    Valide une adresse email

    Args:
        email: Adresse email à valider

    Returns:
        True si l'email est valide
    """
    if not email or not isinstance(email, str):
        return False
    return bool(re.match(EMAIL_PATTERN, email.strip().lower()))


def validate_phone(phone: str) -> bool:
    """
    Valide un numéro de téléphone français

    Args:
        phone: Numéro de téléphone à valider

    Returns:
        True si le téléphone est valide
    """
    if not phone or not isinstance(phone, str):
        return False
    # Nettoyer le numéro (supprimer espaces, points, tirets)
    clean_phone = re.sub(r"[\s\.\-]", "", phone.strip())
    return bool(re.match(PHONE_PATTERN, clean_phone))


def validate_siren(siren: str) -> bool:
    """
    Valide un numéro SIREN

    Args:
        siren: Numéro SIREN à valider

    Returns:
        True si le SIREN est valide
    """
    if not siren or not isinstance(siren, str):
        return False
    clean_siren = siren.strip().replace(" ", "")
    return bool(re.match(SIREN_PATTERN, clean_siren))


def validate_siret(siret: str) -> bool:
    """
    Valide un numéro SIRET

    Args:
        siret: Numéro SIRET à valider

    Returns:
        True si le SIRET est valide
    """
    if not siret or not isinstance(siret, str):
        return False
    clean_siret = siret.strip().replace(" ", "")
    return bool(re.match(SIRET_PATTERN, clean_siret))


def validate_naf_code(naf_code: str) -> bool:
    """
    Valide un code NAF/APE

    Args:
        naf_code: Code NAF à valider

    Returns:
        True si le code NAF est valide
    """
    if not naf_code or not isinstance(naf_code, str):
        return False
    return bool(re.match(NAF_PATTERN, naf_code.strip().upper()))


def normalize_url(url: str) -> Optional[str]:
    """
    Normalise une URL

    Args:
        url: URL à normaliser

    Returns:
        URL normalisée ou None si invalide
    """
    if not url or not isinstance(url, str):
        return None

    url = url.strip()
    if not url:
        return None

    # Ajouter le protocole si manquant
    if not url.startswith(("http://", "https://")):
        url = f"https://{url}"

    try:
        parsed = urlparse(url)
        if not parsed.netloc:
            return None
        return url
    except Exception:
        return None


def extract_domain(url: str) -> Optional[str]:
    """
    Extrait le domaine d'une URL

    Args:
        url: URL dont extraire le domaine

    Returns:
        Domaine ou None si invalide
    """
    normalized_url = normalize_url(url)
    if not normalized_url:
        return None

    try:
        parsed = urlparse(normalized_url)
        return parsed.netloc.lower()
    except Exception:
        return None


def clean_company_name(name: str) -> str:
    """
    Nettoie le nom d'une entreprise

    Args:
        name: Nom à nettoyer

    Returns:
        Nom nettoyé
    """
    if not name or not isinstance(name, str):
        return ""

    # Supprimer les formes juridiques courantes
    legal_forms = [
        r"\bSARL\b",
        r"\bSAS\b",
        r"\bSA\b",
        r"\bEURL\b",
        r"\bSCI\b",
        r"\bSNC\b",
        r"\bSCS\b",
        r"\bSEP\b",
        r"\bGIE\b",
        r"\bEI\b",
        r"\bAUTO-ENTREPRENEUR\b",
        r"\bMICRO-ENTREPRISE\b",
    ]

    cleaned = name.strip()
    for form in legal_forms:
        cleaned = re.sub(form, "", cleaned, flags=re.IGNORECASE)

    # Nettoyer les espaces multiples
    cleaned = re.sub(r"\s+", " ", cleaned).strip()

    return cleaned


def format_phone_number(phone: str) -> Optional[str]:
    """
    Formate un numéro de téléphone français

    Args:
        phone: Numéro à formater

    Returns:
        Numéro formaté ou None si invalide
    """
    if not validate_phone(phone):
        return None

    # Nettoyer le numéro
    clean_phone = re.sub(r"[\s\.\-]", "", phone.strip())

    # Convertir format international vers national si nécessaire
    if clean_phone.startswith("+33"):
        clean_phone = "0" + clean_phone[3:]

    # Formater avec espaces
    if len(clean_phone) == 10:
        return f"{clean_phone[:2]} {clean_phone[2:4]} {clean_phone[4:6]} {clean_phone[6:8]} {clean_phone[8:]}"

    return clean_phone


def get_region_name(region_code: str) -> Optional[str]:
    """
    Récupère le nom d'une région depuis son code

    Args:
        region_code: Code région INSEE

    Returns:
        Nom de la région ou None si non trouvé
    """
    return FRENCH_REGIONS.get(region_code)


def calculate_lead_score(
    has_email: bool = False,
    has_website: bool = False,
    website_accessible: bool = False,
    pain_points_count: int = 0,
    technologies_count: int = 0,
    company_size: Optional[str] = None,
) -> int:
    """
    Calcule un score de lead basé sur différents critères

    Args:
        has_email: A une adresse email
        has_website: A un site web
        website_accessible: Site web accessible
        pain_points_count: Nombre de pain points détectés
        technologies_count: Nombre de technologies détectées
        company_size: Taille de l'entreprise

    Returns:
        Score de 0 à 100
    """
    score = 0

    # Email (+30 points)
    if has_email:
        score += 30

    # Site web (+20 points)
    if has_website:
        score += 20

        # Site accessible (+10 points bonus)
        if website_accessible:
            score += 10

    # Pain points (+5 points par pain point, max 20)
    score += min(pain_points_count * 5, 20)

    # Technologies (+2 points par techno, max 10)
    score += min(technologies_count * 2, 10)

    # Taille d'entreprise
    size_scores = {"micro": 5, "small": 10, "medium": 15, "large": 10}  # Moins intéressant pour une petite agence
    if company_size:
        score += size_scores.get(company_size.lower(), 0)

    return min(score, 100)


async def rate_limit(calls_per_minute: int, last_call_time: float) -> None:
    """
    Applique un rate limiting

    Args:
        calls_per_minute: Nombre d'appels autorisés par minute
        last_call_time: Timestamp du dernier appel
    """
    if calls_per_minute <= 0:
        return

    min_interval = 60.0 / calls_per_minute
    current_time = asyncio.get_event_loop().time()
    time_since_last = current_time - last_call_time

    if time_since_last < min_interval:
        sleep_time = min_interval - time_since_last
        await asyncio.sleep(sleep_time)


def safe_json_load(file_path: str) -> Optional[Dict[Any, Any]]:
    """
    Charge un fichier JSON de manière sécurisée

    Args:
        file_path: Chemin vers le fichier JSON

    Returns:
        Données JSON ou None si erreur
    """
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError, UnicodeDecodeError) as e:
        logger.error(f"Erreur chargement JSON {file_path}: {e}")
        return None


def safe_json_save(data: Any, file_path: str) -> bool:
    """
    Sauvegarde des données JSON de manière sécurisée

    Args:
        data: Données à sauvegarder
        file_path: Chemin vers le fichier JSON

    Returns:
        True si succès
    """
    try:
        # Créer le dossier parent si nécessaire
        Path(file_path).parent.mkdir(parents=True, exist_ok=True)

        with open(file_path, "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        logger.error(f"Erreur sauvegarde JSON {file_path}: {e}")
        return False


def truncate_text(text: str, max_length: int = 100, suffix: str = "...") -> str:
    """
    Tronque un texte à une longueur maximale

    Args:
        text: Texte à tronquer
        max_length: Longueur maximale
        suffix: Suffixe à ajouter si tronqué

    Returns:
        Texte tronqué
    """
    if not text or len(text) <= max_length:
        return text

    return text[: max_length - len(suffix)] + suffix


def format_duration(seconds: float) -> str:
    """
    Formate une durée en secondes en format lisible

    Args:
        seconds: Durée en secondes

    Returns:
        Durée formatée (ex: "2m 30s")
    """
    if seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        minutes = int(seconds // 60)
        remaining_seconds = int(seconds % 60)
        return f"{minutes}m {remaining_seconds}s"
    else:
        hours = int(seconds // 3600)
        remaining_minutes = int((seconds % 3600) // 60)
        return f"{hours}h {remaining_minutes}m"


def get_file_extension(file_path: str) -> str:
    """
    Récupère l'extension d'un fichier

    Args:
        file_path: Chemin vers le fichier

    Returns:
        Extension (avec le point)
    """
    return Path(file_path).suffix.lower()


def is_business_hours(dt: Optional[datetime] = None) -> bool:
    """
    Vérifie si on est en heures ouvrables (9h-18h, lun-ven)

    Args:
        dt: DateTime à vérifier (défaut: maintenant)

    Returns:
        True si heures ouvrables
    """
    if dt is None:
        dt = datetime.now()

    # Lundi = 0, Dimanche = 6
    if dt.weekday() >= 5:  # Weekend
        return False

    hour = dt.hour
    return 9 <= hour < 18
