#!/usr/bin/env python3
"""
Test de l'API OpenDataSoft comme alternative à Sirene
"""

import asyncio
import json

import httpx
from rich.console import Console
from rich.table import Table

console = Console()

class OpenDataSoftTester:
    """Testeur pour l'API OpenDataSoft"""
    
    def __init__(self):
        self.base_url = "https://public.opendatasoft.com/api/records/1.0"
        
    async def test_basic_search(self):
        """Test de recherche basique"""
        console.print("🔍 Test recherche basique OpenDataSoft...")
        
        try:
            url = f"{self.base_url}/search/"
            params = {
                "dataset": "sirene_v3",
                "q": "*",  # Recherche générale
                "rows": 5,
                "format": "json"
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.get(url, params=params, timeout=30.0)
                
                if response.status_code == 200:
                    data = response.json()
                    records = data.get('records', [])
                    console.print(f"✅ Recherche générale: {len(records)} résultats")
                    
                    if records:
                        # Afficher un exemple
                        example = records[0]['fields']
                        console.print(f"   Exemple: {example.get('l1_normalisee', 'N/A')}")
                        console.print(f"   SIREN: {example.get('siren', 'N/A')}")
                        console.print(f"   NAF: {example.get('activite_principale', 'N/A')}")
                        console.print(f"   Ville: {example.get('libelle_commune', 'N/A')}")
                        return True
                else:
                    console.print(f"❌ Erreur: {response.status_code}")
                    return False
                    
        except Exception as e:
            console.print(f"❌ Exception: {e}")
            return False
            
    async def test_naf_search(self):
        """Test de recherche par code NAF"""
        console.print("🔍 Test recherche par NAF...")
        
        naf_codes = ["6201Z", "6202A", "4711F", "5610A"]
        results = []
        
        for naf_code in naf_codes:
            try:
                url = f"{self.base_url}/search/"
                params = {
                    "dataset": "sirene_v3",
                    "q": f"activite_principale:{naf_code}",
                    "rows": 10,
                    "format": "json"
                }
                
                async with httpx.AsyncClient() as client:
                    response = await client.get(url, params=params, timeout=30.0)
                    
                    if response.status_code == 200:
                        data = response.json()
                        records = data.get('records', [])
                        count = len(records)
                        
                        example = ""
                        if records:
                            example = records[0]['fields'].get('l1_normalisee', 'N/A')
                            
                        results.append({
                            "naf": naf_code,
                            "count": count,
                            "example": example,
                            "status": "✅" if count > 0 else "❌"
                        })
                        
                        console.print(f"   {naf_code}: {count} résultats")
                        if records:
                            console.print(f"      Exemple: {example}")
                    else:
                        results.append({
                            "naf": naf_code,
                            "count": 0,
                            "example": f"Erreur {response.status_code}",
                            "status": "❌"
                        })
                        
                await asyncio.sleep(0.5)  # Rate limiting
                
            except Exception as e:
                results.append({
                    "naf": naf_code,
                    "count": 0,
                    "example": f"Exception: {e}",
                    "status": "❌"
                })
                
        return results
        
    async def test_geographic_search(self):
        """Test de recherche géographique"""
        console.print("🔍 Test recherche géographique...")
        
        locations = [
            ("Paris", "75"),
            ("Lyon", "69"),
            ("Marseille", "13"),
            ("Île-de-France", "11")
        ]
        
        results = []
        
        for city, code in locations:
            try:
                url = f"{self.base_url}/search/"
                
                # Tester différents champs géographiques
                queries = [
                    f"code_commune:{code}*",
                    f"libelle_commune:{city}",
                    f"code_postal:{code}*"
                ]
                
                best_result = {"count": 0, "query": ""}
                
                for query in queries:
                    params = {
                        "dataset": "sirene_v3",
                        "q": query,
                        "rows": 5,
                        "format": "json"
                    }
                    
                    async with httpx.AsyncClient() as client:
                        response = await client.get(url, params=params, timeout=30.0)
                        
                        if response.status_code == 200:
                            data = response.json()
                            count = len(data.get('records', []))
                            
                            if count > best_result["count"]:
                                best_result = {"count": count, "query": query}
                                
                    await asyncio.sleep(0.2)
                    
                results.append({
                    "location": f"{city} ({code})",
                    "count": best_result["count"],
                    "best_query": best_result["query"],
                    "status": "✅" if best_result["count"] > 0 else "❌"
                })
                
                console.print(f"   {city}: {best_result['count']} résultats")
                
            except Exception as e:
                results.append({
                    "location": f"{city} ({code})",
                    "count": 0,
                    "best_query": f"Erreur: {e}",
                    "status": "❌"
                })
                
        return results
        
    async def test_combined_search(self):
        """Test de recherche combinée (NAF + géographie)"""
        console.print("🔍 Test recherche combinée...")
        
        try:
            url = f"{self.base_url}/search/"
            params = {
                "dataset": "sirene_v3",
                "q": "activite_principale:6201Z AND code_commune:75*",  # Dev web à Paris
                "rows": 10,
                "format": "json"
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.get(url, params=params, timeout=30.0)
                
                if response.status_code == 200:
                    data = response.json()
                    records = data.get('records', [])
                    console.print(f"✅ Recherche combinée: {len(records)} résultats")
                    
                    if records:
                        for i, record in enumerate(records[:3]):
                            fields = record['fields']
                            console.print(f"   {i+1}. {fields.get('l1_normalisee', 'N/A')}")
                            console.print(f"      SIREN: {fields.get('siren', 'N/A')}")
                            console.print(f"      Ville: {fields.get('libelle_commune', 'N/A')}")
                            
                    return len(records) > 0
                else:
                    console.print(f"❌ Erreur: {response.status_code}")
                    return False
                    
        except Exception as e:
            console.print(f"❌ Exception: {e}")
            return False
            
    async def run_full_test(self):
        """Lance tous les tests"""
        console.print("🚀 Test complet de l'API OpenDataSoft")
        console.print("="*60)
        
        # Test basique
        basic_ok = await self.test_basic_search()
        
        # Test NAF
        naf_results = await self.test_naf_search()
        
        # Test géographique
        geo_results = await self.test_geographic_search()
        
        # Test combiné
        combined_ok = await self.test_combined_search()
        
        # Résumé
        console.print("\n" + "="*60)
        console.print("📊 RÉSUMÉ DES TESTS")
        
        # Tableau NAF
        if naf_results:
            table_naf = Table(title="Résultats par Code NAF")
            table_naf.add_column("Code NAF", style="bold")
            table_naf.add_column("Statut")
            table_naf.add_column("Résultats")
            table_naf.add_column("Exemple")
            
            for result in naf_results:
                table_naf.add_row(
                    result["naf"],
                    result["status"],
                    str(result["count"]),
                    result["example"][:30] + "..." if len(result["example"]) > 30 else result["example"]
                )
            
            console.print(table_naf)
        
        # Tableau géographique
        if geo_results:
            table_geo = Table(title="Résultats par Localisation")
            table_geo.add_column("Localisation", style="bold")
            table_geo.add_column("Statut")
            table_geo.add_column("Résultats")
            table_geo.add_column("Meilleure requête")
            
            for result in geo_results:
                table_geo.add_row(
                    result["location"],
                    result["status"],
                    str(result["count"]),
                    result["best_query"][:40] + "..." if len(result["best_query"]) > 40 else result["best_query"]
                )
            
            console.print(table_geo)
        
        # Recommandations
        console.print("\n💡 RECOMMANDATIONS:")
        
        working_naf = [r for r in naf_results if r["count"] > 0]
        working_geo = [r for r in geo_results if r["count"] > 0]
        
        if basic_ok and working_naf and working_geo and combined_ok:
            console.print("🎉 OpenDataSoft fonctionne parfaitement !")
            console.print("✅ Remplacer l'API Sirene par OpenDataSoft")
            console.print("✅ Syntaxe simple et fiable")
            console.print("✅ Pas d'authentification requise")
        elif basic_ok and (working_naf or working_geo):
            console.print("⚠️ OpenDataSoft fonctionne partiellement")
            console.print("✅ Peut être utilisé comme source principale")
            console.print("⚠️ Certaines requêtes à ajuster")
        else:
            console.print("❌ OpenDataSoft ne fonctionne pas correctement")
            console.print("🔄 Essayer d'autres alternatives")

async def main():
    """Point d'entrée principal"""
    tester = OpenDataSoftTester()
    await tester.run_full_test()

if __name__ == "__main__":
    asyncio.run(main())
