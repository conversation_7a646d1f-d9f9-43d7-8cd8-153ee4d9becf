#!/usr/bin/env python3
"""
CLI principal pour le système de prospection B2B
"""
import asyncio
import logging
import os
import sys
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional

import click
import yaml
from dotenv import load_dotenv
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.table import Table

from collector import CompanyAnalyzer
from lead_generator import LeadManager
from mailer import EmailGenerator

# Imports des modules
from models import Company, CompanyContext, EmailTemplate
from sender import AntiSpamManager, DatabaseManager, EmailSender
from sender.email_tracker import EmailTracker
from sender.email_tracking import EmailTrackingManager
from sender.followup_manager import FollowUpManager
from sender.simple_reply_checker import SimpleReplyChecker

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.FileHandler("logs/prospection.log"), logging.StreamHandler()],
)
logger = logging.getLogger(__name__)

# Console Rich
console = Console()

# Charger les variables d'environnement
load_dotenv()


class ProspectionCLI:
    """Interface CLI pour le système de prospection"""

    def __init__(self):
        """Initialise le CLI avec la configuration"""
        self.config = self._load_config()
        self.lead_manager = None
        self.company_analyzer = None
        self.email_generator = None
        self.db_manager = None
        self.anti_spam_manager = None
        self.email_sender = None

    def _load_config(self) -> dict:
        """Charge la configuration depuis config/config.yaml"""
        try:
            with open("config/config.yaml", "r", encoding="utf-8") as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            console.print("[red]Fichier config/config.yaml non trouvé![/red]")
            sys.exit(1)
        except Exception as e:
            console.print(f"[red]Erreur chargement config: {e}[/red]")
            sys.exit(1)

    async def _initialize_components(self):
        """Initialise tous les composants"""
        if self.lead_manager is not None:
            return  # Déjà initialisé

        console.print("[blue]Initialisation des composants...[/blue]")

        # Lead Manager (APIs gratuites uniquement)
        self.lead_manager = LeadManager(enable_enrichment=True)

        # Company Analyzer
        self.company_analyzer = CompanyAnalyzer()

        # Email Generator
        openai_key = os.getenv("OPENAI_API_KEY")
        if not openai_key:
            console.print("[red]OPENAI_API_KEY non configurée![/red]")
            sys.exit(1)

        self.email_generator = EmailGenerator(
            api_key=openai_key,
            model=self.config["openai"]["model"],
            temperature=self.config["openai"]["temperature"],
            max_tokens=self.config["openai"]["max_tokens"],
        )

        # Database Manager
        self.db_manager = DatabaseManager()
        await self.db_manager.initialize()

        # Anti-spam Manager
        self.anti_spam_manager = AntiSpamManager(self.db_manager)

        # Email Sender
        self.email_sender = EmailSender(
            db_manager=self.db_manager,
            anti_spam_manager=self.anti_spam_manager,
            smtp_host=os.getenv("SMTP_HOST"),
            smtp_port=int(os.getenv("SMTP_PORT", 587)),
            smtp_username=os.getenv("SMTP_USERNAME"),
            smtp_password=os.getenv("SMTP_PASSWORD"),
            sendgrid_api_key=os.getenv("SENDGRID_API_KEY"),
            from_email=os.getenv("SMTP_FROM_EMAIL", "<EMAIL>"),
            from_name=os.getenv("SMTP_FROM_NAME", "Sami Rochdi"),
            rate_limit=int(os.getenv("EMAIL_RATE_LIMIT", 5)),
        )

        console.print("[green]✓ Composants initialisés[/green]")

    async def generate_leads(
        self,
        max_results: int = 100,
        output_file: str = "leads.json",
        naf_codes: Optional[List[str]] = None,
        regions: Optional[List[str]] = None,
        recent_only: bool = False,
        days_back: int = 30,
    ):
        """Génère des leads avec filtres flexibles"""
        await self._initialize_components()

        # Configuration depuis config.yaml
        filters = self.config["search_filters"]["sirene"]

        # Utiliser les paramètres fournis ou ceux de la config
        # Si naf_codes est une liste vide, on cherche tous secteurs
        if naf_codes is not None:
            search_naf_codes = naf_codes if naf_codes else None  # Liste vide = tous secteurs
        else:
            # Par défaut, chercher TOUS secteurs (pas de filtrage NAF)
            search_naf_codes = None  # Recherche tous secteurs par défaut

        search_regions = regions if regions is not None else filters.get("regions")
        search_min_employees = filters.get("min_employees")
        search_max_employees = filters.get("max_employees")

        # Affichage des filtres
        filter_info = []
        if search_naf_codes:
            filter_info.append(f"NAF: {search_naf_codes}")
        else:
            filter_info.append("NAF: TOUS SECTEURS")

        if search_regions:
            filter_info.append(f"Régions: {search_regions}")

        if recent_only:
            filter_info.append(f"Récentes: {days_back} jours")

        console.print(f"[blue]Génération de {max_results} leads[/blue]")
        console.print(f"[cyan]Filtres: {' | '.join(filter_info)}[/cyan]")

        leads = []
        with Progress(SpinnerColumn(), TextColumn("[progress.description]{task.description}"), console=console) as progress:
            task = progress.add_task("Génération des leads...", total=None)

            # Utiliser la recherche flexible
            async for company in self.lead_manager.search_companies_flexible(
                naf_codes=search_naf_codes,
                regions=search_regions,
                min_employees=search_min_employees,
                max_employees=search_max_employees,
                recent_only=recent_only,
                days_back=days_back,
                max_results=max_results,
            ):
                leads.append(company)
                progress.update(task, description=f"Leads générés: {len(leads)}")

                if len(leads) >= max_results:
                    break

        # Exporter les leads
        await self.lead_manager.export_leads(leads, output_file)

        console.print(f"[green]✓ {len(leads)} leads générés et exportés vers {output_file}[/green]")

        # Afficher un échantillon
        if leads:
            self._display_leads_sample(leads[:5])

    def _display_leads_sample(self, leads: List[Company]):
        """Affiche un échantillon de leads"""
        table = Table(title="Échantillon de leads générés")
        table.add_column("Nom", style="cyan")
        table.add_column("Ville", style="green")
        table.add_column("NAF", style="yellow")
        table.add_column("Email", style="blue")
        table.add_column("Source", style="magenta")

        for lead in leads:
            table.add_row(
                lead.name[:40] + "..." if len(lead.name) > 40 else lead.name,
                lead.city or "N/A",
                lead.naf_code or "N/A",
                lead.email or "N/A",
                lead.source.value,
            )

        console.print(table)

    async def analyze_companies(
        self, input_file: str = "leads.json", output_file: str = "analyzed_companies.json", max_concurrent: int = 5
    ):
        """Analyse les entreprises"""
        await self._initialize_components()

        console.print(f"[blue]Analyse des entreprises depuis {input_file}...[/blue]")

        # Charger les leads
        companies = await self.lead_manager.load_leads(input_file)
        if not companies:
            console.print(f"[red]Aucune entreprise trouvée dans {input_file}[/red]")
            return

        console.print(f"[blue]Analyse de {len(companies)} entreprises...[/blue]")

        # Analyser les entreprises
        with Progress(SpinnerColumn(), TextColumn("[progress.description]{task.description}"), console=console) as progress:
            task = progress.add_task("Analyse en cours...", total=len(companies))

            contexts = await self.company_analyzer.batch_analyze_companies(companies, max_concurrent)

            progress.update(task, completed=len(companies))

        # Exporter les résultats
        contexts_data = []
        for context in contexts:
            context_dict = context.model_dump()

            # Convertir les dates et URLs
            def convert_values(obj):
                if isinstance(obj, dict):
                    return {k: convert_values(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [convert_values(item) for item in obj]
                elif hasattr(obj, "isoformat"):
                    return obj.isoformat()
                elif hasattr(obj, "__str__") and "http" in str(obj):
                    return str(obj)
                else:
                    return obj

            context_dict = convert_values(context_dict)
            contexts_data.append(context_dict)

        import json

        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(
                {"analyzed_at": asyncio.get_event_loop().time(), "total_companies": len(contexts), "contexts": contexts_data},
                f,
                indent=2,
                ensure_ascii=False,
            )

        console.print(f"[green]✓ {len(contexts)} entreprises analysées et exportées vers {output_file}[/green]")

        # Afficher les statistiques
        self._display_analysis_stats(contexts)

    def _display_analysis_stats(self, contexts: List[CompanyContext]):
        """Affiche les statistiques d'analyse"""
        # Calculer les statistiques
        total = len(contexts)
        with_email = len([c for c in contexts if c.company.email])
        high_priority = len([c for c in contexts if c.priority == "high"])
        medium_priority = len([c for c in contexts if c.priority == "medium"])

        avg_score = sum(c.lead_score or 0 for c in contexts) / total if total > 0 else 0

        # Créer le tableau
        table = Table(title="Statistiques d'analyse")
        table.add_column("Métrique", style="cyan")
        table.add_column("Valeur", style="green")

        table.add_row("Total entreprises", str(total))
        table.add_row("Avec email", f"{with_email} ({with_email/total*100:.1f}%)")
        table.add_row("Priorité haute", f"{high_priority} ({high_priority/total*100:.1f}%)")
        table.add_row("Priorité moyenne", f"{medium_priority} ({medium_priority/total*100:.1f}%)")
        table.add_row("Score moyen", f"{avg_score:.1f}/100")

        console.print(table)

    async def generate_emails(
        self,
        input_file: str = "analyzed_companies.json",
        output_file: str = "emails.json",
        max_concurrent: int = 3,
        skip_good_websites: bool = True,
    ):
        """Génère les emails personnalisés"""
        await self._initialize_components()

        console.print(f"[blue]Génération d'emails depuis {input_file}...[/blue]")

        # Charger les contextes analysés
        import json

        try:
            with open(input_file, "r", encoding="utf-8") as f:
                data = json.load(f)
            contexts_data = data.get("contexts", [])
        except FileNotFoundError:
            console.print(f"[red]Fichier {input_file} non trouvé![/red]")
            return

        # Reconstituer les objets CompanyContext
        contexts = []
        for context_dict in contexts_data:
            try:
                # Corriger la valeur source si nécessaire
                if "company" in context_dict and "source" in context_dict["company"]:
                    source_value = context_dict["company"]["source"]
                    if source_value == "generated_realistic":
                        # Utiliser la valeur enum correcte
                        from models import LeadSource
                        context_dict["company"]["source"] = LeadSource.GENERATED.value

                # Reconvertir les dates si nécessaire
                context = CompanyContext(**context_dict)
                contexts.append(context)
            except Exception as e:
                logger.warning(f"Erreur reconstruction contexte: {e}")
                continue

        if not contexts:
            console.print("[red]Aucun contexte valide trouvé![/red]")
            return

        console.print(f"[blue]Génération d'emails pour {len(contexts)} entreprises...[/blue]")

        # Générer les emails
        with Progress(SpinnerColumn(), TextColumn("[progress.description]{task.description}"), console=console) as progress:
            task = progress.add_task("Génération des emails...", total=len(contexts))

            templates, filtered_contexts = await self.email_generator.generate_batch_emails(
                contexts, max_concurrent, skip_good_websites=skip_good_websites
            )

            progress.update(task, completed=len(contexts))

        # Sauvegarder les emails
        await self.email_generator.save_emails_to_json(templates, filtered_contexts, output_file)

        console.print(f"[green]✓ {len(templates)} emails générés et sauvegardés vers {output_file}[/green]")

        # Afficher les statistiques de filtrage
        skipped_count = len(contexts) - len(filtered_contexts)
        if skipped_count > 0:
            console.print(f"[yellow]ℹ️  {skipped_count} entreprises ignorées (sites web corrects)[/yellow]")

        # Afficher un échantillon
        if templates and filtered_contexts:
            self._display_email_sample(templates[:3], filtered_contexts[:3])

    def _display_email_sample(self, templates: List[EmailTemplate], contexts: List[CompanyContext]):
        """Affiche un échantillon d'emails générés"""
        for i, (template, context) in enumerate(zip(templates, contexts)):
            panel_title = f"Email {i+1}: {context.company.name}"

            content = f"""[bold]Objet:[/bold] {template.subject}

[bold]Destinataire:[/bold] {context.company.email or 'N/A'}

[bold]Corps:[/bold]
{template.body[:500]}{'...' if len(template.body) > 500 else ''}

[bold]Score lead:[/bold] {context.lead_score}/100
[bold]Caractères:[/bold] {len(template.body)}"""

            console.print(Panel(content, title=panel_title, border_style="blue"))

    async def send_emails(
        self, input_file: str = "emails.json", dry_run: bool = True, use_sendgrid: bool = False, max_concurrent: int = 1
    ):
        """Envoie les emails"""
        await self._initialize_components()

        mode_text = "DRY RUN" if dry_run else "ENVOI RÉEL"
        provider_text = "SendGrid" if use_sendgrid else "SMTP"

        console.print(f"[blue]Envoi d'emails ({mode_text}) via {provider_text}...[/blue]")

        # Vérifier la configuration
        config_status = self.email_sender.validate_configuration()
        if not config_status["can_send"]:
            console.print("[red]Configuration d'envoi invalide![/red]")
            console.print("Vérifiez vos variables d'environnement SMTP ou SendGrid")
            return

        # Charger les emails
        import json

        try:
            with open(input_file, "r", encoding="utf-8") as f:
                data = json.load(f)
            emails_data = data.get("emails", [])
        except FileNotFoundError:
            console.print(f"[red]Fichier {input_file} non trouvé![/red]")
            return

        if not emails_data:
            console.print("[red]Aucun email trouvé![/red]")
            return

        console.print(f"[green]✓ {len(emails_data)} emails chargés depuis {input_file}[/green]")
        console.print(f"[yellow]Mode: {mode_text}[/yellow]")

        # Convertir les données en objets Company et EmailTemplate
        from datetime import datetime

        from models import Company, EmailTemplate, LeadSource

        companies_and_templates = []
        for email_data in emails_data:
            try:
                # Créer l'objet Company
                company_info = email_data["company"]
                # Construire l'URL complète si nécessaire
                website = company_info.get("domain")
                if website and not website.startswith(("http://", "https://")):
                    website = f"https://{website}"

                company = Company(
                    siren=company_info.get("siren"),
                    name=company_info["name"],
                    city=company_info.get("city"),
                    email=company_info.get("email"),
                    website=website,
                    source=LeadSource.MANUAL,
                )

                # Créer l'objet EmailTemplate
                email_info = email_data["email"]
                template = EmailTemplate(
                    subject=email_info["subject"],
                    body=email_info["body"],
                    generated_at=datetime.fromisoformat(email_info["generated_at"].replace("Z", "+00:00")),
                )

                companies_and_templates.append((company, template))

            except Exception as e:
                console.print(f"[red]Erreur parsing email: {e}[/red]")
                continue

        if not companies_and_templates:
            console.print("[red]Aucun email valide trouvé![/red]")
            return

        # Envoyer les emails
        console.print(f"\n[blue]Envoi de {len(companies_and_templates)} emails...[/blue]")

        with Progress(SpinnerColumn(), TextColumn("[progress.description]{task.description}"), console=console) as progress:
            task = progress.add_task("Envoi des emails...", total=None)

            sent_emails = await self.email_sender.send_batch_emails(
                companies_and_templates=companies_and_templates,
                use_sendgrid=use_sendgrid,
                dry_run=dry_run,
                max_concurrent=max_concurrent,
            )

            progress.update(task, description=f"Emails envoyés: {len(sent_emails)}")

        # Afficher les résultats
        from models import EmailStatus

        successful = sum(1 for email in sent_emails if email.status == EmailStatus.SENT)
        failed = len(sent_emails) - successful

        if dry_run:
            console.print(f"\n[green]✓ Simulation terminée:[/green]")
            console.print(f"  - {successful} emails auraient été envoyés")
            console.print(f"  - {failed} emails auraient échoué")

            # Afficher le détail du premier email
            if sent_emails:
                first_email = sent_emails[0]
                console.print(f"\n[bold]Aperçu du premier email:[/bold]")
                console.print(f"[cyan]À:[/cyan] {first_email.email_to}")
                console.print(f"[cyan]Objet:[/cyan] {first_email.subject}")
                console.print(f"[cyan]Corps:[/cyan] {first_email.body[:200]}...")
        else:
            console.print(f"\n[green]✓ Envoi terminé:[/green]")
            console.print(f"  - {successful} emails envoyés avec succès")
            console.print(f"  - {failed} emails échoués")

            if failed > 0:
                console.print(f"\n[red]Emails échoués:[/red]")
                for email in sent_emails:
                    if email.status != EmailStatus.SENT:
                        console.print(f"  - {email.email_to}: {email.error_message}")

        console.print(f"\n[blue]Logs détaillés disponibles dans la base de données[/blue]")

    async def check_replies(self, days_back: int = 7, auto_mode: bool = False):
        """Vérifie les réponses aux emails envoyés"""
        if auto_mode:
            console.print(f"[blue]Vérification automatique des réponses (IMAP)...[/blue]")
            await self._check_replies_automatic()
        else:
            console.print(f"[blue]Guide de surveillance des réponses ({days_back} derniers jours)...[/blue]")
            await self._check_replies_manual(days_back)

    async def _check_replies_automatic(self):
        """Vérification automatique via IMAP"""
        try:
            # Utiliser le tracker IMAP direct
            tracker = EmailTracker()
            replies = await tracker.check_replies(24)  # 24 heures

            if not replies:
                console.print("[green]✅ Aucune réponse détectée dans les dernières 24h[/green]")
                return

            console.print(f"[green]🎉 {len(replies)} réponse(s) détectée(s)![/green]")

            # Afficher les réponses
            for i, reply in enumerate(replies, 1):
                type_colors = {"positive": "green", "negative": "red", "neutral": "yellow", "auto_reply": "blue"}
                color = type_colors.get(reply.reply_type, "white")

                content = f"""[bold]De:[/bold] {reply.from_email}
[bold]Sujet:[/bold] {reply.subject}
[bold]Type:[/bold] [{color}]{reply.reply_type.upper()}[/{color}]
[bold]Reçu:[/bold] {reply.received_at.strftime('%d/%m/%Y %H:%M')}

[bold]Message:[/bold]
{reply.body[:300]}{'...' if len(reply.body) > 300 else ''}"""

                console.print(Panel(content, title=f"Réponse {i}", border_style=color))

            # Résumé par type
            types = {}
            for reply in replies:
                reply_type = reply.reply_type
                types[reply_type] = types.get(reply_type, 0) + 1

            if types:
                console.print("\n[bold]📊 Résumé par type:[/bold]")
                for reply_type, count in types.items():
                    emoji = {"positive": "✅", "negative": "❌", "neutral": "⚪", "auto_reply": "🤖"}.get(reply_type, "❓")
                    color = {"positive": "green", "negative": "red", "neutral": "yellow", "auto_reply": "blue"}.get(
                        reply_type, "white"
                    )
                    console.print(f"[{color}]{emoji} {reply_type.upper()}: {count}[/{color}]")

        except Exception as e:
            console.print(f"[red]Erreur lors de la vérification automatique: {e}[/red]")
            logger.error(f"Erreur check_replies_automatic: {e}")

    async def _check_replies_manual(self, days_back: int):
        """Vérification manuelle avec guide"""
        # Utiliser le checker simple
        checker = SimpleReplyChecker()

        try:
            # Générer le guide de surveillance
            guide = await checker.generate_reply_monitoring_guide(days_back)
            console.print(guide)

            # Afficher les statistiques
            console.print("\n[blue]Calcul des statistiques...[/blue]")
            stats = await checker.get_reply_statistics(30)

            if stats:
                self._display_reply_stats(stats)

        except Exception as e:
            console.print(f"[red]Erreur lors de la vérification: {e}[/red]")
            logger.error(f"Erreur check_replies: {e}")

    def _display_reply_stats(self, stats: Dict[str, Any]):
        """Affiche les statistiques de réponse"""
        table = Table(title="📊 Statistiques de Réponse (Estimées)")
        table.add_column("Métrique", style="cyan")
        table.add_column("Valeur", style="green")

        table.add_row("Période", f"{stats.get('period_days', 0)} jours")
        table.add_row("Emails envoyés", str(stats.get("total_emails_sent", 0)))
        table.add_row("Emails échoués", str(stats.get("total_emails_failed", 0)))
        table.add_row("Réponses estimées", str(stats.get("estimated_total_responses", 0)))
        table.add_row("Réponses positives estimées", str(stats.get("estimated_positive_responses", 0)))
        table.add_row("Taux de réponse estimé", stats.get("estimated_response_rate", "0%"))
        table.add_row("Taux positif estimé", stats.get("estimated_positive_rate", "0%"))

        console.print(table)
        console.print(f"[yellow]Note: {stats.get('note', '')}[/yellow]")

    async def process_followups(self, dry_run: bool = True):
        """Traite les relances automatiques"""
        console.print(f"[blue]Traitement des relances (dry_run={dry_run})...[/blue]")

        followup_manager = FollowUpManager()

        try:
            stats = await followup_manager.process_followups(dry_run)

            # Afficher les résultats
            table = Table(title="📧 Résultats du Traitement des Relances")
            table.add_column("Métrique", style="cyan")
            table.add_column("Valeur", style="green")

            table.add_row("Emails vérifiés", str(stats["emails_checked"]))
            table.add_row("Relances envoyées", str(stats["followups_sent"]))
            table.add_row("Relances échouées", str(stats["followups_failed"]))
            table.add_row("Mode", "DRY RUN" if stats["dry_run"] else "RÉEL")

            console.print(table)

            if stats["followups_sent"] > 0 and dry_run:
                console.print("\n[yellow]💡 Utilisez --real pour envoyer les relances réellement[/yellow]")
            elif stats["followups_sent"] > 0 and not dry_run:
                console.print("\n[green]✅ Relances envoyées avec succès![/green]")

        except Exception as e:
            console.print(f"[red]Erreur lors du traitement des relances: {e}[/red]")
            logger.error(f"Erreur process_followups: {e}")

    async def show_followup_stats(self, days_back: int = 30):
        """Affiche les statistiques de relance"""
        console.print(f"[blue]Statistiques de relance ({days_back} derniers jours)...[/blue]")

        followup_manager = FollowUpManager()

        try:
            stats = await followup_manager.get_followup_statistics(days_back)

            table = Table(title="📊 Statistiques de Relance")
            table.add_column("Métrique", style="cyan")
            table.add_column("Valeur", style="green")

            table.add_row("Période", f"{stats['period_days']} jours")
            table.add_row("Emails nécessitant relance", str(stats["emails_needing_followup"]))
            table.add_row("Relances envoyées", str(stats["followups_sent"]))
            table.add_row("Réponses après relance", str(stats["replies_after_followup"]))
            table.add_row("Taux de réponse relance", f"{stats['followup_response_rate']}%")

            console.print(table)

            if stats["emails_needing_followup"] > 0:
                console.print(f"\n[yellow]💡 {stats['emails_needing_followup']} emails nécessitent une relance[/yellow]")
                console.print("[blue]Utilisez 'python cli.py process-followups' pour les traiter[/blue]")

        except Exception as e:
            console.print(f"[red]Erreur lors de la récupération des statistiques: {e}[/red]")
            logger.error(f"Erreur show_followup_stats: {e}")

    async def show_tracking_stats(self, days_back: int = 30):
        """Affiche les statistiques de tracking"""
        console.print(f"[blue]Statistiques de tracking ({days_back} derniers jours)...[/blue]")

        try:
            import aiomysql

            connection = await aiomysql.connect(
                host="127.0.0.1", port=3306, user="root", password="MLKqsd002", db="prospection_b2b", charset="utf8mb4"
            )

            try:
                async with connection.cursor() as cursor:
                    cutoff_date = datetime.now() - timedelta(days=days_back)

                    # Emails avec ouvertures
                    await cursor.execute(
                        """
                        SELECT COUNT(*) as count FROM sent_emails
                        WHERE sent_at >= %s AND opened = TRUE
                    """,
                        (cutoff_date,),
                    )
                    result = await cursor.fetchone()
                    opened_count = result[0]

                    # Emails avec clics
                    await cursor.execute(
                        """
                        SELECT COUNT(*) as count FROM sent_emails
                        WHERE sent_at >= %s AND clicked = TRUE
                    """,
                        (cutoff_date,),
                    )
                    result = await cursor.fetchone()
                    clicked_count = result[0]

                    # Total emails envoyés
                    await cursor.execute(
                        """
                        SELECT COUNT(*) as count FROM sent_emails
                        WHERE sent_at >= %s AND status = 'sent'
                    """,
                        (cutoff_date,),
                    )
                    result = await cursor.fetchone()
                    total_sent = result[0]

                    # Calculer les taux
                    open_rate = round((opened_count / total_sent * 100) if total_sent > 0 else 0, 2)
                    click_rate = round((clicked_count / total_sent * 100) if total_sent > 0 else 0, 2)

                    table = Table(title="📊 Statistiques de Tracking")
                    table.add_column("Métrique", style="cyan")
                    table.add_column("Valeur", style="green")

                    table.add_row("Période", f"{days_back} jours")
                    table.add_row("Emails envoyés", str(total_sent))
                    table.add_row("Emails ouverts", str(opened_count))
                    table.add_row("Emails cliqués", str(clicked_count))
                    table.add_row("Taux d'ouverture", f"{open_rate}%")
                    table.add_row("Taux de clic", f"{click_rate}%")

                    console.print(table)
            finally:
                connection.close()

        except Exception as e:
            console.print(f"[red]Erreur lors de la récupération des statistiques: {e}[/red]")
            logger.error(f"Erreur show_tracking_stats: {e}")

    async def show_statistics(self, days_back: int = 30):
        """Affiche les statistiques"""
        await self._initialize_components()

        console.print(f"[blue]Statistiques des {days_back} derniers jours[/blue]")

        # Statistiques d'envoi
        stats = await self.email_sender.get_sending_statistics(days_back)

        if stats:
            table = Table(title="Statistiques d'envoi")
            table.add_column("Métrique", style="cyan")
            table.add_column("Valeur", style="green")

            table.add_row("Total envoyés", str(stats.get("total_sent", 0)))
            table.add_row("Réussis", str(stats.get("successful", 0)))
            table.add_row("Échoués", str(stats.get("failed", 0)))
            table.add_row("Taux de succès", f"{stats.get('success_rate', 0):.1f}%")

            console.print(table)


# Instance globale du CLI
cli_instance = ProspectionCLI()


# Commandes Click
@click.group()
def cli():
    """Système de prospection B2B automatisée"""
    pass


@cli.command()
@click.option("--max-results", default=100, help="Nombre maximum de leads")
@click.option("--output", default="leads.json", help="Fichier de sortie")
@click.option("--naf-codes", help="Codes NAF séparés par des virgules (ex: 6201Z,6202A)")
@click.option("--regions", help="Codes région séparés par des virgules (ex: 11,84)")
@click.option("--recent-only", is_flag=True, help="Chercher uniquement les entreprises récentes")
@click.option("--days-back", default=30, help="Nombre de jours en arrière pour --recent-only")
def generate_leads(max_results, output, naf_codes, regions, recent_only, days_back):
    """Génère des leads depuis les APIs avec filtres flexibles"""
    # Convertir les chaînes en listes
    # Si naf_codes est une chaîne vide, on veut None (tous secteurs)
    naf_list = naf_codes.split(",") if naf_codes and naf_codes.strip() else None
    regions_list = regions.split(",") if regions and regions.strip() else None

    asyncio.run(
        cli_instance.generate_leads(
            max_results=max_results,
            output_file=output,
            naf_codes=naf_list,
            regions=regions_list,
            recent_only=recent_only,
            days_back=days_back,
        )
    )


@cli.command()
@click.option("--input", default="leads.json", help="Fichier d'entrée")
@click.option("--output", default="analyzed_companies.json", help="Fichier de sortie")
@click.option("--concurrent", default=5, help="Analyses simultanées")
def analyze(input, output, concurrent):
    """Analyse les entreprises (sites web, stack technique, pain points)"""
    asyncio.run(cli_instance.analyze_companies(input, output, concurrent))


@cli.command()
@click.option("--input", default="analyzed_companies.json", help="Fichier d'entrée")
@click.option("--output", default="emails.json", help="Fichier de sortie")
@click.option("--concurrent", default=3, help="Générations simultanées")
@click.option("--skip-good-sites/--include-all", default=True, help="Ignorer les entreprises avec de bons sites web")
def generate_emails(input, output, concurrent, skip_good_sites):
    """Génère des emails personnalisés avec OpenAI"""
    asyncio.run(cli_instance.generate_emails(input, output, concurrent, skip_good_sites))


@cli.command()
@click.option("--input", default="emails.json", help="Fichier d'entrée")
@click.option("--dry-run/--real", default=True, help="Mode test ou envoi réel")
@click.option("--sendgrid/--smtp", default=False, help="Utiliser SendGrid ou SMTP")
@click.option("--concurrent", default=1, help="Envois simultanés")
def send(input, dry_run, sendgrid, concurrent):
    """Envoie les emails"""

    async def run_send():
        await cli_instance.send_emails(input, dry_run, sendgrid, concurrent)

    asyncio.run(run_send())


@cli.command()
@click.option("--days", default=30, help="Nombre de jours en arrière")
def stats(days):
    """Affiche les statistiques"""

    async def run_stats():
        await cli_instance.show_statistics(days)

    asyncio.run(run_stats())


@cli.command()
@click.option("--days", default=7, help="Nombre de jours en arrière")
def check_replies(days):
    """Vérifie les réponses aux emails envoyés (mode manuel)"""

    async def run_check():
        await cli_instance.check_replies(days, auto_mode=False)

    asyncio.run(run_check())


@cli.command()
def auto_check_replies():
    """Vérification automatique des réponses via IMAP (24h)"""

    async def run_auto_check():
        await cli_instance.check_replies(1, auto_mode=True)

    asyncio.run(run_auto_check())


@cli.command()
@click.option("--real", is_flag=True, help="Envoyer les relances réellement (sinon dry-run)")
def process_followups(real):
    """Traite les relances automatiques"""

    async def run_followups():
        await cli_instance.process_followups(dry_run=not real)

    asyncio.run(run_followups())


@cli.command()
@click.option("--days", default=30, help="Nombre de jours en arrière")
def followup_stats(days):
    """Affiche les statistiques de relance"""

    async def run_stats():
        await cli_instance.show_followup_stats(days)

    asyncio.run(run_stats())


@cli.command()
@click.option("--days", default=30, help="Nombre de jours en arrière")
def tracking_stats(days):
    """Affiche les statistiques de tracking"""

    async def run_stats():
        await cli_instance.show_tracking_stats(days)

    asyncio.run(run_stats())


@cli.command()
def pipeline():
    """Exécute le pipeline complet de prospection"""

    async def run_pipeline():
        console.print("[bold blue]🚀 Démarrage du pipeline de prospection[/bold blue]")

        # 1. Génération des leads
        console.print("\n[bold]Étape 1: Génération des leads[/bold]")
        await cli_instance.generate_leads(max_results=50)

        # 2. Analyse des entreprises
        console.print("\n[bold]Étape 2: Analyse des entreprises[/bold]")
        await cli_instance.analyze_companies()

        # 3. Génération des emails
        console.print("\n[bold]Étape 3: Génération des emails[/bold]")
        await cli_instance.generate_emails()

        # 4. Envoi en mode test
        console.print("\n[bold]Étape 4: Test d'envoi (dry run)[/bold]")
        await cli_instance.send_emails(dry_run=True)

        console.print("\n[bold green]✅ Pipeline terminé![/bold green]")
        console.print("Utilisez 'python cli.py send --real' pour envoyer réellement les emails")

    asyncio.run(run_pipeline())


if __name__ == "__main__":
    # Créer le dossier logs s'il n'existe pas
    Path("logs").mkdir(exist_ok=True)

    # Lancer le CLI
    cli()
