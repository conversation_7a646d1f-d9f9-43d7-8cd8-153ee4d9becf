"""
Configuration centralisée des logs pour le projet de prospection B2B
"""

import logging
import logging.config
from pathlib import Path
from typing import Optional

import yaml


def setup_logging(
    config_path: str = "config/logging.yaml", default_level: int = logging.INFO, env_key: str = "LOG_CFG"
) -> None:
    """
    Configure le système de logging

    Args:
        config_path: Chemin vers le fichier de config YAML
        default_level: Niveau de log par défaut
        env_key: Variable d'environnement pour override le config
    """
    import os

    # Créer le dossier logs s'il n'existe pas
    Path("logs").mkdir(exist_ok=True)

    # Vérifier si un chemin custom est défini en variable d'environnement
    path = os.getenv(env_key, config_path)

    if Path(path).exists():
        try:
            with open(path, "r", encoding="utf-8") as f:
                config = yaml.safe_load(f)
            logging.config.dictConfig(config)
            print(f"✅ Configuration des logs chargée depuis {path}")
        except Exception as e:
            print(f"⚠️ Erreur chargement config logs {path}: {e}")
            print("🔄 Utilisation de la configuration par défaut")
            _setup_default_logging(default_level)
    else:
        print(f"⚠️ Fichier de config logs non trouvé: {path}")
        print("🔄 Utilisation de la configuration par défaut")
        _setup_default_logging(default_level)


def _setup_default_logging(level: int = logging.INFO) -> None:
    """
    Configure un système de logging par défaut

    Args:
        level: Niveau de log par défaut
    """
    # Créer le dossier logs s'il n'existe pas
    Path("logs").mkdir(exist_ok=True)

    # Configuration par défaut
    logging.basicConfig(
        level=level,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler("logs/prospection.log", encoding="utf-8"),
            logging.FileHandler("logs/errors.log", encoding="utf-8"),
        ],
    )

    # Configurer les loggers externes pour être moins verbeux
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("requests").setLevel(logging.WARNING)


def get_logger(name: str) -> logging.Logger:
    """
    Récupère un logger configuré

    Args:
        name: Nom du logger (généralement __name__)

    Returns:
        Logger configuré
    """
    return logging.getLogger(name)


def log_function_call(func_name: str, args: Optional[dict] = None) -> None:
    """
    Log l'appel d'une fonction avec ses arguments

    Args:
        func_name: Nom de la fonction
        args: Arguments de la fonction
    """
    logger = get_logger("prospection.function_calls")

    if args:
        # Masquer les informations sensibles
        safe_args = {}
        sensitive_keys = ["password", "token", "key", "secret", "api_key"]

        for key, value in args.items():
            if any(sensitive in key.lower() for sensitive in sensitive_keys):
                safe_args[key] = "***MASKED***"
            else:
                safe_args[key] = value

        logger.debug(f"Appel fonction {func_name} avec args: {safe_args}")
    else:
        logger.debug(f"Appel fonction {func_name}")


def log_api_call(
    api_name: str,
    endpoint: str,
    method: str = "GET",
    status_code: Optional[int] = None,
    duration: Optional[float] = None,
    error: Optional[str] = None,
) -> None:
    """
    Log un appel d'API

    Args:
        api_name: Nom de l'API
        endpoint: Endpoint appelé
        method: Méthode HTTP
        status_code: Code de statut de la réponse
        duration: Durée de l'appel en secondes
        error: Message d'erreur si applicable
    """
    logger = get_logger("prospection.api_calls")

    log_data = {"api": api_name, "endpoint": endpoint, "method": method}

    if status_code:
        log_data["status"] = status_code

    if duration:
        log_data["duration"] = f"{duration:.2f}s"

    if error:
        log_data["error"] = error
        logger.error(f"API call failed: {log_data}")
    else:
        logger.info(f"API call: {log_data}")


class LoggerMixin:
    """
    Mixin pour ajouter facilement un logger à une classe
    """

    @property
    def logger(self) -> logging.Logger:
        """Retourne un logger pour cette classe"""
        return get_logger(f"{self.__class__.__module__}.{self.__class__.__name__}")

    def log_info(self, message: str, **kwargs) -> None:
        """Log un message d'info avec contexte"""
        if kwargs:
            self.logger.info(f"{message} - Context: {kwargs}")
        else:
            self.logger.info(message)

    def log_error(self, message: str, error: Optional[Exception] = None, **kwargs) -> None:
        """Log un message d'erreur avec contexte"""
        if error:
            self.logger.error(f"{message} - Error: {error} - Context: {kwargs}")
        else:
            self.logger.error(f"{message} - Context: {kwargs}")

    def log_debug(self, message: str, **kwargs) -> None:
        """Log un message de debug avec contexte"""
        if kwargs:
            self.logger.debug(f"{message} - Context: {kwargs}")
        else:
            self.logger.debug(message)
