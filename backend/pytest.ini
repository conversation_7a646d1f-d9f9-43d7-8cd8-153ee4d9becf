[tool:pytest]
# Configuration pytest pour le système de prospection B2B

# Répertoires de tests
testpaths = tests

# Patterns de fichiers de tests
python_files = test_*.py *_test.py

# Patterns de classes de tests
python_classes = Test*

# Patterns de fonctions de tests
python_functions = test_*

# Marqueurs personnalisés
markers =
    unit: Tests unitaires rapides
    integration: Tests d'intégration (nécessitent internet/APIs)
    slow: Tests lents (> 5 secondes)
    api: Tests nécessitant des clés API
    database: Tests utilisant la base de données
    email: Tests d'envoi d'emails
    scraping: Tests de scraping web

# Options par défaut
addopts = 
    -v
    --strict-markers
    --strict-config
    --tb=short
    --cov=.
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml
    --cov-fail-under=80
    --durations=10

# Filtres d'avertissements
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning

# Configuration de la couverture
[coverage:run]
source = .
omit = 
    */venv/*
    */tests/*
    */test_*
    setup.py
    conftest.py
    */migrations/*
    */node_modules/*

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod

[coverage:html]
directory = htmlcov
