#!/usr/bin/env python3
"""
Test des codes NAF dans l'API Sirene
"""

import asyncio
import os

from dotenv import load_dotenv
from rich.console import Console
from rich.table import Table

from lead_generator.sirene_auth import SireneAuthAPI

# Charger les variables d'environnement
load_dotenv()

console = Console()

async def test_naf_codes():
    """Test différents formats de codes NAF"""
    
    sirene = SireneAuthAPI(rate_limit=2)
    
    # Codes NAF à tester avec différents formats
    test_codes = [
        # Format sans point
        "6201Z",
        "6202A", 
        "4711F",
        "7022Z",
        # Format avec point
        "62.01Z",
        "62.02A",
        "47.11F", 
        "70.22Z",
        # Codes très courants
        "4711A",  # Supermarchés
        "47.11A", # Supermarchés avec point
        "5610A",  # Restauration traditionnelle
        "56.10A", # Restauration avec point
    ]
    
    console.print("🔍 Test des codes NAF dans l'API Sirene...")
    
    results = []
    
    for naf_code in test_codes:
        try:
            console.print(f"   Test: {naf_code}")
            
            # Test avec periode()
            companies = []
            async for company in sirene.search_companies(naf_codes=[naf_code], limit=3):
                companies.append(company)
                if len(companies) >= 3:
                    break
                    
            count = len(companies)
            status = "✅" if count > 0 else "❌"
            example = companies[0].name if companies else "Aucune"
            
            results.append({
                "code": naf_code,
                "count": count,
                "status": status,
                "example": example
            })
            
            console.print(f"      {status} {count} résultats")
            if companies:
                console.print(f"      Exemple: {example}")
                
        except Exception as e:
            results.append({
                "code": naf_code,
                "count": 0,
                "status": "❌",
                "example": f"Erreur: {e}"
            })
            console.print(f"      ❌ Erreur: {e}")
            
        # Pause pour respecter le rate limiting
        await asyncio.sleep(1)
    
    # Afficher le tableau des résultats
    console.print("\n" + "="*60)
    
    table = Table(title="Résultats des Tests NAF")
    table.add_column("Code NAF", style="bold")
    table.add_column("Statut")
    table.add_column("Résultats")
    table.add_column("Exemple")
    
    for result in results:
        table.add_row(
            result["code"],
            result["status"],
            str(result["count"]),
            result["example"][:30] + "..." if len(result["example"]) > 30 else result["example"]
        )
    
    console.print(table)
    
    # Recommandations
    console.print("\n💡 Recommandations:")
    
    working_codes = [r for r in results if r["count"] > 0]
    if working_codes:
        console.print("✅ Codes NAF qui fonctionnent:")
        for code in working_codes:
            console.print(f"   • {code['code']}: {code['count']} résultats")
    else:
        console.print("❌ Aucun code NAF ne retourne de résultats")
        console.print("   Possible que l'API soit limitée ou que les codes soient différents")
        
    # Analyser les patterns
    with_dot = [r for r in results if "." in r["code"] and r["count"] > 0]
    without_dot = [r for r in results if "." not in r["code"] and r["count"] > 0]
    
    if with_dot and not without_dot:
        console.print("📊 Pattern: Les codes avec point fonctionnent mieux")
    elif without_dot and not with_dot:
        console.print("📊 Pattern: Les codes sans point fonctionnent mieux")
    elif with_dot and without_dot:
        console.print("📊 Pattern: Les deux formats fonctionnent")
    else:
        console.print("📊 Pattern: Aucun format ne fonctionne")

async def main():
    """Point d'entrée principal"""
    await test_naf_codes()

if __name__ == "__main__":
    asyncio.run(main())
