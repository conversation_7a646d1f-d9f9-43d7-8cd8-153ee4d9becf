#!/usr/bin/env python3
"""
Script de lancement de l'API de prospection B2B
"""

import os
import sys
from pathlib import Path

import uvicorn

# Ajouter le répertoire racine au PYTHONPATH
root_dir = Path(__file__).parent
sys.path.insert(0, str(root_dir))


def main():
    """Lance l'API FastAPI"""

    # Configuration par défaut
    host = os.getenv("API_HOST", "0.0.0.0")
    port = int(os.getenv("API_PORT", 8000))
    reload = os.getenv("API_RELOAD", "true").lower() == "true"
    log_level = os.getenv("API_LOG_LEVEL", "info")
    workers = int(os.getenv("API_WORKERS", 1))

    print("🚀 Lancement de l'API de Prospection B2B")
    print(f"📡 Host: {host}")
    print(f"🔌 Port: {port}")
    print(f"🔄 Reload: {reload}")
    print(f"📝 Log Level: {log_level}")
    print(f"👥 Workers: {workers}")
    print(f"📚 Documentation: http://{host}:{port}/docs")
    print(f"🔍 ReDoc: http://{host}:{port}/redoc")
    print(f"🏠 API Root: http://{host}:{port}/")
    print("-" * 50)

    # Configuration uvicorn
    config = {
        "app": "api.main:app",
        "host": host,
        "port": port,
        "log_level": log_level,
        "access_log": True,
        "use_colors": True,
        "timeout_keep_alive": 300,  # 5 minutes pour les requêtes longues
        "timeout_graceful_shutdown": 30,
    }

    # En mode développement
    if reload:
        config.update({"reload": True, "reload_dirs": [str(root_dir)], "reload_excludes": ["*.pyc", "__pycache__", "*.log"]})
    else:
        # En mode production
        config.update({"workers": workers, "loop": "uvloop", "http": "httptools"})  # Plus performant  # Plus performant

    try:
        uvicorn.run(**config)
    except KeyboardInterrupt:
        print("\n🛑 Arrêt de l'API demandé par l'utilisateur")
    except Exception as e:
        print(f"❌ Erreur lors du lancement de l'API: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
