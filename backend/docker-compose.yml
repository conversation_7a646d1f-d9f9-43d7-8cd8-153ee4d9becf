version: '3.8'

services:
  prospection:
    build: .
    container_name: prospection-b2b
    restart: unless-stopped
    
    # Variables d'environnement
    environment:
      - PYTHONUNBUFFERED=1
      - LOG_LEVEL=INFO
      - DATABASE_URL=sqlite:///data/prospection.db
    
    # Fichier d'environnement (à créer depuis .env.example)
    env_file:
      - .env
    
    # Volumes pour la persistance
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./config.yaml:/app/config.yaml:ro
    
    # Commande par défaut (peut être surchargée)
    command: ["pipeline"]
    
    # Limites de ressources
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
    
    # Réseau
    networks:
      - prospection-network

  # Service optionnel : base de données PostgreSQL
  # (si on veut migrer de SQLite vers PostgreSQL)
  postgres:
    image: postgres:15-alpine
    container_name: prospection-db
    restart: unless-stopped
    
    environment:
      POSTGRES_DB: prospection
      POSTGRES_USER: prospection
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-changeme}
    
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./sql/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    
    networks:
      - prospection-network
    
    # Exposer le port pour debug (optionnel)
    # ports:
    #   - "5432:5432"

  # Service optionnel : Redis pour le cache
  redis:
    image: redis:7-alpine
    container_name: prospection-cache
    restart: unless-stopped
    
    command: redis-server --appendonly yes
    
    volumes:
      - redis_data:/data
    
    networks:
      - prospection-network
    
    # Limites de ressources
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.1'

  # Service optionnel : monitoring avec Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: prospection-monitoring
    restart: unless-stopped
    
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana:/etc/grafana/provisioning:ro
    
    ports:
      - "3000:3000"
    
    networks:
      - prospection-network
    
    depends_on:
      - postgres

# Volumes nommés
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  grafana_data:
    driver: local

# Réseau
networks:
  prospection-network:
    driver: bridge
