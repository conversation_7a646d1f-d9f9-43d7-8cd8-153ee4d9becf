#!/usr/bin/env python3
"""
Script de nettoyage et d'optimisation du système de prospection B2B
"""

import asyncio
import json
import logging
import os
import shutil
import sqlite3
from datetime import datetime
from pathlib import Path

from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.table import Table

console = Console()

class SystemCleaner:
    """Nettoyeur et optimiseur du système"""
    
    def __init__(self):
        self.root_dir = Path(__file__).parent
        self.issues_found = []
        self.fixes_applied = []
        
    def log_issue(self, issue: str, severity: str = "warning"):
        """Enregistre un problème trouvé"""
        self.issues_found.append({"issue": issue, "severity": severity, "timestamp": datetime.now()})
        
    def log_fix(self, fix: str):
        """Enregistre une correction appliquée"""
        self.fixes_applied.append({"fix": fix, "timestamp": datetime.now()})
        
    def clean_cache_files(self):
        """Nettoie les fichiers de cache Python"""
        console.print("🧹 Nettoyage des fichiers de cache...")
        
        cache_patterns = [
            "**/__pycache__",
            "**/*.pyc",
            "**/*.pyo",
            "**/*.pyd",
            ".pytest_cache",
            ".mypy_cache",
            "htmlcov",
            ".coverage"
        ]
        
        cleaned_count = 0
        for pattern in cache_patterns:
            for path in self.root_dir.glob(pattern):
                if path.is_file():
                    path.unlink()
                    cleaned_count += 1
                elif path.is_dir():
                    shutil.rmtree(path)
                    cleaned_count += 1
                    
        if cleaned_count > 0:
            self.log_fix(f"Supprimé {cleaned_count} fichiers/dossiers de cache")
            console.print(f"✅ {cleaned_count} fichiers de cache supprimés")
        else:
            console.print("✅ Aucun fichier de cache à nettoyer")
            
    def check_database_integrity(self):
        """Vérifie l'intégrité de la base de données"""
        console.print("🔍 Vérification de la base de données...")
        
        db_path = self.root_dir / "prospection.db"
        if not db_path.exists():
            self.log_issue("Base de données SQLite manquante", "warning")
            console.print("⚠️ Base de données SQLite manquante")
            return
            
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Vérifier l'intégrité
            cursor.execute("PRAGMA integrity_check")
            result = cursor.fetchone()
            
            if result[0] == "ok":
                console.print("✅ Base de données intègre")
            else:
                self.log_issue(f"Problème d'intégrité DB: {result[0]}", "error")
                console.print(f"❌ Problème d'intégrité: {result[0]}")
                
            # Statistiques
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            
            stats = {}
            for table in tables:
                table_name = table[0]
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                stats[table_name] = count
                
            console.print(f"📊 Tables: {len(tables)}, Enregistrements: {sum(stats.values())}")
            
            conn.close()
            
        except Exception as e:
            self.log_issue(f"Erreur vérification DB: {e}", "error")
            console.print(f"❌ Erreur: {e}")
            
    def optimize_configuration(self):
        """Optimise les fichiers de configuration"""
        console.print("⚙️ Optimisation de la configuration...")
        
        # Vérifier config.yaml
        config_path = self.root_dir / "config" / "config.yaml"
        if config_path.exists():
            console.print("✅ config.yaml trouvé")
        else:
            # Copier depuis config.free-apis.yaml
            free_config = self.root_dir / "config.free-apis.yaml"
            if free_config.exists():
                shutil.copy2(free_config, config_path)
                self.log_fix("Configuration copiée depuis config.free-apis.yaml")
                console.print("✅ Configuration copiée depuis config.free-apis.yaml")
            else:
                self.log_issue("Aucun fichier de configuration trouvé", "error")
                
        # Vérifier .env
        env_path = self.root_dir / ".env"
        if env_path.exists():
            console.print("✅ Fichier .env trouvé")
            
            # Vérifier les variables critiques
            with open(env_path) as f:
                env_content = f.read()
                
            critical_vars = ["OPENAI_API_KEY", "SMTP_USERNAME", "SMTP_PASSWORD"]
            missing_vars = []
            
            for var in critical_vars:
                if var not in env_content or f"{var}=your_" in env_content:
                    missing_vars.append(var)
                    
            if missing_vars:
                self.log_issue(f"Variables manquantes: {missing_vars}", "warning")
                console.print(f"⚠️ Variables à configurer: {', '.join(missing_vars)}")
            else:
                console.print("✅ Variables d'environnement configurées")
        else:
            self.log_issue("Fichier .env manquant", "error")
            console.print("❌ Fichier .env manquant")
            
    def check_data_files(self):
        """Vérifie et optimise les fichiers de données"""
        console.print("📁 Vérification des fichiers de données...")
        
        data_files = {
            "leads.json": "Fichier des leads générés",
            "analyzed_companies.json": "Entreprises analysées", 
            "emails.json": "Emails générés"
        }
        
        for filename, description in data_files.items():
            filepath = self.root_dir / filename
            
            if filepath.exists():
                try:
                    with open(filepath) as f:
                        data = json.load(f)
                        
                    if isinstance(data, dict) and "companies" in data:
                        count = len(data.get("companies", []))
                        console.print(f"✅ {filename}: {count} entrées")
                        
                        if count == 0:
                            self.log_issue(f"{filename} vide", "info")
                            
                    else:
                        self.log_issue(f"{filename} format invalide", "warning")
                        console.print(f"⚠️ {filename}: format invalide")
                        
                except json.JSONDecodeError:
                    self.log_issue(f"{filename} JSON invalide", "error")
                    console.print(f"❌ {filename}: JSON invalide")
                    
            else:
                self.log_issue(f"{filename} manquant", "info")
                console.print(f"ℹ️ {filename}: manquant")
                
    def optimize_logs(self):
        """Optimise les fichiers de logs"""
        console.print("📝 Optimisation des logs...")
        
        logs_dir = self.root_dir / "logs"
        if not logs_dir.exists():
            logs_dir.mkdir()
            self.log_fix("Dossier logs créé")
            console.print("✅ Dossier logs créé")
            return
            
        # Compter les lignes de logs
        log_files = list(logs_dir.glob("*.log"))
        total_lines = 0
        
        for log_file in log_files:
            try:
                with open(log_file) as f:
                    lines = sum(1 for _ in f)
                    total_lines += lines
                    
                # Si le fichier est trop gros (>10000 lignes), le tronquer
                if lines > 10000:
                    with open(log_file) as f:
                        all_lines = f.readlines()
                        
                    # Garder les 5000 dernières lignes
                    with open(log_file, 'w') as f:
                        f.writelines(all_lines[-5000:])
                        
                    self.log_fix(f"Log {log_file.name} tronqué ({lines} -> 5000 lignes)")
                    console.print(f"✅ {log_file.name} optimisé")
                    
            except Exception as e:
                self.log_issue(f"Erreur lecture log {log_file.name}: {e}", "warning")
                
        console.print(f"📊 {len(log_files)} fichiers de logs, {total_lines} lignes total")
        
    def generate_report(self):
        """Génère un rapport de nettoyage"""
        console.print("\n" + "="*60)
        console.print(Panel.fit("📋 RAPPORT DE NETTOYAGE", style="bold blue"))
        
        # Tableau des problèmes
        if self.issues_found:
            issues_table = Table(title="🔍 Problèmes Identifiés")
            issues_table.add_column("Sévérité", style="bold")
            issues_table.add_column("Problème")
            
            for issue in self.issues_found:
                severity = issue["severity"]
                color = {"error": "red", "warning": "yellow", "info": "blue"}.get(severity, "white")
                issues_table.add_row(f"[{color}]{severity.upper()}[/{color}]", issue["issue"])
                
            console.print(issues_table)
        else:
            console.print("✅ Aucun problème identifié")
            
        # Tableau des corrections
        if self.fixes_applied:
            fixes_table = Table(title="🔧 Corrections Appliquées")
            fixes_table.add_column("Correction")
            fixes_table.add_column("Heure")
            
            for fix in self.fixes_applied:
                fixes_table.add_row(fix["fix"], fix["timestamp"].strftime("%H:%M:%S"))
                
            console.print(fixes_table)
        else:
            console.print("ℹ️ Aucune correction nécessaire")
            
        # Résumé
        error_count = sum(1 for i in self.issues_found if i["severity"] == "error")
        warning_count = sum(1 for i in self.issues_found if i["severity"] == "warning")
        
        console.print(f"\n📊 Résumé: {error_count} erreurs, {warning_count} avertissements, {len(self.fixes_applied)} corrections")
        
        if error_count == 0:
            console.print("🎉 Système prêt à l'utilisation !")
        else:
            console.print("⚠️ Certains problèmes nécessitent votre attention")
            
    async def run_full_cleanup(self):
        """Lance le nettoyage complet"""
        console.print(Panel.fit("🚀 NETTOYAGE ET OPTIMISATION DU SYSTÈME", style="bold green"))
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            
            task = progress.add_task("Nettoyage en cours...", total=6)
            
            progress.update(task, description="Nettoyage des fichiers cache...")
            self.clean_cache_files()
            progress.advance(task)
            
            progress.update(task, description="Vérification base de données...")
            self.check_database_integrity()
            progress.advance(task)
            
            progress.update(task, description="Optimisation configuration...")
            self.optimize_configuration()
            progress.advance(task)
            
            progress.update(task, description="Vérification fichiers de données...")
            self.check_data_files()
            progress.advance(task)
            
            progress.update(task, description="Optimisation des logs...")
            self.optimize_logs()
            progress.advance(task)
            
            progress.update(task, description="Génération du rapport...")
            progress.advance(task)
            
        self.generate_report()

async def main():
    """Point d'entrée principal"""
    cleaner = SystemCleaner()
    await cleaner.run_full_cleanup()

if __name__ == "__main__":
    asyncio.run(main())
