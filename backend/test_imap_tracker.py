"""
Test direct du tracking IMAP des réponses
"""

import email
import imaplib
import os
import re
from datetime import datetime, timedelta
from email.header import decode_header

from dotenv import load_dotenv

load_dotenv()


def decode_header_value(header):
    """Décode un header d'email"""
    try:
        decoded_parts = decode_header(header)
        decoded_string = ""
        for part, encoding in decoded_parts:
            if isinstance(part, bytes):
                decoded_string += part.decode(encoding or "utf-8")
            else:
                decoded_string += part
        return decoded_string
    except Exception:
        return header


def extract_email(from_header):
    """Extrait l'adresse email du header From"""
    match = re.search(r"<(.+?)>", from_header)
    if match:
        return match.group(1)

    if "@" in from_header:
        return from_header.strip()

    return ""


def is_reply_to_our_email(subject, from_email):
    """Vérifie si c'est une réponse à nos emails"""
    subject_lower = subject.lower()

    # Mots-clés de nos sujets d'emails
    our_keywords = [
        "présence en ligne",
        "site web",
        "modernisation",
        "développeur web",
        "rochdi sami",
        "sami rochdi",
        "optimisation",
        "amélioration",
    ]

    # Vérifier si c'est un "Re:" avec nos mots-clés
    if subject_lower.startswith("re:") and any(keyword in subject_lower for keyword in our_keywords):
        return True

    return False


def analyze_reply_type(subject, body):
    """Analyse le type de réponse"""
    text = (subject + " " + body).lower()

    # Réponses automatiques
    auto_reply_keywords = [
        "out of office",
        "absent",
        "congé",
        "vacances",
        "automatic reply",
        "réponse automatique",
        "ne pas répondre",
        "do not reply",
    ]

    if any(keyword in text for keyword in auto_reply_keywords):
        return "auto_reply"

    # Réponses positives
    positive_keywords = [
        "intéressé",
        "interested",
        "merci",
        "thank you",
        "rendez-vous",
        "meeting",
        "appelez",
        "call",
        "discuter",
        "discuss",
        "projet",
        "project",
    ]

    if any(keyword in text for keyword in positive_keywords):
        return "positive"

    # Réponses négatives
    negative_keywords = [
        "pas intéressé",
        "not interested",
        "non merci",
        "no thank",
        "déjà",
        "already",
        "stop",
        "unsubscribe",
        "ne pas contacter",
        "do not contact",
    ]

    if any(keyword in text for keyword in negative_keywords):
        return "negative"

    return "neutral"


def extract_body(email_message):
    """Extrait le corps de l'email"""
    try:
        if email_message.is_multipart():
            for part in email_message.walk():
                if part.get_content_type() == "text/plain":
                    payload = part.get_payload(decode=True)
                    if payload:
                        return payload.decode("utf-8", errors="ignore")
        else:
            payload = email_message.get_payload(decode=True)
            if payload:
                return payload.decode("utf-8", errors="ignore")
    except Exception as e:
        print(f"Erreur extraction corps: {e}")

    return ""


def check_replies():
    """Vérifie les réponses aux emails"""
    print("🔍 Vérification automatique des réponses...")

    # Configuration
    email_address = os.getenv("SMTP_USER", "<EMAIL>")
    password = os.getenv("SMTP_PASSWORD", "")

    print(f"📧 Connexion à {email_address}...")

    try:
        # Connexion IMAP
        imap = imaplib.IMAP4_SSL("imap.gmail.com", 993)
        imap.login(email_address, password)
        print("✅ Connexion IMAP réussie!")

        # Sélectionner la boîte de réception
        imap.select("INBOX")

        # Rechercher les emails des dernières 24h
        since_date = (datetime.now() - timedelta(hours=24)).strftime("%d-%b-%Y")
        status, messages = imap.search(None, f"SINCE {since_date}")

        if status != "OK":
            print("❌ Erreur recherche emails")
            return

        message_ids = messages[0].split()
        print(f"📧 {len(message_ids)} emails trouvés dans les dernières 24h")

        replies_found = []

        # Traiter chaque email
        for msg_id in message_ids[-20:]:  # Limiter aux 20 derniers
            try:
                # Récupérer l'email
                status, msg_data = imap.fetch(msg_id, "(RFC822)")

                if status != "OK":
                    continue

                # Parser l'email
                email_message = email.message_from_bytes(msg_data[0][1])

                # Extraire les informations
                from_header = email_message.get("From", "")
                subject_header = email_message.get("Subject", "")
                date_header = email_message.get("Date", "")

                # Décoder le sujet
                subject = decode_header_value(subject_header)
                from_email = extract_email(from_header)

                # Vérifier si c'est une réponse à nos emails
                if is_reply_to_our_email(subject, from_email):
                    body = extract_body(email_message)
                    reply_type = analyze_reply_type(subject, body)

                    replies_found.append(
                        {
                            "from_email": from_email,
                            "subject": subject,
                            "body": body[:300],
                            "reply_type": reply_type,
                            "date": date_header,
                        }
                    )

                    print(f"\n🎯 RÉPONSE DÉTECTÉE!")
                    print(f"De: {from_email}")
                    print(f"Sujet: {subject}")
                    print(f"Type: {reply_type.upper()}")
                    print(f"Message: {body[:200]}...")

            except Exception as e:
                print(f"Erreur traitement email {msg_id}: {e}")

        imap.close()
        imap.logout()

        if not replies_found:
            print("✅ Aucune réponse détectée dans les dernières 24h")
        else:
            print(f"\n🎉 {len(replies_found)} réponse(s) détectée(s)!")

            # Résumé par type
            types = {}
            for reply in replies_found:
                reply_type = reply["reply_type"]
                types[reply_type] = types.get(reply_type, 0) + 1

            print("\n📊 Résumé par type:")
            for reply_type, count in types.items():
                emoji = {"positive": "✅", "negative": "❌", "neutral": "⚪", "auto_reply": "🤖"}.get(reply_type, "❓")
                print(f"{emoji} {reply_type.upper()}: {count}")

    except Exception as e:
        print(f"❌ Erreur: {e}")


if __name__ == "__main__":
    check_replies()
