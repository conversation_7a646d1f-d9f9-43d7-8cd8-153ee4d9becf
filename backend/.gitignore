# Système de prospection B2B - Fichiers à ignorer

# Variables d'environnement et secrets
.env
.env.local
.env.production
*.key
*.pem
secrets/

# Base de données
*.db
*.sqlite
*.sqlite3
prospection.db
data/

# Logs
logs/
*.log
*.log.*

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/
.venv/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Tests et couverture
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
coverage.xml
*.cover
*.py,cover
.hypothesis/

# Jupyter Notebooks
.ipynb_checkpoints

# Documentation
docs/_build/
.readthedocs.yml

# Fichiers temporaires
*.tmp
*.temp
temp/
tmp/

# Fichiers de données sensibles
leads.json
analyzed_companies.json
emails.json
contacts.json
companies.json
*.csv
*.xlsx

# Sauvegardes
backups/
*.backup
*.bak

# Cache
.cache/
.mypy_cache/
.dmypy.json
dmypy.json

# Rapports de sécurité
bandit-report.json
.secrets.baseline

# Docker
.dockerignore

# Fichiers système
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Fichiers de configuration locaux
config.local.yaml
settings.local.py

# Certificats SSL
*.crt
*.cert
*.ca-bundle

# Fichiers de monitoring
monitoring/data/
grafana/data/

# Fichiers de déploiement
deploy/
.terraform/
*.tfstate
*.tfstate.backup

# Fichiers de build
*.tar.gz
*.zip
*.rar

# Fichiers de profiling
*.prof
*.pstats

# Fichiers de debug
debug.log
error.log

# Fichiers spécifiques au projet
examples/real_*
examples/production_*
test_output/
screenshots/

# Ignore mais garder les dossiers
!.gitkeep
