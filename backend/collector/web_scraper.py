"""
Scraper web pour analyser les sites d'entreprises
"""

import asyncio
import logging
import re
from typing import Any, Dict, List, Optional
from urllib.parse import urljoin, urlparse

import httpx
from bs4 import BeautifulSoup

logger = logging.getLogger(__name__)


class WebScraper:
    """Scraper pour analyser les sites web d'entreprises"""

    def __init__(self, timeout: int = 30, max_retries: int = 3):
        """
        Args:
            timeout: Timeout en secondes pour les requêtes
            max_retries: Nombre maximum de tentatives
        """
        self.timeout = timeout
        self.max_retries = max_retries
        self.user_agent = (
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
            "(KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        )

    async def _make_request(self, url: str) -> Optional[str]:
        """Effectue une requête HTTP avec gestion d'erreurs"""
        headers = {
            "User-Agent": self.user_agent,
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
            "Accept-Language": "fr-FR,fr;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate",
            "Connection": "keep-alive",
        }

        for attempt in range(self.max_retries):
            try:
                async with httpx.AsyncClient(timeout=self.timeout, follow_redirects=True, headers=headers) as client:
                    response = await client.get(url)
                    response.raise_for_status()
                    return response.text

            except httpx.HTTPError as e:
                logger.warning(f"Tentative {attempt + 1} échouée pour {url}: {e}")
                if attempt == self.max_retries - 1:
                    logger.error(f"Impossible de récupérer {url} après {self.max_retries} tentatives")
                    return None
                await asyncio.sleep(2**attempt)  # Backoff exponentiel

        return None

    def _extract_text_content(self, soup: BeautifulSoup) -> str:
        """Extrait le contenu textuel principal d'une page"""
        # Supprimer les scripts, styles, etc.
        for script in soup(["script", "style", "nav", "footer", "header"]):
            script.decompose()

        # Extraire le texte principal
        main_content = soup.find("main") or soup.find("div", class_=re.compile(r"content|main"))
        if main_content:
            text = main_content.get_text()
        else:
            text = soup.get_text()

        # Nettoyer le texte
        lines = (line.strip() for line in text.splitlines())
        chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
        text = " ".join(chunk for chunk in chunks if chunk)

        return text[:5000]  # Limiter à 5000 caractères

    def _extract_meta_info(self, soup: BeautifulSoup) -> Dict[str, str]:
        """Extrait les métadonnées de la page"""
        meta_info = {}

        # Title
        title_tag = soup.find("title")
        if title_tag:
            meta_info["title"] = title_tag.get_text().strip()

        # Meta description
        meta_desc = soup.find("meta", attrs={"name": "description"})
        if meta_desc:
            meta_info["description"] = meta_desc.get("content", "").strip()

        # Meta keywords
        meta_keywords = soup.find("meta", attrs={"name": "keywords"})
        if meta_keywords:
            meta_info["keywords"] = meta_keywords.get("content", "").strip()

        # Open Graph
        og_title = soup.find("meta", property="og:title")
        if og_title:
            meta_info["og_title"] = og_title.get("content", "").strip()

        og_desc = soup.find("meta", property="og:description")
        if og_desc:
            meta_info["og_description"] = og_desc.get("content", "").strip()

        return meta_info

    def _detect_cms_framework(self, soup: BeautifulSoup, html_content: str) -> List[str]:
        """Détecte le CMS ou framework utilisé"""
        technologies = []

        # WordPress
        if "wp-content" in html_content or soup.find(
            "meta", attrs={"name": "generator", "content": re.compile(r"WordPress", re.I)}
        ):
            technologies.append("WordPress")

        # Drupal
        if "drupal" in html_content.lower() or soup.find(
            "meta", attrs={"name": "generator", "content": re.compile(r"Drupal", re.I)}
        ):
            technologies.append("Drupal")

        # Joomla
        if "joomla" in html_content.lower() or soup.find(
            "meta", attrs={"name": "generator", "content": re.compile(r"Joomla", re.I)}
        ):
            technologies.append("Joomla")

        # Shopify
        if "shopify" in html_content.lower() or "cdn.shopify.com" in html_content:
            technologies.append("Shopify")

        # Wix
        if "wix.com" in html_content or "static.wixstatic.com" in html_content:
            technologies.append("Wix")

        # Squarespace
        if "squarespace" in html_content.lower():
            technologies.append("Squarespace")

        # React
        if "react" in html_content.lower() or soup.find("div", id="root"):
            technologies.append("React")

        # Vue.js
        if "vue" in html_content.lower() or soup.find(attrs={"v-": True}):
            technologies.append("Vue.js")

        # Angular
        if "angular" in html_content.lower() or soup.find(attrs={"ng-": True}):
            technologies.append("Angular")

        return technologies

    def _analyze_page_quality(self, soup: BeautifulSoup, url: str) -> Dict[str, Any]:
        """Analyse la qualité technique de la page"""
        quality_issues = []

        # Vérifier la présence d'éléments essentiels
        if not soup.find("title"):
            quality_issues.append("Pas de balise title")

        if not soup.find("meta", attrs={"name": "description"}):
            quality_issues.append("Pas de meta description")

        # Vérifier les images sans alt
        images_without_alt = soup.find_all("img", alt="")
        if len(images_without_alt) > 5:
            quality_issues.append(f"{len(images_without_alt)} images sans attribut alt")

        # Vérifier les liens cassés (basique)
        external_links = soup.find_all("a", href=re.compile(r"^https?://"))
        if len(external_links) > 50:
            quality_issues.append("Beaucoup de liens externes (possible spam)")

        # Vérifier la structure HTML
        if not soup.find("h1"):
            quality_issues.append("Pas de balise H1")

        # Analyser l'âge apparent du design
        design_age_indicators = []

        # Rechercher des indicateurs de design obsolète
        if soup.find("table", attrs={"cellpadding": True, "cellspacing": True}):
            design_age_indicators.append("Utilisation de tables pour la mise en page")

        if soup.find("font"):
            design_age_indicators.append("Utilisation de balises font obsolètes")

        # Vérifier la responsivité
        viewport_meta = soup.find("meta", attrs={"name": "viewport"})
        is_responsive = bool(viewport_meta)

        return {
            "quality_issues": quality_issues,
            "design_age_indicators": design_age_indicators,
            "is_responsive": is_responsive,
            "total_issues": len(quality_issues) + len(design_age_indicators),
        }

    async def analyze_website(self, url: str) -> Dict[str, Any]:
        """
        Analyse complète d'un site web

        Args:
            url: URL du site à analyser

        Returns:
            Dictionnaire avec toutes les informations collectées
        """
        if not url.startswith(("http://", "https://")):
            url = f"https://{url}"

        html_content = await self._make_request(url)
        if not html_content:
            return {"url": url, "accessible": False, "error": "Site inaccessible"}

        try:
            soup = BeautifulSoup(html_content, "html.parser")

            # Extraire toutes les informations
            analysis = {
                "url": url,
                "accessible": True,
                "meta_info": self._extract_meta_info(soup),
                "content": self._extract_text_content(soup),
                "technologies": self._detect_cms_framework(soup, html_content),
                "quality_analysis": self._analyze_page_quality(soup, url),
                "analyzed_at": asyncio.get_event_loop().time(),
            }

            return analysis

        except Exception as e:
            logger.error(f"Erreur analyse de {url}: {e}")
            return {"url": url, "accessible": False, "error": str(e)}

    async def get_website_description(self, url: str) -> str:
        """
        Récupère une description concise du site web

        Args:
            url: URL du site

        Returns:
            Description du site ou chaîne vide
        """
        analysis = await self.analyze_website(url)

        if not analysis.get("accessible"):
            return ""

        # Priorité : meta description > og:description > début du contenu
        meta_info = analysis.get("meta_info", {})

        description = meta_info.get("description") or meta_info.get("og_description") or analysis.get("content", "")[:500]

        return description.strip()
