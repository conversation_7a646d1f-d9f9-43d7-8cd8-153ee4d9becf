"""
Analyseur principal pour collecter toutes les informations sur une entreprise
"""

import asyncio
import logging
from datetime import datetime
from typing import Any, Dict, Optional

from models import Company, CompanyContext, PainPoint, TechStack

from .pain_detector import PainPointDetector
from .tech_analyzer import TechAnalyzer
from .web_scraper import WebScraper

logger = logging.getLogger(__name__)


class CompanyAnalyzer:
    """Analyseur principal pour collecter les informations d'entreprise"""

    def __init__(self):
        """Initialise l'analyseur avec tous les modules"""
        self.web_scraper = WebScraper()
        self.tech_analyzer = TechAnalyzer()
        self.pain_detector = PainPointDetector()

    async def analyze_company(self, company: Company) -> CompanyContext:
        """
        Analyse complète d'une entreprise

        Args:
            company: Entreprise à analyser

        Returns:
            Contexte complet de l'entreprise
        """
        logger.info(f"Analyse de l'entreprise: {company.name}")

        # Initialiser le contexte
        context = CompanyContext(company=company, analyzed_at=datetime.now())

        try:
            # 1. Analyser le site web si disponible
            if company.website or company.domain:
                website_url = str(company.website) if company.website else f"https://{company.domain}"

                logger.debug(f"Analyse du site web: {website_url}")
                website_analysis = await self.web_scraper.analyze_website(website_url)

                if website_analysis.get("accessible"):
                    # Extraire la description
                    context.website_description = await self.web_scraper.get_website_description(website_url)

                    # 2. Analyser la stack technique
                    logger.debug("Analyse de la stack technique")
                    html_content = website_analysis.get("content", "")
                    context.tech_stack = await self.tech_analyzer.analyze_technologies(website_url, html_content)

                    # 3. Détecter les pain points
                    logger.debug("Détection des pain points")
                    context.pain_points = self.pain_detector.detect_pain_points(
                        website_analysis=website_analysis, tech_stack=context.tech_stack, content=html_content
                    )
                else:
                    logger.warning(f"Site web inaccessible pour {company.name}: {website_analysis.get('error')}")
                    # Ajouter un pain point pour site inaccessible
                    context.pain_points = [
                        PainPoint(
                            category="website",
                            description="Site web inaccessible",
                            severity=4,
                            evidence=website_analysis.get("error", "Erreur inconnue"),
                        )
                    ]

            # 4. Calculer le score de lead
            context.lead_score = self._calculate_lead_score(context)
            context.priority = self._determine_priority(context.lead_score)

            logger.info(f"Analyse terminée pour {company.name} - Score: {context.lead_score}")

        except Exception as e:
            logger.error(f"Erreur lors de l'analyse de {company.name}: {e}")
            # Retourner un contexte minimal en cas d'erreur
            context.pain_points = [PainPoint(category="process", description="Erreur d'analyse", severity=1, evidence=str(e))]

        return context

    def _calculate_lead_score(self, context: CompanyContext) -> int:
        """
        Calcule un score de lead basé sur l'analyse

        Args:
            context: Contexte de l'entreprise

        Returns:
            Score entre 0 et 100
        """
        score = 50  # Score de base

        try:
            # Bonus/malus basés sur la taille de l'entreprise
            if context.company.size:
                size_scores = {
                    "micro": -10,  # Trop petites
                    "small": 10,  # Taille idéale
                    "medium": 15,  # Très bonne taille
                    "large": 5,  # Peuvent être plus difficiles
                }
                score += size_scores.get(context.company.size.value, 0)

            # Bonus si on a un email
            if context.company.email:
                score += 15

            # Bonus si on a un site web accessible
            if context.website_description:
                score += 10

            # Malus/bonus basés sur les pain points
            if context.pain_points:
                # Bonus pour les pain points détectés (opportunités)
                high_severity_pains = [p for p in context.pain_points if p.severity >= 4]
                medium_severity_pains = [p for p in context.pain_points if p.severity == 3]

                score += len(high_severity_pains) * 8  # Pain points critiques
                score += len(medium_severity_pains) * 5  # Pain points moyens

                # Bonus spécial pour certains types de pain points
                website_pains = [p for p in context.pain_points if p.category == "website"]
                if website_pains:
                    score += 10  # Opportunité de refonte

            # Bonus basé sur la stack technique
            if context.tech_stack:
                # Malus pour les technologies modernes (moins d'opportunités)
                modern_cms = ["WordPress", "Shopify", "Webflow"]
                if any(cms in context.tech_stack.cms for cms in modern_cms):
                    score -= 5

                # Bonus pour les technologies obsolètes
                obsolete_indicators = ["Joomla", "Drupal", "Wix"]
                if any(tech in context.tech_stack.cms for tech in obsolete_indicators):
                    score += 10

                # Bonus si pas d'analytics
                if not context.tech_stack.analytics:
                    score += 8

            # Bonus pour les secteurs d'activité ciblés
            if context.company.naf_code:
                target_naf_codes = ["6201Z", "6202A", "6202B", "7022Z"]
                if context.company.naf_code in target_naf_codes:
                    score += 15

            # S'assurer que le score reste dans les limites
            score = max(0, min(100, score))

        except Exception as e:
            logger.error(f"Erreur calcul score lead: {e}")
            score = 50  # Score par défaut en cas d'erreur

        return score

    def _determine_priority(self, lead_score: int) -> str:
        """
        Détermine la priorité basée sur le score

        Args:
            lead_score: Score calculé

        Returns:
            Priorité: "high", "medium", ou "low"
        """
        if lead_score >= 75:
            return "high"
        elif lead_score >= 50:
            return "medium"
        else:
            return "low"

    def get_analysis_summary(self, context: CompanyContext) -> str:
        """
        Génère un résumé textuel de l'analyse

        Args:
            context: Contexte analysé

        Returns:
            Résumé textuel
        """
        summary_parts = []

        # Informations de base
        company = context.company
        summary_parts.append(f"Entreprise: {company.name}")

        if company.city:
            summary_parts.append(f"Ville: {company.city}")

        if company.employees:
            summary_parts.append(f"Effectifs: {company.employees}")

        # Score et priorité
        summary_parts.append(f"Score: {context.lead_score}/100 ({context.priority})")

        # Stack technique
        if context.tech_stack:
            tech_summary = self.tech_analyzer.get_technology_summary(context.tech_stack)
            if tech_summary != "Aucune technologie détectée":
                summary_parts.append(f"Technologies: {tech_summary}")

        # Pain points
        if context.pain_points:
            pain_summary = self.pain_detector.get_pain_points_summary(context.pain_points)
            summary_parts.append(f"Opportunités: {pain_summary}")

        return " | ".join(summary_parts)

    async def batch_analyze_companies(self, companies: list[Company], max_concurrent: int = 5) -> list[CompanyContext]:
        """
        Analyse un lot d'entreprises en parallèle

        Args:
            companies: Liste des entreprises à analyser
            max_concurrent: Nombre maximum d'analyses simultanées

        Returns:
            Liste des contextes analysés
        """
        semaphore = asyncio.Semaphore(max_concurrent)

        async def analyze_with_semaphore(company: Company) -> CompanyContext:
            async with semaphore:
                return await self.analyze_company(company)

        logger.info(f"Analyse de {len(companies)} entreprises (max {max_concurrent} simultanées)")

        tasks = [analyze_with_semaphore(company) for company in companies]
        contexts = await asyncio.gather(*tasks, return_exceptions=True)

        # Filtrer les erreurs
        valid_contexts = []
        for i, result in enumerate(contexts):
            if isinstance(result, Exception):
                logger.error(f"Erreur analyse entreprise {companies[i].name}: {result}")
            else:
                valid_contexts.append(result)

        logger.info(f"Analyse terminée: {len(valid_contexts)}/{len(companies)} réussies")
        return valid_contexts
