"""
Détecteur de points de douleur (pain points) pour les entreprises
"""

import logging
import re
from typing import Any, Dict, List, Optional

from models import PainPoint, TechStack

logger = logging.getLogger(__name__)


class PainPointDetector:
    """Détecteur de points de douleur basé sur l'analyse web et technologique"""

    def __init__(self):
        """Initialise le détecteur avec les règles de détection"""
        self.pain_point_rules = self._load_pain_point_rules()

    def _load_pain_point_rules(self) -> Dict[str, Dict[str, Any]]:
        """Charge les règles de détection des pain points"""
        return {**self._get_website_rules(), **self._get_technology_rules(), **self._get_process_rules()}

    def _get_website_rules(self) -> Dict[str, Dict[str, Any]]:
        """Règles pour les pain points liés au site web"""
        return {
            "site_obsolete": {
                "category": "website",
                "description": "Site web avec un design obsolète",
                "severity": 3,
                "indicators": {
                    "html_patterns": [r"<table[^>]*cellpadding", r"<font[^>]*>", r"<center>", r"bgcolor="],
                    "css_patterns": [r"font-family:\s*arial", r"font-family:\s*times"],
                    "meta_patterns": [r"generator.*frontpage", r"generator.*dreamweaver"],
                },
            },
            "non_responsive": {
                "category": "website",
                "description": "Site non responsive (pas adapté mobile)",
                "severity": 4,
                "indicators": {
                    "missing_viewport": True,
                    "fixed_width_patterns": [r"width:\s*\d+px", r"min-width:\s*\d{3,}px"],
                },
            },
            "site_lent": {
                "category": "website",
                "description": "Site potentiellement lent",
                "severity": 3,
                "indicators": {"large_images": True, "no_optimization": True, "many_external_resources": True},
            },
            "securite_faible": {
                "category": "website",
                "description": "Problèmes de sécurité potentiels",
                "severity": 5,
                "indicators": {"no_https": True, "old_cms": True, "exposed_admin": True},
            },
        }

    def _get_technology_rules(self) -> Dict[str, Dict[str, Any]]:
        """Règles pour les pain points technologiques"""
        return {
            "cms_obsolete": {
                "category": "technology",
                "description": "CMS obsolète ou non maintenu",
                "severity": 4,
                "indicators": {"old_wordpress": True, "old_drupal": True, "deprecated_tech": True},
            },
            "pas_de_analytics": {
                "category": "technology",
                "description": "Absence d'outils d'analyse web",
                "severity": 2,
                "indicators": {"no_analytics": True},
            },
            "technologie_mixte": {
                "category": "technology",
                "description": "Mélange de technologies incompatibles",
                "severity": 3,
                "indicators": {"mixed_frameworks": True, "conflicting_libraries": True},
            },
        }

    def _get_process_rules(self) -> Dict[str, Dict[str, Any]]:
        """Règles pour les pain points de processus"""
        return {
            "processus_manuels": {
                "category": "process",
                "description": "Processus manuels détectés",
                "severity": 3,
                "indicators": {
                    "content_patterns": [
                        r"excel",
                        r"fichier.*partag",
                        r"email.*commande",
                        r"fax",
                        r"papier",
                        r"manuel",
                        r"saisie.*main",
                    ]
                },
            },
            "pas_de_crm": {
                "category": "process",
                "description": "Absence d'outils CRM détectée",
                "severity": 3,
                "indicators": {"no_crm_integration": True, "basic_contact_form": True},
            },
            "communication_obsolete": {
                "category": "process",
                "description": "Moyens de communication obsolètes",
                "severity": 2,
                "indicators": {"content_patterns": [r"fax\s*:\s*\d", r"minitel", r"courrier.*postal"]},
            },
        }

    def _check_html_patterns(self, html_content: str, patterns: List[str]) -> List[str]:
        """Vérifie les patterns dans le HTML"""
        found_patterns = []
        for pattern in patterns:
            if re.search(pattern, html_content, re.IGNORECASE):
                found_patterns.append(pattern)
        return found_patterns

    def _check_content_patterns(self, content: str, patterns: List[str]) -> List[str]:
        """Vérifie les patterns dans le contenu textuel"""
        found_patterns = []
        for pattern in patterns:
            if re.search(pattern, content, re.IGNORECASE):
                found_patterns.append(pattern)
        return found_patterns

    def _analyze_website_quality(self, website_analysis: Dict[str, Any]) -> List[PainPoint]:
        """Analyse la qualité du site web pour détecter les pain points"""
        pain_points = []

        if not website_analysis.get("accessible"):
            pain_points.append(
                PainPoint(
                    category="website",
                    description="Site web inaccessible",
                    severity=5,
                    evidence="Site ne répond pas ou erreur HTTP",
                )
            )
            return pain_points

        quality_analysis = website_analysis.get("quality_analysis", {})

        # Site obsolète
        design_issues = quality_analysis.get("design_age_indicators", [])
        if design_issues:
            pain_points.append(
                PainPoint(
                    category="website", description="Design obsolète détecté", severity=3, evidence="; ".join(design_issues)
                )
            )

        # Non responsive
        if not quality_analysis.get("is_responsive", True):
            pain_points.append(
                PainPoint(
                    category="website",
                    description="Site non responsive",
                    severity=4,
                    evidence="Pas de balise viewport détectée",
                )
            )

        # Problèmes de qualité
        quality_issues = quality_analysis.get("quality_issues", [])
        if len(quality_issues) >= 3:
            pain_points.append(
                PainPoint(
                    category="website",
                    description="Problèmes de qualité SEO",
                    severity=2,
                    evidence=f"{len(quality_issues)} problèmes détectés",
                )
            )

        # Sécurité HTTPS
        url = website_analysis.get("url", "")
        if url.startswith("http://"):
            pain_points.append(
                PainPoint(
                    category="website",
                    description="Site non sécurisé (pas de HTTPS)",
                    severity=4,
                    evidence="URL commence par http://",
                )
            )

        return pain_points

    def _analyze_tech_stack(self, tech_stack: TechStack) -> List[PainPoint]:
        """Analyse la stack technique pour détecter les pain points"""
        pain_points = []

        # CMS obsolètes ou problématiques
        problematic_cms = ["Joomla", "Drupal"]  # Versions souvent obsolètes
        for cms in tech_stack.cms:
            if cms in problematic_cms:
                pain_points.append(
                    PainPoint(
                        category="technology",
                        description=f"CMS potentiellement obsolète: {cms}",
                        severity=3,
                        evidence=f"Utilisation de {cms}",
                    )
                )

        # Builders de sites limitants
        limiting_builders = ["Wix", "Squarespace"]
        for cms in tech_stack.cms:
            if cms in limiting_builders:
                pain_points.append(
                    PainPoint(
                        category="technology",
                        description=f"Plateforme limitante: {cms}",
                        severity=2,
                        evidence=f"Site construit avec {cms}",
                    )
                )

        # Absence d'analytics
        if not tech_stack.analytics:
            pain_points.append(
                PainPoint(
                    category="technology",
                    description="Absence d'outils d'analyse web",
                    severity=2,
                    evidence="Aucun outil d'analytics détecté",
                )
            )

        # Technologies conflictuelles
        if len(tech_stack.frameworks) > 2:
            pain_points.append(
                PainPoint(
                    category="technology",
                    description="Trop de frameworks JavaScript",
                    severity=2,
                    evidence=f"Frameworks détectés: {', '.join(tech_stack.frameworks)}",
                )
            )

        return pain_points

    def _analyze_content(self, content: str) -> List[PainPoint]:
        """Analyse le contenu textuel pour détecter les pain points"""
        pain_points = []

        # Processus manuels
        manual_indicators = [r"excel", r"fichier.*partag", r"email.*commande", r"fax", r"papier", r"manuel", r"saisie.*main"]

        found_manual = self._check_content_patterns(content, manual_indicators)
        if found_manual:
            pain_points.append(
                PainPoint(
                    category="process",
                    description="Processus manuels détectés",
                    severity=3,
                    evidence=f"Mentions: {', '.join(found_manual[:3])}",
                )
            )

        # Communication obsolète
        obsolete_comm = [r"fax\s*:\s*\d", r"minitel", r"courrier.*postal"]
        found_obsolete = self._check_content_patterns(content, obsolete_comm)
        if found_obsolete:
            pain_points.append(
                PainPoint(
                    category="process",
                    description="Moyens de communication obsolètes",
                    severity=2,
                    evidence=f"Mentions: {', '.join(found_obsolete)}",
                )
            )

        return pain_points

    def detect_pain_points(
        self,
        website_analysis: Optional[Dict[str, Any]] = None,
        tech_stack: Optional[TechStack] = None,
        content: Optional[str] = None,
    ) -> List[PainPoint]:
        """
        Détecte tous les pain points d'une entreprise

        Args:
            website_analysis: Analyse du site web
            tech_stack: Stack technique détectée
            content: Contenu textuel du site

        Returns:
            Liste des pain points détectés
        """
        all_pain_points = []

        try:
            # Analyser le site web
            if website_analysis:
                website_pains = self._analyze_website_quality(website_analysis)
                all_pain_points.extend(website_pains)

            # Analyser la stack technique
            if tech_stack:
                tech_pains = self._analyze_tech_stack(tech_stack)
                all_pain_points.extend(tech_pains)

            # Analyser le contenu
            if content:
                content_pains = self._analyze_content(content)
                all_pain_points.extend(content_pains)

            # Trier par sévérité (plus grave en premier)
            all_pain_points.sort(key=lambda x: x.severity, reverse=True)

            return all_pain_points

        except Exception as e:
            logger.error(f"Erreur détection pain points: {e}")
            return []

    def get_pain_points_summary(self, pain_points: List[PainPoint]) -> str:
        """
        Génère un résumé textuel des pain points

        Args:
            pain_points: Liste des pain points

        Returns:
            Résumé textuel
        """
        if not pain_points:
            return "Aucun point de douleur majeur détecté"

        # Grouper par catégorie
        by_category = {}
        for pain in pain_points:
            if pain.category not in by_category:
                by_category[pain.category] = []
            by_category[pain.category].append(pain)

        summary_parts = []
        category_names = {"website": "Site web", "technology": "Technologie", "process": "Processus"}

        for category, pains in by_category.items():
            category_name = category_names.get(category, category)
            pain_descriptions = [p.description for p in pains[:3]]  # Max 3 par catégorie
            summary_parts.append(f"{category_name}: {', '.join(pain_descriptions)}")

        return " | ".join(summary_parts)
