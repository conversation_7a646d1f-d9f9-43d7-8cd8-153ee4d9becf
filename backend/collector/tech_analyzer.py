"""
Analyseur de stack technique pour détecter les technologies utilisées
"""

import asyncio
import logging
import re
from typing import Any, Dict, List, Optional

import httpx
from bs4 import BeautifulSoup

from models import TechStack

logger = logging.getLogger(__name__)


class TechAnalyzer:
    """Analyseur de technologies web"""

    def __init__(self):
        """Initialise l'analyseur avec les signatures de technologies"""
        self.tech_signatures = self._load_tech_signatures()

    def _load_tech_signatures(self) -> Dict[str, Dict[str, Any]]:
        """Charge les signatures de détection des technologies"""
        return {
            # CMS
            "WordPress": {
                "html": [r"wp-content", r"wp-includes"],
                "meta": [r"WordPress"],
                "headers": {"x-powered-by": r"WordPress"},
                "category": "cms",
            },
            "Drupal": {"html": [r"drupal", r"sites/default/files"], "meta": [r"Drupal"], "category": "cms"},
            "Joomla": {"html": [r"joomla", r"option=com_"], "meta": [r"Joomla"], "category": "cms"},
            "Shopify": {"html": [r"cdn\.shopify\.com", r"shopify"], "category": "ecommerce"},
            "WooCommerce": {"html": [r"woocommerce", r"wc-"], "category": "ecommerce"},
            "Magento": {"html": [r"magento", r"mage/"], "category": "ecommerce"},
            # Frameworks JavaScript
            "React": {"html": [r"react", r"__REACT_DEVTOOLS_GLOBAL_HOOK__"], "category": "frameworks"},
            "Vue.js": {"html": [r"vue\.js", r"v-"], "category": "frameworks"},
            "Angular": {"html": [r"angular", r"ng-"], "category": "frameworks"},
            "jQuery": {"html": [r"jquery", r"\$\("], "category": "frameworks"},
            # Analytics
            "Google Analytics": {"html": [r"google-analytics\.com", r"gtag\(", r"ga\("], "category": "analytics"},
            "Google Tag Manager": {"html": [r"googletagmanager\.com"], "category": "analytics"},
            "Facebook Pixel": {"html": [r"facebook\.net/tr", r"fbq\("], "category": "analytics"},
            "Hotjar": {"html": [r"hotjar\.com"], "category": "analytics"},
            # Hébergement/CDN
            "Cloudflare": {"headers": {"server": r"cloudflare"}, "category": "hosting"},
            "Amazon CloudFront": {"headers": {"server": r"CloudFront"}, "category": "hosting"},
            "OVH": {"headers": {"server": r"ovh"}, "category": "hosting"},
            # Autres
            "Bootstrap": {"html": [r"bootstrap", r"btn-"], "category": "other"},
            "Font Awesome": {"html": [r"font-awesome", r"fa-"], "category": "other"},
        }

    def _check_html_signatures(self, html_content: str, signatures: List[str]) -> bool:
        """Vérifie les signatures dans le contenu HTML"""
        for signature in signatures:
            if re.search(signature, html_content, re.IGNORECASE):
                return True
        return False

    def _check_meta_signatures(self, soup: BeautifulSoup, signatures: List[str]) -> bool:
        """Vérifie les signatures dans les balises meta"""
        meta_tags = soup.find_all("meta")
        meta_content = " ".join([tag.get("content", "") + " " + tag.get("name", "") for tag in meta_tags])

        for signature in signatures:
            if re.search(signature, meta_content, re.IGNORECASE):
                return True
        return False

    def _check_header_signatures(self, headers: Dict[str, str], header_sigs: Dict[str, str]) -> bool:
        """Vérifie les signatures dans les headers HTTP"""
        for header_name, pattern in header_sigs.items():
            header_value = headers.get(header_name, "")
            if re.search(pattern, header_value, re.IGNORECASE):
                return True
        return False

    async def analyze_technologies(self, url: str, html_content: str, headers: Optional[Dict[str, str]] = None) -> TechStack:
        """
        Analyse les technologies utilisées sur un site

        Args:
            url: URL du site
            html_content: Contenu HTML de la page
            headers: Headers HTTP (optionnel)

        Returns:
            Objet TechStack avec les technologies détectées
        """
        if headers is None:
            headers = {}

        try:
            soup = BeautifulSoup(html_content, "html.parser")
            detected_techs = {"cms": [], "frameworks": [], "analytics": [], "ecommerce": [], "hosting": [], "other": []}

            # Analyser chaque signature de technologie
            for tech_name, tech_config in self.tech_signatures.items():
                is_detected = False

                # Vérifier HTML
                if "html" in tech_config:
                    if self._check_html_signatures(html_content, tech_config["html"]):
                        is_detected = True

                # Vérifier meta tags
                if "meta" in tech_config and not is_detected:
                    if self._check_meta_signatures(soup, tech_config["meta"]):
                        is_detected = True

                # Vérifier headers
                if "headers" in tech_config and not is_detected:
                    if self._check_header_signatures(headers, tech_config["headers"]):
                        is_detected = True

                # Ajouter à la catégorie appropriée
                if is_detected:
                    category = tech_config.get("category", "other")
                    detected_techs[category].append(tech_name)

            # Détections supplémentaires spécifiques
            additional_techs = await self._detect_additional_technologies(soup, html_content)
            for category, techs in additional_techs.items():
                detected_techs[category].extend(techs)

            return TechStack(
                cms=detected_techs["cms"],
                frameworks=detected_techs["frameworks"],
                analytics=detected_techs["analytics"],
                ecommerce=detected_techs["ecommerce"],
                hosting=detected_techs["hosting"],
                other=detected_techs["other"],
            )

        except Exception as e:
            logger.error(f"Erreur analyse technologies pour {url}: {e}")
            return TechStack()

    async def _detect_additional_technologies(self, soup: BeautifulSoup, html_content: str) -> Dict[str, List[str]]:
        """Détections supplémentaires de technologies"""
        additional = {"cms": [], "frameworks": [], "analytics": [], "ecommerce": [], "hosting": [], "other": []}

        # Détecter les builders de sites
        if "wix.com" in html_content or "static.wixstatic.com" in html_content:
            additional["cms"].append("Wix")

        if "squarespace" in html_content.lower():
            additional["cms"].append("Squarespace")

        if "webflow" in html_content.lower():
            additional["cms"].append("Webflow")

        # Détecter les frameworks CSS
        if re.search(r"tailwind", html_content, re.IGNORECASE):
            additional["other"].append("Tailwind CSS")

        if re.search(r"bulma", html_content, re.IGNORECASE):
            additional["other"].append("Bulma")

        # Détecter les outils de chat
        if re.search(r"intercom", html_content, re.IGNORECASE):
            additional["other"].append("Intercom")

        if re.search(r"zendesk", html_content, re.IGNORECASE):
            additional["other"].append("Zendesk Chat")

        if re.search(r"crisp", html_content, re.IGNORECASE):
            additional["other"].append("Crisp")

        # Détecter les solutions de paiement
        if re.search(r"stripe", html_content, re.IGNORECASE):
            additional["ecommerce"].append("Stripe")

        if re.search(r"paypal", html_content, re.IGNORECASE):
            additional["ecommerce"].append("PayPal")

        return additional

    def get_technology_summary(self, tech_stack: TechStack) -> str:
        """
        Génère un résumé textuel de la stack technique

        Args:
            tech_stack: Stack technique analysée

        Returns:
            Résumé textuel
        """
        summary_parts = []

        if tech_stack.cms:
            summary_parts.append(f"CMS: {', '.join(tech_stack.cms)}")

        if tech_stack.frameworks:
            summary_parts.append(f"Frameworks: {', '.join(tech_stack.frameworks)}")

        if tech_stack.ecommerce:
            summary_parts.append(f"E-commerce: {', '.join(tech_stack.ecommerce)}")

        if tech_stack.analytics:
            summary_parts.append(f"Analytics: {', '.join(tech_stack.analytics)}")

        if tech_stack.hosting:
            summary_parts.append(f"Hébergement: {', '.join(tech_stack.hosting)}")

        if tech_stack.other:
            summary_parts.append(f"Autres: {', '.join(tech_stack.other)}")

        return " | ".join(summary_parts) if summary_parts else "Aucune technologie détectée"
