#!/usr/bin/env python3
"""
Test de l'API Entreprise comme alternative
"""

import asyncio

import httpx
from rich.console import Console
from rich.table import Table

console = Console()

class APIEntrepriseTester:
    """Testeur pour l'API Entreprise"""
    
    def __init__(self):
        self.base_url = "https://entreprise.api.gouv.fr/v3"
        
    async def test_siren_lookup(self):
        """Test de recherche par SIREN"""
        console.print("🔍 Test recherche par SIREN...")
        
        # SIRENs de test (entreprises connues)
        test_sirens = [
            "552032534",  # DANONE
            "542065479",  # GOOGLE FRANCE
            "775672272",  # MICROSOFT FRANCE
            "433166861",  # AMAZON FRANCE
        ]
        
        results = []
        
        for siren in test_sirens:
            try:
                url = f"{self.base_url}/insee/sirene/unites_legales/{siren}"
                
                async with httpx.AsyncClient() as client:
                    response = await client.get(url, timeout=30.0)
                    
                    if response.status_code == 200:
                        data = response.json()
                        if "data" in data:
                            company_data = data["data"]
                            name = company_data.get("denomination", "N/A")
                            naf = company_data.get("activite_principale", "N/A")
                            
                            results.append({
                                "siren": siren,
                                "status": "✅",
                                "name": name,
                                "naf": naf
                            })
                            
                            console.print(f"   ✅ {siren}: {name}")
                        else:
                            results.append({
                                "siren": siren,
                                "status": "❌",
                                "name": "Pas de données",
                                "naf": "N/A"
                            })
                    else:
                        results.append({
                            "siren": siren,
                            "status": "❌",
                            "name": f"Erreur {response.status_code}",
                            "naf": "N/A"
                        })
                        console.print(f"   ❌ {siren}: Erreur {response.status_code}")
                        
                await asyncio.sleep(0.5)  # Rate limiting
                
            except Exception as e:
                results.append({
                    "siren": siren,
                    "status": "❌",
                    "name": f"Exception: {e}",
                    "naf": "N/A"
                })
                console.print(f"   ❌ {siren}: Exception {e}")
                
        return results
        
    async def test_siret_lookup(self):
        """Test de recherche par SIRET"""
        console.print("🔍 Test recherche par SIRET...")
        
        # SIRETs de test
        test_sirets = [
            "55203253400175",  # DANONE siège
            "54206547900023",  # GOOGLE FRANCE
        ]
        
        results = []
        
        for siret in test_sirets:
            try:
                url = f"{self.base_url}/insee/sirene/etablissements/{siret}"
                
                async with httpx.AsyncClient() as client:
                    response = await client.get(url, timeout=30.0)
                    
                    if response.status_code == 200:
                        data = response.json()
                        if "data" in data:
                            etab_data = data["data"]
                            name = etab_data.get("unite_legale", {}).get("denomination", "N/A")
                            city = etab_data.get("adresse", {}).get("libelle_commune", "N/A")
                            
                            results.append({
                                "siret": siret,
                                "status": "✅",
                                "name": name,
                                "city": city
                            })
                            
                            console.print(f"   ✅ {siret}: {name} ({city})")
                        else:
                            results.append({
                                "siret": siret,
                                "status": "❌",
                                "name": "Pas de données",
                                "city": "N/A"
                            })
                    else:
                        results.append({
                            "siret": siret,
                            "status": "❌",
                            "name": f"Erreur {response.status_code}",
                            "city": "N/A"
                        })
                        console.print(f"   ❌ {siret}: Erreur {response.status_code}")
                        
                await asyncio.sleep(0.5)
                
            except Exception as e:
                results.append({
                    "siret": siret,
                    "status": "❌",
                    "name": f"Exception: {e}",
                    "city": "N/A"
                })
                console.print(f"   ❌ {siret}: Exception {e}")
                
        return results
        
    async def test_search_capabilities(self):
        """Test des capacités de recherche"""
        console.print("🔍 Test capacités de recherche...")
        
        # L'API Entreprise ne fait que du lookup unitaire, pas de recherche multicritère
        console.print("   ℹ️ API Entreprise = lookup unitaire seulement")
        console.print("   ℹ️ Pas de recherche par NAF/géographie")
        
        return True
        
    async def run_full_test(self):
        """Lance tous les tests"""
        console.print("🚀 Test complet de l'API Entreprise")
        console.print("="*60)
        
        # Test SIREN
        siren_results = await self.test_siren_lookup()
        
        # Test SIRET
        siret_results = await self.test_siret_lookup()
        
        # Test capacités
        search_ok = await self.test_search_capabilities()
        
        # Résumé
        console.print("\n" + "="*60)
        console.print("📊 RÉSUMÉ DES TESTS")
        
        # Tableau SIREN
        if siren_results:
            table_siren = Table(title="Résultats Lookup SIREN")
            table_siren.add_column("SIREN", style="bold")
            table_siren.add_column("Statut")
            table_siren.add_column("Nom")
            table_siren.add_column("NAF")
            
            for result in siren_results:
                table_siren.add_row(
                    result["siren"],
                    result["status"],
                    result["name"][:30] + "..." if len(result["name"]) > 30 else result["name"],
                    result["naf"]
                )
            
            console.print(table_siren)
        
        # Tableau SIRET
        if siret_results:
            table_siret = Table(title="Résultats Lookup SIRET")
            table_siret.add_column("SIRET", style="bold")
            table_siret.add_column("Statut")
            table_siret.add_column("Nom")
            table_siret.add_column("Ville")
            
            for result in siret_results:
                table_siret.add_row(
                    result["siret"],
                    result["status"],
                    result["name"][:30] + "..." if len(result["name"]) > 30 else result["name"],
                    result["city"]
                )
            
            console.print(table_siret)
        
        # Recommandations
        console.print("\n💡 RECOMMANDATIONS:")
        
        working_siren = [r for r in siren_results if r["status"] == "✅"]
        working_siret = [r for r in siret_results if r["status"] == "✅"]
        
        if working_siren and working_siret:
            console.print("🎉 API Entreprise fonctionne pour le lookup !")
            console.print("✅ Parfait pour enrichir des données existantes")
            console.print("❌ Ne peut pas faire de recherche multicritère")
            console.print("💡 Utiliser en complément d'une autre source")
        elif working_siren or working_siret:
            console.print("⚠️ API Entreprise fonctionne partiellement")
            console.print("✅ Peut enrichir certaines données")
        else:
            console.print("❌ API Entreprise ne fonctionne pas")

async def main():
    """Point d'entrée principal"""
    tester = APIEntrepriseTester()
    await tester.run_full_test()

if __name__ == "__main__":
    asyncio.run(main())
