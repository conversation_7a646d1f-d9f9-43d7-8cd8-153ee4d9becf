#!/usr/bin/env python3
"""
Script pour modifier les dates d'envoi de quelques emails pour tester les relances
"""

import asyncio
import aiomysql
from datetime import datetime, timedelta

async def update_email_dates():
    """Met à jour les dates d'envoi de quelques emails pour tester les relances"""
    
    connection = await aiomysql.connect(
        host="127.0.0.1",
        port=3306,
        user="root",
        password="MLKqsd002",
        db="prospection_b2b",
        charset="utf8mb4"
    )
    
    try:
        async with connection.cursor() as cursor:
            
            # Récupérer tous les emails envoyés
            await cursor.execute("SELECT id, company_name, sent_at FROM sent_emails WHERE status = 'sent' ORDER BY id LIMIT 8")
            emails = await cursor.fetchall()
            
            print(f"Modification des dates pour {len(emails)} emails...")
            
            for i, (email_id, company_name, current_date) in enumerate(emails):
                # Modifier les dates pour créer différents scénarios de relance
                if i < 2:
                    # Emails envoyés il y a 8 jours (relance due)
                    new_date = datetime.now() - timedelta(days=8)
                elif i < 4:
                    # Emails envoyés il y a 5 jours (relance bientôt due)
                    new_date = datetime.now() - timedelta(days=5)
                elif i < 6:
                    # Emails envoyés il y a 2 jours (pas encore de relance)
                    new_date = datetime.now() - timedelta(days=2)
                else:
                    # Emails envoyés aujourd'hui
                    new_date = datetime.now()
                
                # Mettre à jour la date
                await cursor.execute("""
                    UPDATE sent_emails 
                    SET sent_at = %s
                    WHERE id = %s
                """, (new_date, email_id))
                
                print(f"Email {email_id} ({company_name}): {current_date} -> {new_date}")
            
            await connection.commit()
            print("\n✅ Dates mises à jour avec succès !")
            
            # Afficher un résumé
            print("\n📊 Résumé des emails par ancienneté :")
            await cursor.execute("""
                SELECT 
                    CASE 
                        WHEN DATEDIFF(NOW(), sent_at) >= 7 THEN 'Relance due (7+ jours)'
                        WHEN DATEDIFF(NOW(), sent_at) >= 5 THEN 'Relance bientôt (5-6 jours)'
                        WHEN DATEDIFF(NOW(), sent_at) >= 2 THEN 'Récent (2-4 jours)'
                        ELSE 'Très récent (0-1 jour)'
                    END as category,
                    COUNT(*) as count
                FROM sent_emails 
                WHERE status = 'sent'
                GROUP BY category
                ORDER BY MIN(DATEDIFF(NOW(), sent_at)) DESC
            """)
            
            summary = await cursor.fetchall()
            for category, count in summary:
                print(f"- {category}: {count} emails")
                
    except Exception as e:
        print(f"❌ Erreur lors de la mise à jour: {e}")
        await connection.rollback()
        raise
    finally:
        connection.close()

if __name__ == "__main__":
    asyncio.run(update_email_dates())
