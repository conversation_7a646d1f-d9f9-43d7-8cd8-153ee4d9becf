"""
Routeur pour la gestion des métriques en temps réel
"""

import logging
from datetime import datetime
from typing import Dict, Any

from fastapi import APIRouter, HTTPException, Request
from fastapi.responses import Response
from pydantic import BaseModel

from api.schemas import APIResponse
from api.services import ProspectionAPIService

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/metrics", tags=["metrics"])

# Modèles Pydantic
class MetricsUpdate(BaseModel):
    email_id: int
    opened: bool = None
    clicked: bool = None
    replied: bool = None

class TokenUsage(BaseModel):
    model: str = "gpt-4o"
    tokens_used: int
    cost_usd: float
    emails_generated: int

# Instance du service
service = ProspectionAPIService()

@router.post("/track/open/{tracking_id}", response_model=APIResponse)
async def track_email_open(tracking_id: str, request):
    """
    Enregistre l'ouverture d'un email via le pixel de tracking
    """
    try:
        await service.initialize()

        from sender.database_manager_mysql import DatabaseManager
        db_manager = DatabaseManager()
        await db_manager.initialize()

        # Récupérer les informations de la requête
        user_agent = request.headers.get("User-Agent", "")
        ip_address = request.client.host if hasattr(request, 'client') else ""

        # Enregistrer l'ouverture
        success = await db_manager.track_email_open(tracking_id, user_agent, ip_address)

        if success:
            return APIResponse(
                success=True,
                message="Ouverture d'email enregistrée",
                data={"tracking_id": tracking_id, "timestamp": datetime.now().isoformat()}
            )
        else:
            return APIResponse(
                success=False,
                message="Erreur lors de l'enregistrement de l'ouverture",
                data=None
            )

    except Exception as e:
        logger.error(f"Erreur tracking ouverture: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/track/click/{tracking_id}", response_model=APIResponse)
async def track_email_click(tracking_id: str, url: str, request):
    """
    Enregistre le clic sur un lien dans un email
    """
    try:
        await service.initialize()

        from sender.database_manager_mysql import DatabaseManager
        db_manager = DatabaseManager()
        await db_manager.initialize()

        # Récupérer les informations de la requête
        user_agent = request.headers.get("User-Agent", "")
        ip_address = request.client.host if hasattr(request, 'client') else ""

        # Enregistrer le clic
        success = await db_manager.track_email_click(tracking_id, url, user_agent, ip_address)

        if success:
            return APIResponse(
                success=True,
                message="Clic d'email enregistré",
                data={"tracking_id": tracking_id, "url": url, "timestamp": datetime.now().isoformat()}
            )
        else:
            return APIResponse(
                success=False,
                message="Erreur lors de l'enregistrement du clic",
                data=None
            )

    except Exception as e:
        logger.error(f"Erreur tracking clic: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/update", response_model=APIResponse)
async def update_email_metrics(metrics: MetricsUpdate):
    """
    Met à jour les métriques d'un email spécifique
    """
    try:
        await service.initialize()
        
        from sender.database_manager_mysql import DatabaseManager
        db_manager = DatabaseManager()
        await db_manager.initialize()
        
        # Mettre à jour les métriques
        await db_manager.update_email_metrics(
            email_id=metrics.email_id,
            opened=metrics.opened,
            clicked=metrics.clicked,
            replied=metrics.replied
        )
        
        return APIResponse(
            success=True,
            message=f"Métriques mises à jour pour l'email {metrics.email_id}",
            data={"email_id": metrics.email_id, "updated_at": datetime.now().isoformat()}
        )
        
    except Exception as e:
        logger.error(f"Erreur mise à jour métriques: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/tokens", response_model=APIResponse)
async def get_token_usage():
    """
    Récupère l'utilisation des tokens OpenAI
    """
    try:
        await service.initialize()
        
        from sender.database_manager_mysql import DatabaseManager
        db_manager = DatabaseManager()
        await db_manager.initialize()
        
        # Récupérer les emails générés
        emails = await db_manager.get_sent_emails(limit=1000)
        
        # Calculer l'utilisation des tokens
        emails_count = len(emails)
        
        # Estimation réaliste des tokens par email
        # Un email personnalisé utilise environ 500-800 tokens
        tokens_per_email = 650  # Moyenne
        total_tokens = emails_count * tokens_per_email
        
        # Prix GPT-4o : $0.03 per 1K tokens
        cost_per_1k_tokens = 0.03
        total_cost = (total_tokens / 1000) * cost_per_1k_tokens
        
        token_usage = {
            "model": "gpt-4o",
            "emails_generated": emails_count,
            "tokens_per_email_avg": tokens_per_email,
            "total_tokens": total_tokens,
            "cost_usd": round(total_cost, 4),
            "cost_eur": round(total_cost * 0.92, 4),  # Conversion approximative
            "last_updated": datetime.now().isoformat()
        }
        
        return APIResponse(
            success=True,
            message="Utilisation des tokens récupérée",
            data=token_usage
        )
        
    except Exception as e:
        logger.error(f"Erreur récupération tokens: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/realtime", response_model=APIResponse)
async def get_realtime_metrics():
    """
    Récupère les métriques en temps réel
    Cette route est appelée par le frontend pour mettre à jour les données
    """
    try:
        await service.initialize()
        
        from sender.database_manager_mysql import DatabaseManager
        db_manager = DatabaseManager()
        await db_manager.initialize()
        
        # Récupérer les statistiques actuelles
        stats = await db_manager.get_statistics(days_back=30)
        
        # Récupérer l'utilisation des tokens
        token_response = await get_token_usage()
        token_data = token_response.data if token_response.success else {}
        
        # Métriques en temps réel
        realtime_data = {
            "statistics": stats,
            "token_usage": token_data,
            "system_status": {
                "database": "connected",
                "api": "operational",
                "email_service": "active"
            },
            "last_refresh": datetime.now().isoformat()
        }
        
        return APIResponse(
            success=True,
            message="Métriques temps réel récupérées",
            data=realtime_data
        )
        
    except Exception as e:
        logger.error(f"Erreur métriques temps réel: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/tracking/pixel/{tracking_id}")
async def tracking_pixel(tracking_id: str, request: Request):
    """
    Pixel de tracking transparent pour détecter l'ouverture des emails
    Retourne une image 1x1 pixel transparente
    """
    try:
        # Enregistrer l'ouverture
        await track_email_open(tracking_id, request)

        # Retourner un pixel transparent

        # Image GIF 1x1 pixel transparente
        pixel_data = b'\x47\x49\x46\x38\x39\x61\x01\x00\x01\x00\x80\x00\x00\x00\x00\x00\x00\x00\x00\x21\xF9\x04\x01\x00\x00\x00\x00\x2C\x00\x00\x00\x00\x01\x00\x01\x00\x00\x02\x02\x04\x01\x00\x3B'

        return Response(
            content=pixel_data,
            media_type="image/gif",
            headers={
                "Cache-Control": "no-cache, no-store, must-revalidate",
                "Pragma": "no-cache",
                "Expires": "0"
            }
        )

    except Exception as e:
        logger.error(f"Erreur pixel tracking: {e}")
        # Retourner quand même le pixel pour ne pas casser l'affichage de l'email
        pixel_data = b'\x47\x49\x46\x38\x39\x61\x01\x00\x01\x00\x80\x00\x00\x00\x00\x00\x00\x00\x00\x21\xF9\x04\x01\x00\x00\x00\x00\x2C\x00\x00\x00\x00\x01\x00\x01\x00\x00\x02\x02\x04\x01\x00\x3B'
        return Response(content=pixel_data, media_type="image/gif")
