"""
Routeur API pour le dashboard et les statistiques générales
"""

from fastapi import APIRouter, HTTPException

from api.schemas import (
    APIResponse,
    CampaignStatsResponse,
    FollowUpStatsResponse,
    HealthResponse,
    TrackingStatsResponse,
)
from api.services import ProspectionAPIService

router = APIRouter(prefix="/dashboard", tags=["dashboard"])

# Instance du service
service = ProspectionAPIService()


@router.get("/overview", response_model=APIResponse)
async def get_dashboard_overview(days_back: int = 30):
    """
    Récupère la vue d'ensemble du dashboard

    - **days_back**: Nombre de jours en arrière pour les statistiques
    """
    try:
        # Récupérer les statistiques principales
        campaign_stats = await service.get_campaign_stats(days_back)
        tracking_stats = await service.get_tracking_stats(days_back)
        followup_stats = await service.get_followup_stats(days_back)

        overview_data = {
            "period_days": days_back,
            "campaign": {
                "emails_sent": campaign_stats.emails_sent,
                "emails_opened": campaign_stats.emails_opened,
                "emails_clicked": campaign_stats.emails_clicked,
                "emails_replied": campaign_stats.emails_replied,
                "followups_sent": campaign_stats.followups_sent,
                "open_rate": campaign_stats.open_rate,
                "click_rate": campaign_stats.click_rate,
                "reply_rate": campaign_stats.reply_rate,
            },
            "tracking": {
                "total_events": tracking_stats.tracking_events,
                "open_rate": tracking_stats.open_rate,
                "click_rate": tracking_stats.click_rate,
            },
            "followups": {
                "emails_needing_followup": followup_stats.emails_needing_followup,
                "followups_sent": followup_stats.followups_sent,
                "followup_response_rate": followup_stats.followup_response_rate,
            },
            "trends": {
                "emails_trend": "stable",  # À calculer
                "open_rate_trend": "up",  # À calculer
                "reply_rate_trend": "up",  # À calculer
            },
        }

        return APIResponse(success=True, message="Vue d'ensemble du dashboard récupérée", data=overview_data)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur vue d'ensemble dashboard: {str(e)}")


@router.get("/kpis", response_model=APIResponse)
async def get_key_performance_indicators(days_back: int = 30):
    """
    Récupère les indicateurs clés de performance (KPIs)

    - **days_back**: Nombre de jours en arrière
    """
    try:
        campaign_stats = await service.get_campaign_stats(days_back)

        # Calculer les KPIs
        total_prospects = campaign_stats.emails_sent
        qualified_leads = campaign_stats.emails_replied  # Approximation
        conversion_rate = campaign_stats.reply_rate

        # Coût par lead (approximation)
        cost_per_email = 0.01  # $0.01 par email
        total_cost = total_prospects * cost_per_email
        cost_per_lead = total_cost / qualified_leads if qualified_leads > 0 else 0

        # ROI approximatif
        revenue_per_lead = 500  # Approximation
        total_revenue = qualified_leads * revenue_per_lead
        roi = ((total_revenue - total_cost) / total_cost * 100) if total_cost > 0 else 0

        kpis = {
            "total_prospects": total_prospects,
            "qualified_leads": qualified_leads,
            "conversion_rate": conversion_rate,
            "cost_per_lead": round(cost_per_lead, 2),
            "roi_percentage": round(roi, 1),
            "average_response_time": "2.5 jours",  # À calculer
            "email_deliverability": 98.5,  # Approximation
            "engagement_score": round((campaign_stats.open_rate + campaign_stats.click_rate) / 2, 1),
            "pipeline_value": total_revenue,
            "active_campaigns": 1,  # À implémenter
        }

        return APIResponse(success=True, message="KPIs récupérés avec succès", data=kpis)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur KPIs: {str(e)}")


@router.get("/charts/timeline", response_model=APIResponse)
async def get_timeline_chart(days_back: int = 30):
    """
    Récupère les données pour le graphique de timeline

    - **days_back**: Nombre de jours en arrière
    """
    try:
        # TODO: Implémenter les données de timeline par jour

        return APIResponse(
            success=True,
            message="Données de timeline récupérées",
            data={
                "timeline": [],
                "metrics": ["emails_sent", "emails_opened", "emails_clicked", "emails_replied"],
                "period": f"{days_back} jours",
            },
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur timeline: {str(e)}")


@router.get("/charts/funnel", response_model=APIResponse)
async def get_funnel_chart(days_back: int = 30):
    """
    Récupère les données pour le graphique en entonnoir

    - **days_back**: Nombre de jours en arrière
    """
    try:
        campaign_stats = await service.get_campaign_stats(days_back)

        funnel_data = {
            "stages": [
                {"name": "Emails Envoyés", "value": campaign_stats.emails_sent, "percentage": 100.0, "color": "#3B82F6"},
                {
                    "name": "Emails Ouverts",
                    "value": campaign_stats.emails_opened,
                    "percentage": campaign_stats.open_rate,
                    "color": "#10B981",
                },
                {
                    "name": "Liens Cliqués",
                    "value": campaign_stats.emails_clicked,
                    "percentage": campaign_stats.click_rate,
                    "color": "#F59E0B",
                },
                {
                    "name": "Réponses Reçues",
                    "value": campaign_stats.emails_replied,
                    "percentage": campaign_stats.reply_rate,
                    "color": "#EF4444",
                },
            ],
            "conversion_rates": {
                "open_to_click": round(
                    (
                        (campaign_stats.emails_clicked / campaign_stats.emails_opened * 100)
                        if campaign_stats.emails_opened > 0
                        else 0
                    ),
                    1,
                ),
                "click_to_reply": round(
                    (
                        (campaign_stats.emails_replied / campaign_stats.emails_clicked * 100)
                        if campaign_stats.emails_clicked > 0
                        else 0
                    ),
                    1,
                ),
            },
        }

        return APIResponse(success=True, message="Données de funnel récupérées", data=funnel_data)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur funnel: {str(e)}")


@router.get("/charts/geographic", response_model=APIResponse)
async def get_geographic_chart():
    """
    Récupère les données pour la répartition géographique
    """
    try:
        # TODO: Implémenter la répartition géographique

        return APIResponse(
            success=True,
            message="Données géographiques récupérées",
            data={"by_region": {}, "by_department": {}, "top_cities": []},
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur données géographiques: {str(e)}")


@router.get("/charts/sectors", response_model=APIResponse)
async def get_sectors_chart():
    """
    Récupère les données pour la répartition par secteur d'activité
    """
    try:
        # TODO: Implémenter la répartition par secteur

        return APIResponse(
            success=True,
            message="Données sectorielles récupérées",
            data={"by_naf_code": {}, "by_sector": {}, "top_performing_sectors": []},
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur données sectorielles: {str(e)}")


@router.get("/alerts", response_model=APIResponse)
async def get_dashboard_alerts():
    """
    Récupère les alertes et notifications du dashboard
    """
    try:
        # TODO: Implémenter les alertes intelligentes

        alerts = [
            {
                "id": 1,
                "type": "info",
                "title": "Relances en attente",
                "message": "5 emails nécessitent une relance",
                "action_url": "/followups/pending",
                "created_at": "2025-06-14T23:00:00Z",
            },
            {
                "id": 2,
                "type": "success",
                "title": "Nouvelle réponse positive",
                "message": "Une réponse positive a été détectée",
                "action_url": "/tracking/replies",
                "created_at": "2025-06-14T22:30:00Z",
            },
        ]

        return APIResponse(success=True, message="Alertes récupérées", data={"alerts": alerts, "unread_count": len(alerts)})

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur alertes: {str(e)}")


@router.get("/export", response_model=APIResponse)
async def export_dashboard_data(format: str = "csv", days_back: int = 30):
    """
    Exporte les données du dashboard

    - **format**: Format d'export (csv, xlsx, pdf)
    - **days_back**: Nombre de jours en arrière
    """
    try:
        # TODO: Implémenter l'export des données

        return APIResponse(
            success=True,
            message=f"Données exportées au format {format}",
            data={
                "download_url": f"/downloads/dashboard_export_{days_back}days.{format}",
                "expires_at": "2025-06-15T23:00:00Z",
            },
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur export: {str(e)}")


@router.get("/health", response_model=APIResponse)
async def get_system_health():
    """
    Récupère l'état de santé du système
    """
    try:
        health = await service.get_health_status()

        return APIResponse(success=True, message="État de santé récupéré", data=health.model_dump())

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur état de santé: {str(e)}")


@router.get("/config", response_model=APIResponse)
async def get_system_config():
    """
    Récupère la configuration du système
    """
    try:
        # TODO: Implémenter la récupération de configuration

        config = {
            "smtp_configured": True,
            "sendgrid_configured": False,
            "openai_configured": True,
            "database_connected": True,
            "tracking_enabled": True,
            "rate_limit": 5,
            "version": "1.0.0",
            "features": {
                "lead_generation": True,
                "email_tracking": True,
                "auto_followups": True,
                "reply_detection": True,
                "ai_personalization": True,
            },
        }

        return APIResponse(success=True, message="Configuration récupérée", data=config)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur configuration: {str(e)}")
