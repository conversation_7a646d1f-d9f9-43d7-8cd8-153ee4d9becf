"""
Routeur API pour la gestion des relances
"""

from fastapi import APIRouter, HTTPException

from api.schemas import APIResponse, FollowUpRequest
from api.services import ProspectionAPIService

router = APIRouter(prefix="/followups", tags=["followups"])

# Instance du service
service = ProspectionAPIService()


@router.post("/process", response_model=APIResponse)
async def process_followups(request: FollowUpRequest):
    """
    Traite les relances automatiques

    - **days_threshold**: Nombre de jours après envoi pour déclencher une relance (1-30)
    - **dry_run**: Mode test (ne pas envoyer réellement)
    - **max_concurrent**: Nombre de relances simultanées (1-3)
    """
    try:
        result = await service.process_followups(request)

        return APIResponse(
            success=True, message=f"{result.followups_sent} relances traitées avec succès", data=result.model_dump()
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur traitement relances: {str(e)}")


@router.get("/stats", response_model=APIResponse)
async def get_followup_stats(days_back: int = 30):
    """
    Récupère les statistiques de relance

    - **days_back**: Nombre de jours en arrière pour les statistiques
    """
    try:
        stats = await service.get_followup_stats(days_back)

        return APIResponse(success=True, message="Statistiques de relance récupérées", data=stats.model_dump())

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur statistiques relances: {str(e)}")


@router.get("/pending", response_model=APIResponse)
async def get_pending_followups():
    """
    Récupère la liste des emails en attente de relance
    """
    try:
        # TODO: Implémenter la récupération des emails en attente

        return APIResponse(
            success=True,
            message="Emails en attente de relance récupérés",
            data={"pending_emails": [], "total_pending": 0, "next_batch_date": None},
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur récupération emails en attente: {str(e)}")


@router.post("/{email_id}/schedule", response_model=APIResponse)
async def schedule_followup(email_id: int, days_delay: int = 7):
    """
    Programme une relance pour un email spécifique

    - **email_id**: Identifiant de l'email
    - **days_delay**: Nombre de jours avant la relance
    """
    try:
        # TODO: Implémenter la programmation de relance

        return APIResponse(success=True, message=f"Relance programmée pour l'email {email_id} dans {days_delay} jours")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur programmation relance: {str(e)}")


@router.delete("/{email_id}/cancel", response_model=APIResponse)
async def cancel_followup(email_id: int):
    """
    Annule une relance programmée

    - **email_id**: Identifiant de l'email
    """
    try:
        # TODO: Implémenter l'annulation de relance

        return APIResponse(success=True, message=f"Relance annulée pour l'email {email_id}")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur annulation relance: {str(e)}")


@router.get("/templates", response_model=APIResponse)
async def get_followup_templates():
    """
    Récupère les templates de relance disponibles
    """
    try:
        # TODO: Implémenter la récupération des templates

        return APIResponse(
            success=True,
            message="Templates de relance récupérés",
            data={
                "templates": [
                    {
                        "id": "default",
                        "name": "Relance standard",
                        "subject_template": "Suivi - {original_subject}",
                        "body_template": "Template de relance par défaut",
                    }
                ]
            },
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur récupération templates: {str(e)}")


@router.post("/templates", response_model=APIResponse)
async def create_followup_template():
    """
    Crée un nouveau template de relance
    """
    try:
        # TODO: Implémenter la création de template

        return APIResponse(success=True, message="Template de relance créé avec succès")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur création template: {str(e)}")


@router.get("/performance", response_model=APIResponse)
async def get_followup_performance(days_back: int = 30):
    """
    Récupère les performances des relances

    - **days_back**: Nombre de jours en arrière
    """
    try:
        stats = await service.get_followup_stats(days_back)

        # Calculer des métriques supplémentaires
        performance_data = {
            "followup_conversion_rate": stats.followup_response_rate,
            "total_followups_sent": stats.followups_sent,
            "replies_generated": stats.replies_after_followup,
            "roi_improvement": 0,  # À calculer
            "best_followup_timing": "7 jours",  # À analyser
            "response_time_analysis": {},  # À implémenter
        }

        return APIResponse(success=True, message="Performance des relances récupérée", data=performance_data)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur performance relances: {str(e)}")


@router.post("/bulk-schedule", response_model=APIResponse)
async def bulk_schedule_followups():
    """
    Programme des relances en masse pour tous les emails éligibles
    """
    try:
        # TODO: Implémenter la programmation en masse

        return APIResponse(
            success=True,
            message="Relances programmées en masse avec succès",
            data={"scheduled_count": 0, "skipped_count": 0, "errors": []},
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur programmation en masse: {str(e)}")


@router.get("/calendar", response_model=APIResponse)
async def get_followup_calendar():
    """
    Récupère le calendrier des relances programmées
    """
    try:
        # TODO: Implémenter le calendrier des relances

        return APIResponse(
            success=True,
            message="Calendrier des relances récupéré",
            data={"upcoming_followups": [], "by_date": {}, "total_scheduled": 0},
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur calendrier relances: {str(e)}")
