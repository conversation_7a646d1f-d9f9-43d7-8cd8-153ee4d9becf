"""
Routeur pour les statistiques détaillées
"""

import logging
from datetime import datetime, timedelta
from typing import List, Optional

from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel

from api.schemas import APIResponse
from api.services import ProspectionAPIService

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/statistics", tags=["statistics"])

# Modèles Pydantic
class DailyBreakdown(BaseModel):
    date: str
    count: int

class Statistics(BaseModel):
    period_days: int
    total_sent: int
    successful: int
    failed: int
    opened: int
    clicked: int
    replied: int
    today_emails: int
    success_rate: float
    open_rate: float
    click_rate: float
    reply_rate: float
    daily_breakdown: List[DailyBreakdown]

# Instance du service
service = ProspectionAPIService()

@router.get("", response_model=APIResponse)
async def get_statistics(days_back: int = Query(30, description="Nombre de jours à analyser")):
    """
    Récupère les statistiques détaillées
    """
    try:
        # Récupérer les statistiques depuis la base de données
        await service.initialize()
        
        # Utiliser le database manager pour récupérer les vraies statistiques
        db_manager = service.db_manager
        
        # Récupérer les statistiques depuis la base de données
        stats_data = await db_manager.get_statistics(days_back=days_back)
        
        # Utiliser les statistiques de la base de données
        if not stats_data:
            # Si pas de données, retourner des statistiques vides
            stats_data = {
                "period_days": days_back,
                "total_sent": 0,
                "successful": 0,
                "failed": 0,
                "opened": 0,
                "clicked": 0,
                "replied": 0,
                "today_emails": 0,
                "success_rate": 0,
                "open_rate": 0,
                "click_rate": 0,
                "reply_rate": 0,
                "daily_breakdown": []
            }

        # Convertir daily_breakdown en format attendu
        daily_breakdown = []
        for item in stats_data.get("daily_breakdown", []):
            daily_breakdown.append(DailyBreakdown(
                date=item.get("date", ""),
                count=item.get("count", 0)
            ))

        stats = Statistics(
            period_days=stats_data.get("period_days", days_back),
            total_sent=stats_data.get("total_sent", 0),
            successful=stats_data.get("successful", 0),
            failed=stats_data.get("failed", 0),
            opened=stats_data.get("opened", 0),
            clicked=stats_data.get("clicked", 0),
            replied=stats_data.get("replied", 0),
            today_emails=stats_data.get("today_emails", 0),
            success_rate=stats_data.get("success_rate", 0),
            open_rate=stats_data.get("open_rate", 0),
            click_rate=stats_data.get("click_rate", 0),
            reply_rate=stats_data.get("reply_rate", 0),
            daily_breakdown=daily_breakdown
        )
        
        logger.info(f"Statistiques calculées pour {days_back} jours: {stats.total_sent} emails")
        
        return APIResponse(
            success=True,
            message="Statistiques récupérées",
            data=stats.model_dump()
        )
        
    except Exception as e:
        logger.error(f"Erreur récupération statistiques: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/summary", response_model=APIResponse)
async def get_statistics_summary():
    """
    Récupère un résumé des statistiques
    """
    try:
        await service.initialize()
        db_manager = service.db_manager
        
        # Récupérer les emails envoyés
        sent_emails = await db_manager.get_sent_emails(limit=1000)
        
        # Calculer le résumé
        total_sent = len(sent_emails)
        successful = len([e for e in sent_emails if e.get('status') == 'sent'])
        open_rate = len([e for e in sent_emails if e.get('opened', False)]) / successful * 100 if successful > 0 else 0
        reply_rate = len([e for e in sent_emails if e.get('replied', False)]) / successful * 100 if successful > 0 else 0
        
        summary = {
            "total_emails": total_sent,
            "success_rate": successful / total_sent * 100 if total_sent > 0 else 0,
            "open_rate": open_rate,
            "reply_rate": reply_rate,
            "last_updated": datetime.now().isoformat()
        }
        
        return APIResponse(
            success=True,
            message="Résumé des statistiques",
            data=summary
        )
        
    except Exception as e:
        logger.error(f"Erreur résumé statistiques: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/performance", response_model=APIResponse)
async def get_performance_metrics():
    """
    Récupère les métriques de performance
    """
    try:
        await service.initialize()
        db_manager = service.db_manager
        
        # Récupérer les emails envoyés
        sent_emails = await db_manager.get_sent_emails(limit=1000)
        
        # Calculer les métriques de performance
        total_sent = len(sent_emails)
        
        # Métriques par statut
        status_breakdown = {}
        for email in sent_emails:
            status = email.get('status', 'unknown')
            status_breakdown[status] = status_breakdown.get(status, 0) + 1
        
        # Métriques d'engagement
        engagement_metrics = {
            "emails_opened": len([e for e in sent_emails if e.get('opened', False)]),
            "emails_clicked": len([e for e in sent_emails if e.get('clicked', False)]),
            "emails_replied": len([e for e in sent_emails if e.get('replied', False)]),
        }
        
        # Métriques temporelles
        today = datetime.now().date()
        yesterday = today - timedelta(days=1)
        this_week = today - timedelta(days=7)
        
        temporal_metrics = {
            "today": len([e for e in sent_emails if e.get('sent_at', '').startswith(str(today))]),
            "yesterday": len([e for e in sent_emails if e.get('sent_at', '').startswith(str(yesterday))]),
            "this_week": len([e for e in sent_emails if e.get('sent_at', '').startswith(str(this_week)) or 
                            e.get('sent_at', '') > str(this_week)]),
        }
        
        performance = {
            "total_emails": total_sent,
            "status_breakdown": status_breakdown,
            "engagement_metrics": engagement_metrics,
            "temporal_metrics": temporal_metrics,
            "calculated_at": datetime.now().isoformat()
        }
        
        return APIResponse(
            success=True,
            message="Métriques de performance",
            data=performance
        )
        
    except Exception as e:
        logger.error(f"Erreur métriques performance: {e}")
        raise HTTPException(status_code=500, detail=str(e))
