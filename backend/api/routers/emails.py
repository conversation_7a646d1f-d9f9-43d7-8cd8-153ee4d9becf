"""
Routeur API pour la gestion des emails
"""

from typing import Optional

from fastapi import APIRouter, HTTPException

from api.schemas import APIResponse, EmailGenerationRequest, EmailSendRequest
from api.services import ProspectionAPIService

router = APIRouter(prefix="/emails", tags=["emails"])

# Instance du service
service = ProspectionAPIService()


@router.post("/generate", response_model=APIResponse)
async def generate_emails(request: EmailGenerationRequest):
    """
    Génère des emails personnalisés pour les entreprises analysées

    - **context_ids**: IDs des contextes d'entreprises (optionnel)
    - **max_concurrent**: Nombre de générations simultanées (1-5)
    - **skip_good_websites**: Ignorer les entreprises avec de bons sites web
    """
    try:
        # Récupérer les contextes depuis la base de données
        contexts = await service.get_company_contexts(request.context_ids)

        if not contexts:
            return APIResponse(
                success=True,
                message="Aucun contexte d'entreprise trouvé pour la génération d'emails",
                data={
                    "emails": [],
                    "total_generated": 0,
                    "generation_time": 0.0,
                    "skipped_count": 0,
                }
            )

        result = await service.generate_emails(request, contexts)

        return APIResponse(
            success=True,
            message=f"{result.total_generated} emails générés avec succès",
            data={
                "emails": result.emails,
                "total_generated": result.total_generated,
                "generation_time": result.generation_time,
                "skipped_count": result.skipped_count,
            },
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur génération emails: {str(e)}")


@router.post("/send", response_model=APIResponse)
async def send_emails(request: EmailSendRequest):
    """
    Envoie les emails générés

    - **email_ids**: IDs des emails à envoyer (optionnel)
    - **use_sendgrid**: Utiliser SendGrid au lieu de SMTP
    - **dry_run**: Mode test (ne pas envoyer réellement)
    - **max_concurrent**: Nombre d'envois simultanés (1-3)
    """
    try:
        # TODO: Récupérer les emails et entreprises depuis la base
        companies_and_templates = []  # À implémenter

        if not companies_and_templates:
            return APIResponse(success=False, message="Aucun email trouvé pour l'envoi")

        result = await service.send_emails(request, companies_and_templates)

        return APIResponse(
            success=True,
            message=f"{result.successful} emails envoyés avec succès",
            data={
                "total_sent": result.total_sent,
                "successful": result.successful,
                "failed": result.failed,
                "duplicates": result.duplicates,
                "send_time": result.send_time,
                "emails": [email.model_dump() for email in result.emails],
            },
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur envoi emails: {str(e)}")


@router.get("/sent", response_model=APIResponse)
async def get_sent_emails(
    limit: int = 100,
    offset: int = 0,
    status: Optional[str] = None,
    company_name: Optional[str] = None,
    days_back: Optional[int] = 30,
):
    """
    Récupère la liste des emails envoyés avec pagination et filtres

    - **limit**: Nombre d'éléments à récupérer
    - **offset**: Décalage pour la pagination
    - **status**: Filtrer par statut (sent, opened, clicked, replied, etc.)
    - **company_name**: Filtrer par nom d'entreprise
    - **days_back**: Nombre de jours en arrière
    """
    try:
        await service.initialize()
        db_manager = service.db_manager

        # Récupérer les emails envoyés depuis la base de données
        sent_emails = await db_manager.get_sent_emails(limit=limit + offset)

        # Appliquer les filtres si nécessaire
        if status:
            sent_emails = [email for email in sent_emails if email.get('status') == status]

        if company_name:
            sent_emails = [email for email in sent_emails if company_name.lower() in email.get('company_name', '').lower()]

        # Appliquer la pagination manuelle
        paginated_emails = sent_emails[offset:offset + limit] if offset < len(sent_emails) else []

        return APIResponse(
            success=True,
            message=f"{len(paginated_emails)} emails récupérés",
            data=paginated_emails
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur récupération emails: {str(e)}")

@router.get("/", response_model=APIResponse)
async def get_emails_list(
    page: int = 1,
    limit: int = 20,
    status: Optional[str] = None,
    company_name: Optional[str] = None,
    days_back: Optional[int] = 30,
):
    """
    Récupère la liste des emails avec pagination

    - **page**: Numéro de page
    - **limit**: Nombre d'éléments par page
    - **status**: Filtrer par statut (sent, opened, clicked, replied, etc.)
    - **company_name**: Filtrer par nom d'entreprise
    - **days_back**: Nombre de jours en arrière
    """
    try:
        await service.initialize()
        db_manager = service.db_manager

        # Calculer l'offset
        offset = (page - 1) * limit

        # Récupérer les emails envoyés depuis la base de données
        sent_emails = await db_manager.get_sent_emails(limit=limit + offset)

        # Appliquer les filtres si nécessaire
        if status:
            sent_emails = [email for email in sent_emails if email.get('status') == status]

        if company_name:
            sent_emails = [email for email in sent_emails if company_name.lower() in email.get('company_name', '').lower()]

        # Appliquer la pagination manuelle
        paginated_emails = sent_emails[offset:offset + limit] if offset < len(sent_emails) else []

        total = len(sent_emails)
        total_pages = (total + limit - 1) // limit

        return APIResponse(
            success=True,
            message="Emails récupérés avec succès",
            data={"emails": paginated_emails, "total": total, "page": page, "limit": limit, "total_pages": total_pages},
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur récupération emails: {str(e)}")


@router.get("/{email_id}", response_model=APIResponse)
async def get_email(email_id: int):
    """
    Récupère un email spécifique par son ID

    - **email_id**: Identifiant de l'email
    """
    try:
        # TODO: Implémenter la récupération d'un email spécifique

        return APIResponse(success=True, message="Email récupéré avec succès", data={"email": None})  # À implémenter

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur récupération email: {str(e)}")


@router.get("/{email_id}/tracking", response_model=APIResponse)
async def get_email_tracking(email_id: int):
    """
    Récupère les informations de tracking d'un email

    - **email_id**: Identifiant de l'email
    """
    try:
        # TODO: Implémenter le tracking d'un email spécifique

        return APIResponse(
            success=True,
            message="Tracking récupéré avec succès",
            data={
                "tracking": {
                    "email_id": email_id,
                    "opened": False,
                    "clicked": False,
                    "first_open": None,
                    "first_click": None,
                    "total_opens": 0,
                    "total_clicks": 0,
                }
            },
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur tracking email: {str(e)}")


@router.post("/{email_id}/resend", response_model=APIResponse)
async def resend_email(email_id: int, dry_run: bool = True):
    """
    Renvoie un email spécifique

    - **email_id**: Identifiant de l'email à renvoyer
    - **dry_run**: Mode test
    """
    try:
        # TODO: Implémenter le renvoi d'email

        return APIResponse(
            success=True, message="Email renvoyé avec succès" if not dry_run else "Simulation de renvoi réussie"
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur renvoi email: {str(e)}")


@router.get("/stats/performance", response_model=APIResponse)
async def get_email_performance(days_back: int = 30):
    """
    Récupère les statistiques de performance des emails

    - **days_back**: Nombre de jours en arrière
    """
    try:
        tracking_stats = await service.get_tracking_stats(days_back)

        return APIResponse(success=True, message="Statistiques de performance récupérées", data=tracking_stats.model_dump())

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur statistiques emails: {str(e)}")


@router.get("/stats/subjects", response_model=APIResponse)
async def get_subject_performance():
    """
    Récupère les performances par sujet d'email
    """
    try:
        # TODO: Implémenter l'analyse des sujets

        return APIResponse(
            success=True,
            message="Performance des sujets récupérée",
            data={"best_subjects": [], "worst_subjects": [], "average_open_rate": 0, "average_click_rate": 0},
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur performance sujets: {str(e)}")


@router.delete("/{email_id}", response_model=APIResponse)
async def delete_email(email_id: int):
    """
    Supprime un email de l'historique

    - **email_id**: Identifiant de l'email à supprimer
    """
    try:
        # TODO: Implémenter la suppression

        return APIResponse(success=True, message="Email supprimé avec succès")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur suppression email: {str(e)}")


@router.post("/templates/test", response_model=APIResponse)
async def test_email_template():
    """
    Teste un template d'email avec des données fictives
    """
    try:
        # TODO: Implémenter le test de template

        return APIResponse(
            success=True,
            message="Template testé avec succès",
            data={"preview": {"subject": "Test Subject", "body": "Test Body", "personalization_applied": True}},
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur test template: {str(e)}")
