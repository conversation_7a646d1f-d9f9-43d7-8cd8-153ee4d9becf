"""
Routeur API pour la gestion des leads
"""

import logging
from typing import Optional

from fastapi import APIRouter, HTTPException

from api.schemas import AnalysisRequest, APIResponse, LeadGenerationRequest
from api.services import ProspectionAPIService

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/leads", tags=["leads"])

# Instance du service (sera injectée)
service = ProspectionAPIService()


@router.post("/generate", response_model=APIResponse)
async def generate_leads(request: LeadGenerationRequest):
    """
    Génère des leads selon les critères spécifiés

    - **max_results**: Nombre maximum de leads à générer (1-1000)
    - **naf_codes**: Codes NAF à cibler (optionnel)
    - **regions**: Régions à cibler (optionnel)
    - **recent_only**: Uniquement les entreprises récentes
    - **days_back**: Nombre de jours en arrière pour "récent"
    """
    try:
        result = await service.generate_leads(request)

        return APIResponse(
            success=True,
            message=f"{result.total_generated} leads générés avec succès",
            data={
                "leads": [lead.model_dump() for lead in result.leads],
                "total_generated": result.total_generated,
                "filters_applied": result.filters_applied,
                "generation_time": result.generation_time,
            },
        )

    except ValueError as e:
        # Erreur de verrou (génération déjà en cours)
        if "génération de leads est déjà en cours" in str(e):
            raise HTTPException(
                status_code=409,  # Conflict
                detail="Une génération de leads est déjà en cours. Veuillez attendre qu'elle se termine."
            )
        else:
            raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur génération leads: {str(e)}")


@router.get("/generation/status", response_model=APIResponse)
async def get_generation_status():
    """
    Vérifie si une génération de leads est en cours
    """
    try:
        # Importer la variable globale
        from api.services import _generation_in_progress

        return APIResponse(
            success=True,
            message="Statut de génération récupéré",
            data={
                "is_generating": _generation_in_progress,
                "message": "Génération en cours..." if _generation_in_progress else "Aucune génération en cours"
            }
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur vérification statut: {str(e)}")


@router.post("/generation/unlock", response_model=APIResponse)
async def unlock_generation():
    """
    Force la libération du verrou de génération (à utiliser en cas de blocage)
    """
    try:
        # Importer et modifier la variable globale
        import api.services
        api.services._generation_in_progress = False

        return APIResponse(
            success=True,
            message="Verrou de génération libéré avec succès",
            data={"is_generating": False}
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur libération verrou: {str(e)}")


@router.get("/", response_model=APIResponse)
async def get_leads(
    limit: int = 100,
    offset: int = 0,
    source: Optional[str] = None,
    city: Optional[str] = None,
    has_email: Optional[bool] = None
):
    """
    Récupère la liste des leads avec pagination et filtres

    - **limit**: Nombre d'éléments à récupérer (défaut: 100, max: 1000)
    - **offset**: Décalage pour la pagination (défaut: 0)
    - **source**: Filtrer par source (sirene, manual, etc.)
    - **city**: Filtrer par ville
    - **has_email**: Filtrer par présence d'email
    """
    try:
        # Récupérer les leads depuis la base de données
        leads = await service.get_leads(limit=limit, offset=offset)

        # Convertir en dictionnaires pour la réponse
        leads_data = []
        for lead in leads:
            lead_dict = {
                "name": lead.name,
                "siren": lead.siren,
                "city": lead.city,
                "email": lead.email,
                "website": lead.website,
                "naf_code": lead.naf_code,
                "naf_label": lead.naf_label,
                "employees": lead.employees,
                "source": lead.source,
                "created_at": lead.created_at.isoformat() if lead.created_at else None,
            }
            leads_data.append(lead_dict)

        # Appliquer les filtres (TODO: déplacer vers la requête SQL)
        if source:
            leads_data = [lead for lead in leads_data if lead.get('source', '').lower() == source.lower()]

        if city:
            leads_data = [lead for lead in leads_data if city.lower() in lead.get('city', '').lower()]

        if has_email is not None:
            if has_email:
                leads_data = [lead for lead in leads_data if lead.get('email')]
            else:
                leads_data = [lead for lead in leads_data if not lead.get('email')]

        return APIResponse(
            success=True,
            message=f"{len(leads_data)} leads récupérés",
            data=leads_data
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur récupération leads: {str(e)}")


@router.get("/{lead_id}", response_model=APIResponse)
async def get_lead(lead_id: str):
    """
    Récupère un lead spécifique par son ID

    - **lead_id**: Identifiant du lead (SIREN ou ID interne)
    """
    try:
        # TODO: Implémenter la récupération d'un lead spécifique

        return APIResponse(success=True, message="Lead récupéré avec succès", data={"lead": None})  # À implémenter

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur récupération lead: {str(e)}")


@router.post("/analyze", response_model=APIResponse)
async def analyze_leads(request: AnalysisRequest):
    """
    Analyse les leads pour détecter les pain points et calculer les scores

    - **company_ids**: Liste des IDs d'entreprises à analyser (optionnel)
    - **max_concurrent**: Nombre d'analyses simultanées (1-10)
    """
    try:
        # Récupérer les entreprises depuis la base de données
        companies = []

        if request.company_ids:
            # Récupérer les entreprises spécifiques
            import aiomysql
            connection = await aiomysql.connect(
                host="127.0.0.1", port=3306, user="root", password="MLKqsd002",
                db="prospection_b2b", charset="utf8mb4"
            )

            try:
                async with connection.cursor() as cursor:
                    placeholders = ','.join(['%s'] * len(request.company_ids))
                    await cursor.execute(f"""
                        SELECT company_name, siret, siren, naf_code, naf_label,
                               city, email, website, employee_count, source
                        FROM leads
                        WHERE siren IN ({placeholders})
                    """, request.company_ids)

                    rows = await cursor.fetchall()

                    # Convertir en objets Company
                    from models import Company, LeadSource
                    from urllib.parse import urlparse

                    for row in rows:
                        try:
                            website = None
                            if row[7]:  # website
                                website = urlparse(row[7]) if row[7].startswith('http') else urlparse(f'https://{row[7]}')

                            company = Company(
                                name=row[0],
                                siren=row[2],
                                city=row[5],
                                email=row[6],
                                website=website,
                                naf_code=row[3],
                                naf_label=row[4],
                                employees=row[8],
                                source=LeadSource(row[9]) if row[9] else LeadSource.SIRENE
                            )
                            companies.append(company)
                        except Exception as e:
                            logging.warning(f"Erreur conversion company {row[0]}: {e}")
                            continue

            finally:
                connection.close()

        if not companies:
            return APIResponse(success=False, message="Aucune entreprise trouvée pour l'analyse", data={"total_analyzed": 0})

        result = await service.analyze_companies(request, companies)

        return APIResponse(
            success=True,
            message=f"{result.total_analyzed} entreprises analysées avec succès",
            data={
                "contexts": [ctx.model_dump() for ctx in result.contexts],
                "total_analyzed": result.total_analyzed,
                "analysis_time": result.analysis_time,
                "statistics": result.statistics,
            },
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur analyse leads: {str(e)}")


@router.get("/stats/summary", response_model=APIResponse)
async def get_leads_stats():
    """
    Récupère les statistiques des leads
    """
    try:
        # TODO: Implémenter les statistiques des leads

        return APIResponse(
            success=True,
            message="Statistiques des leads récupérées",
            data={
                "total_leads": 0,
                "with_email": 0,
                "analyzed": 0,
                "high_priority": 0,
                "by_source": {},
                "by_region": {},
                "by_sector": {},
            },
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur statistiques leads: {str(e)}")


@router.delete("/{lead_id}", response_model=APIResponse)
async def delete_lead(lead_id: str):
    """
    Supprime un lead

    - **lead_id**: Identifiant du lead à supprimer
    """
    try:
        # TODO: Implémenter la suppression

        return APIResponse(success=True, message="Lead supprimé avec succès")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur suppression lead: {str(e)}")


@router.post("/import", response_model=APIResponse)
async def import_leads():
    """
    Importe des leads depuis un fichier CSV
    """
    try:
        # TODO: Implémenter l'import CSV

        return APIResponse(
            success=True, message="Leads importés avec succès", data={"imported": 0, "skipped": 0, "errors": []}
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur import leads: {str(e)}")


@router.post("/export", response_model=APIResponse)
async def export_leads():
    """
    Exporte les leads vers un fichier CSV
    """
    try:
        # TODO: Implémenter l'export CSV

        return APIResponse(
            success=True,
            message="Leads exportés avec succès",
            data={"file_url": "/downloads/leads_export.csv", "total_exported": 0},
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur export leads: {str(e)}")
