"""
Routeur API pour le tracking et les réponses
"""

from typing import Optional

from fastapi import APIRouter, HTTPException, Request

from api.schemas import APIResponse, WebhookEventRequest
from api.services import ProspectionAPIService

router = APIRouter(prefix="/tracking", tags=["tracking"])

# Instance du service
service = ProspectionAPIService()


@router.get("/stats", response_model=APIResponse)
async def get_tracking_stats(days_back: int = 30):
    """
    Récupère les statistiques de tracking

    - **days_back**: Nombre de jours en arrière pour les statistiques
    """
    try:
        stats = await service.get_tracking_stats(days_back)

        return APIResponse(success=True, message="Statistiques de tracking récupérées", data=stats.model_dump())

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur statistiques tracking: {str(e)}")


@router.get("/replies/check", response_model=APIResponse)
async def check_replies(hours_back: int = 24):
    """
    Vérifie les nouvelles réponses aux emails

    - **hours_back**: Nombre d'heures en arrière pour vérifier
    """
    try:
        result = await service.check_replies(hours_back)

        return APIResponse(success=True, message=f"{result.total_replies} réponses détectées", data=result.model_dump())

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur vérification réponses: {str(e)}")


@router.get("/replies", response_model=APIResponse)
async def get_replies(page: int = 1, limit: int = 20, reply_type: Optional[str] = None, days_back: int = 30):
    """
    Récupère la liste des réponses avec pagination et filtres

    - **page**: Numéro de page
    - **limit**: Nombre d'éléments par page
    - **reply_type**: Filtrer par type (positive, negative, neutral, auto_reply)
    - **days_back**: Nombre de jours en arrière
    """
    try:
        # TODO: Implémenter la récupération des réponses depuis la base

        return APIResponse(
            success=True,
            message="Réponses récupérées avec succès",
            data={
                "replies": [],
                "total": 0,
                "page": page,
                "limit": limit,
                "total_pages": 0,
                "by_type": {"positive": 0, "negative": 0, "neutral": 0, "auto_reply": 0},
            },
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur récupération réponses: {str(e)}")


@router.post("/replies/{reply_id}/classify", response_model=APIResponse)
async def classify_reply(reply_id: int, reply_type: str):
    """
    Reclassifie une réponse manuellement

    - **reply_id**: Identifiant de la réponse
    - **reply_type**: Nouveau type (positive, negative, neutral, auto_reply)
    """
    try:
        # TODO: Implémenter la reclassification

        return APIResponse(success=True, message=f"Réponse {reply_id} reclassifiée comme {reply_type}")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur classification réponse: {str(e)}")


@router.get("/opens", response_model=APIResponse)
async def get_email_opens(days_back: int = 30):
    """
    Récupère les statistiques d'ouverture d'emails

    - **days_back**: Nombre de jours en arrière
    """
    try:
        # TODO: Implémenter les statistiques d'ouverture détaillées

        return APIResponse(
            success=True,
            message="Statistiques d'ouverture récupérées",
            data={
                "total_opens": 0,
                "unique_opens": 0,
                "open_rate": 0,
                "opens_by_day": {},
                "opens_by_hour": {},
                "top_opening_companies": [],
            },
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur statistiques ouvertures: {str(e)}")


@router.get("/clicks", response_model=APIResponse)
async def get_email_clicks(days_back: int = 30):
    """
    Récupère les statistiques de clics d'emails

    - **days_back**: Nombre de jours en arrière
    """
    try:
        # TODO: Implémenter les statistiques de clics détaillées

        return APIResponse(
            success=True,
            message="Statistiques de clics récupérées",
            data={
                "total_clicks": 0,
                "unique_clicks": 0,
                "click_rate": 0,
                "clicks_by_day": {},
                "clicks_by_link": {},
                "top_clicking_companies": [],
            },
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur statistiques clics: {str(e)}")


@router.get("/funnel", response_model=APIResponse)
async def get_conversion_funnel(days_back: int = 30):
    """
    Récupère le funnel de conversion

    - **days_back**: Nombre de jours en arrière
    """
    try:
        campaign_stats = await service.get_campaign_stats(days_back)

        funnel_data = {
            "emails_sent": campaign_stats.emails_sent,
            "emails_delivered": campaign_stats.emails_sent,  # Approximation
            "emails_opened": campaign_stats.emails_opened,
            "emails_clicked": campaign_stats.emails_clicked,
            "emails_replied": campaign_stats.emails_replied,
            "conversion_rates": {
                "delivery_rate": 100.0,  # Approximation
                "open_rate": campaign_stats.open_rate,
                "click_rate": campaign_stats.click_rate,
                "reply_rate": campaign_stats.reply_rate,
            },
            "funnel_steps": [
                {"step": "Envoyés", "count": campaign_stats.emails_sent, "rate": 100.0},
                {"step": "Ouverts", "count": campaign_stats.emails_opened, "rate": campaign_stats.open_rate},
                {"step": "Cliqués", "count": campaign_stats.emails_clicked, "rate": campaign_stats.click_rate},
                {"step": "Réponses", "count": campaign_stats.emails_replied, "rate": campaign_stats.reply_rate},
            ],
        }

        return APIResponse(success=True, message="Funnel de conversion récupéré", data=funnel_data)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur funnel conversion: {str(e)}")


@router.get("/heatmap", response_model=APIResponse)
async def get_activity_heatmap(days_back: int = 30):
    """
    Récupère la heatmap d'activité (ouvertures/clics par heure/jour)

    - **days_back**: Nombre de jours en arrière
    """
    try:
        # TODO: Implémenter la heatmap d'activité

        return APIResponse(
            success=True,
            message="Heatmap d'activité récupérée",
            data={"heatmap_data": [], "peak_hours": [], "peak_days": [], "timezone": "Europe/Paris"},
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur heatmap activité: {str(e)}")


# Endpoints pour les webhooks de tracking
@router.get("/open/{tracking_id}.png")
async def track_email_open(tracking_id: str, request: Request):
    """
    Endpoint pour tracker l'ouverture d'email (pixel invisible)

    - **tracking_id**: Identifiant de tracking de l'email
    """
    try:
        # Récupérer l'IP et User-Agent
        client_ip = request.client.host
        user_agent = request.headers.get("user-agent", "")

        # TODO: Enregistrer l'ouverture
        # await service.record_email_open(tracking_id, client_ip, user_agent)

        # Retourner un pixel transparent 1x1
        from fastapi.responses import Response

        # Pixel PNG transparent 1x1
        pixel_data = b"\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x06\x00\x00\x00\x1f\x15\xc4\x89\x00\x00\x00\nIDATx\x9cc\x00\x01\x00\x00\x05\x00\x01\r\n-\xdb\x00\x00\x00\x00IEND\xaeB`\x82"

        return Response(
            content=pixel_data,
            media_type="image/png",
            headers={"Cache-Control": "no-cache, no-store, must-revalidate", "Pragma": "no-cache", "Expires": "0"},
        )

    except Exception as e:
        # En cas d'erreur, retourner quand même le pixel
        from fastapi.responses import Response

        pixel_data = b"\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x06\x00\x00\x00\x1f\x15\xc4\x89\x00\x00\x00\nIDATx\x9cc\x00\x01\x00\x00\x05\x00\x01\r\n-\xdb\x00\x00\x00\x00IEND\xaeB`\x82"
        return Response(content=pixel_data, media_type="image/png")


@router.get("/click/{tracking_id}/{link_id}")
async def track_email_click(tracking_id: str, link_id: str, url: str, request: Request):
    """
    Endpoint pour tracker les clics sur les liens

    - **tracking_id**: Identifiant de tracking de l'email
    - **link_id**: Identifiant du lien cliqué
    - **url**: URL originale (encodée en base64)
    """
    try:
        # Récupérer l'IP et User-Agent
        client_ip = request.client.host
        user_agent = request.headers.get("user-agent", "")

        # TODO: Enregistrer le clic
        # original_url = await service.record_email_click(tracking_id, link_id, client_ip, user_agent)

        # Décoder l'URL originale
        import base64

        try:
            original_url = base64.urlsafe_b64decode(url.encode()).decode()
        except Exception:
            original_url = "https://www.slconception.fr"  # URL par défaut

        # Rediriger vers l'URL originale
        from fastapi.responses import RedirectResponse

        return RedirectResponse(url=original_url, status_code=302)

    except Exception as e:
        # En cas d'erreur, rediriger vers le site principal
        from fastapi.responses import RedirectResponse

        return RedirectResponse(url="https://www.slconception.fr", status_code=302)


@router.post("/webhook", response_model=APIResponse)
async def tracking_webhook(event: WebhookEventRequest):
    """
    Webhook pour recevoir les événements de tracking externes

    - **event**: Événement de tracking (ouverture, clic, réponse)
    """
    try:
        # TODO: Traiter l'événement webhook

        return APIResponse(success=True, message=f"Événement {event.event_type} traité avec succès")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur traitement webhook: {str(e)}")
