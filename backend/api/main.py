"""
Application FastAPI principale pour l'API de prospection B2B
"""

import logging
import os
from contextlib import asynccontextmanager
from datetime import datetime

from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.openapi.utils import get_openapi
from fastapi.responses import JSONResponse

# Import des routeurs
from api.routers import dashboard, emails, followups, leads, tracking, campaigns, statistics, metrics
from api.schemas import APIResponse
from api.services import ProspectionAPIService

# Configuration du logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

# Instance globale du service
service_instance = ProspectionAPIService()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Gestionnaire de cycle de vie de l'application"""
    # Startup
    logger.info("🚀 Démarrage de l'API de prospection B2B")
    try:
        await service_instance.initialize()
        logger.info("✅ Services initialisés avec succès")
    except Exception as e:
        logger.error(f"❌ Erreur initialisation services: {e}")

    yield

    # Shutdown
    logger.info("🛑 Arrêt de l'API de prospection B2B")


# Création de l'application FastAPI
app = FastAPI(
    title="API de Prospection B2B",
    description="""
    ## API REST complète pour le système de prospection B2B automatisé

    Cette API permet de gérer l'ensemble du processus de prospection :

    ### 🎯 **Fonctionnalités principales**

    * **Génération de leads** : Recherche d'entreprises via l'API Sirene
    * **Analyse intelligente** : Détection des pain points avec IA
    * **Emails personnalisés** : Génération automatique avec OpenAI
    * **Envoi professionnel** : SMTP/SendGrid avec anti-spam
    * **Tracking complet** : Ouvertures, clics, réponses
    * **Relances automatiques** : Suivi intelligent après 7 jours
    * **Statistiques avancées** : Dashboard et métriques de performance

    ### 🔧 **Technologies utilisées**

    * **FastAPI** : Framework web moderne et performant
    * **MySQL** : Base de données relationnelle
    * **OpenAI GPT-4** : Intelligence artificielle pour la personnalisation
    * **IMAP/SMTP** : Gestion des emails
    * **API Sirene** : Données officielles des entreprises françaises

    ### 📊 **Métriques de performance**

    * **Coût** : ~0.01€ par email personnalisé
    * **Taux de réponse** : 2-5% (moyenne du secteur)
    * **Personnalisation** : 100% (nom, ville, situation)
    * **Tracking** : 100% des interactions détectées

    ### 🚀 **Utilisation**

    1. **Générer des leads** avec `/leads/generate`
    2. **Analyser les entreprises** avec `/leads/analyze`
    3. **Créer des emails** avec `/emails/generate`
    4. **Envoyer la campagne** avec `/emails/send`
    5. **Suivre les performances** avec `/dashboard/overview`
    6. **Gérer les relances** avec `/followups/process`

    ### 📈 **Dashboard en temps réel**

    Accédez aux statistiques complètes via les endpoints `/dashboard/*`
    """,
    version="1.0.0",
    contact={"name": "Rochdi Sami", "email": "<EMAIL>", "url": "https://www.slconception.fr"},
    license_info={"name": "MIT License", "url": "https://opensource.org/licenses/MIT"},
    lifespan=lifespan,
    docs_url="/docs",
    redoc_url="/redoc",
)

# Configuration CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # En production, spécifier les domaines autorisés
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Middleware de sécurité
app.add_middleware(TrustedHostMiddleware, allowed_hosts=["*"])  # En production, spécifier les hosts autorisés


# Middleware de logging des requêtes
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """Log toutes les requêtes"""
    start_time = datetime.now()

    # Traiter la requête
    response = await call_next(request)

    # Calculer le temps de traitement
    process_time = (datetime.now() - start_time).total_seconds()

    # Logger la requête
    logger.info(
        f"{request.method} {request.url.path} - "
        f"Status: {response.status_code} - "
        f"Time: {process_time:.3f}s - "
        f"Client: {request.client.host if request.client else 'unknown'}"
    )

    # Ajouter le temps de traitement dans les headers
    response.headers["X-Process-Time"] = str(process_time)

    return response


# Gestionnaire d'erreurs global
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Gestionnaire d'erreurs global"""
    logger.error(f"Erreur non gérée: {exc}", exc_info=True)

    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "message": "Erreur interne du serveur",
            "error": str(exc) if os.getenv("DEBUG") == "true" else "Une erreur inattendue s'est produite",
        },
    )


@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """Gestionnaire d'erreurs HTTP"""
    return JSONResponse(
        status_code=exc.status_code, content={"success": False, "message": exc.detail, "error": f"HTTP {exc.status_code}"}
    )


# Routes principales
@app.get("/", response_model=APIResponse)
async def root():
    """
    Point d'entrée de l'API
    """
    return APIResponse(
        success=True,
        message="API de Prospection B2B - Système opérationnel",
        data={
            "version": "1.0.0",
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "endpoints": {
                "documentation": "/docs",
                "health": "/health",
                "dashboard": "/dashboard/overview",
                "leads": "/leads/generate",
                "emails": "/emails/generate",
                "tracking": "/tracking/stats",
            },
            "features": [
                "Génération de leads automatique",
                "Analyse IA des entreprises",
                "Emails personnalisés",
                "Tracking complet",
                "Relances automatiques",
                "Dashboard en temps réel",
            ],
        },
    )


@app.get("/health", response_model=APIResponse)
async def health_check():
    """
    Vérification de l'état de santé de l'API
    """
    try:
        health = await service_instance.get_health_status()

        return APIResponse(success=True, message="Système opérationnel", data=health.model_dump())
    except Exception as e:
        return APIResponse(success=False, message="Problème détecté", error=str(e))


@app.get("/version", response_model=APIResponse)
async def get_version():
    """
    Récupère la version de l'API
    """
    return APIResponse(
        success=True,
        message="Version de l'API",
        data={
            "version": "1.0.0",
            "build_date": "2025-06-14",
            "python_version": "3.12+",
            "framework": "FastAPI",
            "database": "MySQL",
            "ai_model": "OpenAI GPT-4",
        },
    )


# Inclusion des routeurs
app.include_router(leads.router, prefix="/api/v1")
app.include_router(emails.router, prefix="/api/v1")
app.include_router(followups.router, prefix="/api/v1")
app.include_router(tracking.router, prefix="/api/v1")
app.include_router(dashboard.router, prefix="/api/v1")
app.include_router(campaigns.router, prefix="/api/v1")
app.include_router(statistics.router, prefix="/api/v1")
app.include_router(metrics.router, prefix="/api/v1")


# Configuration OpenAPI personnalisée
def custom_openapi():
    """Configuration OpenAPI personnalisée"""
    if app.openapi_schema:
        return app.openapi_schema

    openapi_schema = get_openapi(
        title="API de Prospection B2B",
        version="1.0.0",
        description=app.description,
        routes=app.routes,
    )

    # Ajouter des informations supplémentaires
    openapi_schema["info"]["x-logo"] = {"url": "https://www.slconception.fr/logo.png"}

    # Ajouter des tags personnalisés
    openapi_schema["tags"] = [
        {"name": "leads", "description": "Gestion des leads et génération d'entreprises"},
        {"name": "emails", "description": "Génération et envoi d'emails personnalisés"},
        {"name": "followups", "description": "Gestion des relances automatiques"},
        {"name": "tracking", "description": "Tracking des ouvertures, clics et réponses"},
        {"name": "dashboard", "description": "Statistiques et vue d'ensemble"},
    ]

    app.openapi_schema = openapi_schema
    return app.openapi_schema


app.openapi = custom_openapi


# Point d'entrée pour le développement
if __name__ == "__main__":
    import uvicorn

    # Configuration pour le développement
    uvicorn.run("api.main:app", host="0.0.0.0", port=8000, reload=True, log_level="info", access_log=True)
