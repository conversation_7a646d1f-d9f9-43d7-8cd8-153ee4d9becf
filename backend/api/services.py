"""
Services pour l'API REST
"""

import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

# Verrou global pour éviter les générations simultanées
_generation_lock = asyncio.Lock()
_generation_in_progress = False

from api.schemas import (
    AnalysisRequest,
    AnalysisResponse,
    APIResponse,
    CampaignStatsResponse,
    CompanyContextResponse,
    CompanyResponse,
    ConfigResponse,
    EmailGenerationRequest,
    EmailGenerationResponse,
    EmailReplyResponse,
    EmailSendRequest,
    EmailSendResponse,
    EmailTemplateResponse,
    FollowUpRequest,
    FollowUpResponse,
    FollowUpStatsResponse,
    HealthResponse,
    LeadGenerationRequest,
    LeadGenerationResponse,

    PainPointResponse,
    ReplyCheckResponse,
    SentEmailResponse,
    TrackingStatsResponse,
    WebhookEventRequest,
)
from collector import CompanyAnalyzer
from lead_generator import LeadManager
from mailer import EmailGenerator
from models import Company, CompanyContext, EmailTemplate, SentEmail
from sender import AntiSpamManager, EmailSender
from sender.database_manager_mysql import DatabaseManager
from sender.email_tracker import EmailTracker
from sender.followup_manager import FollowUpManager

logger = logging.getLogger(__name__)


class ProspectionAPIService:
    """Service principal pour l'API de prospection"""

    def __init__(self):
        self.lead_manager = None
        self.company_analyzer = None
        self.email_generator = None
        self.db_manager = None
        self.anti_spam_manager = None
        self.email_sender = None
        self.followup_manager = None
        self.email_tracker = None
        self.reply_checker = None
        self._initialized = False
        self._generation_in_progress = False  # Verrou pour éviter les générations simultanées

    async def initialize(self) -> None:
        """Initialise tous les services"""
        if self._initialized:
            return

        try:
            # Lead Manager
            self.lead_manager = LeadManager(enable_enrichment=True)

            # Company Analyzer
            self.company_analyzer = CompanyAnalyzer()

            # Email Generator
            import os

            openai_key = os.getenv("OPENAI_API_KEY")
            if openai_key:
                self.email_generator = EmailGenerator(api_key=openai_key, model="gpt-4o", temperature=0.7, max_tokens=1000)

            # Database Manager
            self.db_manager = DatabaseManager()
            await self.db_manager.initialize()

            # Anti-spam Manager
            self.anti_spam_manager = AntiSpamManager(self.db_manager)

            # Email Sender
            self.email_sender = EmailSender(
                db_manager=self.db_manager,
                anti_spam_manager=self.anti_spam_manager,
                smtp_host=os.getenv("SMTP_HOST"),
                smtp_port=int(os.getenv("SMTP_PORT", 587)),
                smtp_username=os.getenv("SMTP_USERNAME"),
                smtp_password=os.getenv("SMTP_PASSWORD"),
                sendgrid_api_key=os.getenv("SENDGRID_API_KEY"),
                from_email=os.getenv("SMTP_FROM_EMAIL", "<EMAIL>"),
                from_name=os.getenv("SMTP_FROM_NAME", "Sami Rochdi"),
                rate_limit=int(os.getenv("EMAIL_RATE_LIMIT", 5)),
            )

            # Follow-up Manager
            self.followup_manager = FollowUpManager()

            # Email Tracker
            self.email_tracker = EmailTracker()

            self._initialized = True
            logger.info("Services API initialisés")

        except Exception as e:
            logger.error(f"Erreur initialisation services API: {e}")
            raise

    async def generate_leads(self, request: LeadGenerationRequest) -> LeadGenerationResponse:
        """Génère des leads"""
        await self.initialize()

        # Utiliser le verrou global pour éviter les générations simultanées
        global _generation_in_progress

        if _generation_in_progress:
            raise ValueError("Une génération de leads est déjà en cours. Veuillez attendre qu'elle se termine.")

        try:
            async with _generation_lock:
                if _generation_in_progress:
                    raise ValueError("Une génération de leads est déjà en cours. Veuillez attendre qu'elle se termine.")

                _generation_in_progress = True
                logger.info("🚀 Début de génération de leads")

                start_time = time.time()
                leads = []
                saved_count = 0

                async for company in self.lead_manager.search_companies_flexible(
                    naf_codes=request.naf_codes,
                    regions=request.regions,
                    min_employees=None,
                    max_employees=None,
                    recent_only=request.recent_only,
                    days_back=request.days_back,
                    max_results=request.max_results,
                ):
                    # Créer la réponse pour l'API
                    lead_response = CompanyResponse(
                        siren=company.siren,
                        name=company.name,
                        city=company.city,
                        email=company.email,
                        website=str(company.website) if company.website else None,
                        naf_code=company.naf_code,
                        naf_label=company.naf_label,
                        employees=company.employees,
                        size=company.size,
                        source=company.source,
                        created_at=company.created_at,
                    )
                    leads.append(lead_response)

                    # Sauvegarder le lead dans la base de données
                    try:
                        await self._save_lead_to_database(company)
                        saved_count += 1
                    except Exception as e:
                        logger.warning(f"Erreur sauvegarde lead {company.name}: {e}")

                    if len(leads) >= request.max_results:
                        break

                generation_time = time.time() - start_time
                logger.info(f"✅ {len(leads)} leads générés, {saved_count} sauvegardés en base")

                return LeadGenerationResponse(
                    total_generated=len(leads),
                    leads=leads,
                    filters_applied={
                        "naf_codes": request.naf_codes,
                        "regions": request.regions,
                        "recent_only": request.recent_only,
                        "days_back": request.days_back,
                    },
                    generation_time=generation_time,
                )

        except Exception as e:
            logger.error(f"❌ Erreur génération leads: {e}")
            raise
        finally:
            _generation_in_progress = False
            logger.info("🏁 Fin de génération de leads")

    async def _save_lead_to_database(self, company):
        """Sauvegarde un lead dans la base de données"""
        try:
            import aiomysql

            connection = await aiomysql.connect(
                host="127.0.0.1", port=3306, user="root", password="MLKqsd002",
                db="prospection_b2b", charset="utf8mb4"
            )

            try:
                async with connection.cursor() as cursor:
                    # Vérifier si le lead existe déjà (éviter les doublons)
                    if company.siren:
                        await cursor.execute("SELECT id FROM leads WHERE siren = %s", (company.siren,))
                        existing = await cursor.fetchone()
                        if existing:
                            return  # Lead déjà existant

                    # Insérer le nouveau lead
                    await cursor.execute("""
                        INSERT INTO leads (
                            company_name, siret, siren, naf_code, naf_label,
                            address, city, postal_code, region, phone, email, website,
                            description, employee_count, source
                        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        company.name,
                        getattr(company, 'siret', None),
                        company.siren,
                        company.naf_code,
                        company.naf_label,
                        getattr(company, 'address', None),
                        company.city,
                        getattr(company, 'postal_code', None),
                        getattr(company, 'region', None),
                        getattr(company, 'phone', None),
                        company.email,
                        str(company.website) if company.website else None,
                        getattr(company, 'description', None),
                        company.employees,
                        company.source.value if hasattr(company.source, 'value') else str(company.source)
                    ))

                    await connection.commit()

            finally:
                connection.close()

        except Exception as e:
            logger.error(f"Erreur sauvegarde lead en base: {e}")
            raise

    async def get_leads(self, limit: int = 100, offset: int = 0) -> List[CompanyResponse]:
        """Récupère les leads depuis la base de données"""
        try:
            import aiomysql

            connection = await aiomysql.connect(
                host="127.0.0.1", port=3306, user="root", password="MLKqsd002",
                db="prospection_b2b", charset="utf8mb4"
            )

            try:
                async with connection.cursor() as cursor:
                    await cursor.execute("""
                        SELECT company_name, siret, siren, naf_code, naf_label,
                               city, email, website, employee_count, source, created_at
                        FROM leads
                        ORDER BY created_at DESC
                        LIMIT %s OFFSET %s
                    """, (limit, offset))

                    rows = await cursor.fetchall()
                    leads = []

                    for row in rows:
                        leads.append(CompanyResponse(
                            name=row[0],
                            siren=row[2],
                            city=row[5],
                            email=row[6],
                            website=row[7],
                            naf_code=row[3],
                            naf_label=row[4],
                            employees=row[8],
                            size=None,  # Pas stocké dans la table leads
                            source=row[9],
                            created_at=row[10] if row[10] else datetime.now(),
                        ))

                    return leads

            finally:
                connection.close()

        except Exception as e:
            logger.error(f"Erreur récupération leads: {e}")
            return []

    async def analyze_companies(self, request: AnalysisRequest, companies: List[Company]) -> AnalysisResponse:
        """Analyse les entreprises"""
        await self.initialize()

        start_time = time.time()

        contexts = await self.company_analyzer.batch_analyze_companies(companies, request.max_concurrent)

        analysis_time = time.time() - start_time

        # Convertir en réponses
        context_responses = []
        for context in contexts:
            pain_points = [
                PainPointResponse(category=pp.category, description=pp.description, severity=pp.severity, evidence=pp.evidence)
                for pp in context.pain_points
            ]

            context_responses.append(
                CompanyContextResponse(
                    company=CompanyResponse(
                        siren=context.company.siren,
                        name=context.company.name,
                        city=context.company.city,
                        email=context.company.email,
                        website=str(context.company.website) if context.company.website else None,
                        naf_code=context.company.naf_code,
                        naf_label=context.company.naf_label,
                        employees=context.company.employees,
                        size=context.company.size,
                        source=context.company.source,
                        created_at=context.company.created_at,
                    ),
                    pain_points=pain_points,
                    website_description=context.website_description,
                    lead_score=context.lead_score,
                    priority=context.priority,
                    analyzed_at=context.analyzed_at,
                )
            )

        # Calculer les statistiques
        total = len(contexts)
        with_email = len([c for c in contexts if c.company.email])
        high_priority = len([c for c in contexts if c.priority == "high"])
        avg_score = sum(c.lead_score or 0 for c in contexts) / total if total > 0 else 0

        statistics = {
            "total_companies": total,
            "with_email": with_email,
            "high_priority": high_priority,
            "average_score": round(avg_score, 1),
            "email_percentage": round(with_email / total * 100, 1) if total > 0 else 0,
        }

        return AnalysisResponse(
            total_analyzed=len(contexts), contexts=context_responses, analysis_time=analysis_time, statistics=statistics
        )

    async def get_company_contexts(self, context_ids: Optional[List[int]] = None) -> List[CompanyContext]:
        """Récupère les contextes d'entreprises depuis la base de données"""
        await self.initialize()

        try:
            # Si aucun ID spécifié, récupérer tous les contextes récents
            if not context_ids:
                # Récupérer les entreprises récemment générées
                company_responses = await self.get_leads(limit=50)
                contexts = []

                for company_response in company_responses:
                    # Convertir CompanyResponse en Company
                    from models import Company, LeadSource

                    website_url = None
                    if company_response.website:
                        try:
                            # Garder l'URL comme string, pas comme ParseResult
                            if company_response.website.startswith('http'):
                                website_url = company_response.website
                            else:
                                website_url = f'https://{company_response.website}'
                        except:
                            website_url = None

                    company = Company(
                        name=company_response.name,
                        siren=company_response.siren,
                        city=company_response.city,
                        email=company_response.email,
                        website=website_url,
                        naf_code=company_response.naf_code,
                        naf_label=company_response.naf_label,
                        employees=company_response.employees,
                        source=LeadSource(company_response.source) if company_response.source else LeadSource.SIRENE
                    )

                    # Créer un contexte basique pour chaque entreprise
                    context = CompanyContext(
                        company=company,
                        lead_score=50,  # Score par défaut
                        priority="medium",
                        pain_points=[],
                        tech_stack=None,
                        website_analysis=None,
                        contact_info=None
                    )
                    contexts.append(context)

                logger.info(f"Récupération de {len(contexts)} contextes d'entreprises")
                return contexts
            else:
                # TODO: Récupérer par IDs spécifiques
                return []

        except Exception as e:
            logger.error(f"Erreur récupération contextes: {e}")
            return []

    async def generate_emails(
        self, request: EmailGenerationRequest, contexts: List[CompanyContext]
    ) -> EmailGenerationResponse:
        """Génère les emails personnalisés"""
        await self.initialize()

        start_time = time.time()

        templates, filtered_contexts = await self.email_generator.generate_batch_emails(
            contexts, request.max_concurrent, skip_good_websites=request.skip_good_websites
        )

        generation_time = time.time() - start_time

        # Combiner les entreprises et templates
        emails = []
        for template, context in zip(templates, filtered_contexts):
            emails.append(
                {
                    "company": {
                        "name": context.company.name,
                        "email": context.company.email,
                        "city": context.company.city,
                        "siren": context.company.siren,
                    },
                    "email": {
                        "subject": template.subject,
                        "body": template.body,
                        "personalization_tokens": template.personalization_tokens,
                        "generated_at": template.generated_at.isoformat(),
                        "model_used": template.model_used,
                    },
                }
            )

        return EmailGenerationResponse(
            total_generated=len(templates),
            emails=emails,
            generation_time=generation_time,
            skipped_count=len(contexts) - len(filtered_contexts),
        )

    async def send_emails(self, request: EmailSendRequest, companies_and_templates: List[tuple]) -> EmailSendResponse:
        """Envoie les emails"""
        await self.initialize()

        start_time = time.time()

        sent_emails = await self.email_sender.send_batch_emails(
            companies_and_templates=companies_and_templates,
            use_sendgrid=request.use_sendgrid,
            dry_run=request.dry_run,
            max_concurrent=request.max_concurrent,
        )

        send_time = time.time() - start_time

        # Convertir en réponses
        email_responses = []
        for email in sent_emails:
            email_responses.append(
                SentEmailResponse(
                    id=email.id,
                    company_name=email.company_name,
                    email_to=email.email_to,
                    subject=email.subject,
                    status=email.status,
                    sent_at=email.sent_at,
                    tracking_id=email.tracking_id,
                    opened=email.opened,
                    clicked=email.clicked,
                    replied=email.replied,
                    error_message=email.error_message,
                )
            )

        # Calculer les statistiques
        from models import EmailStatus

        successful = len([e for e in sent_emails if e.status == EmailStatus.SENT])
        failed = len([e for e in sent_emails if e.status == EmailStatus.FAILED])
        duplicates = len([e for e in sent_emails if e.status == EmailStatus.DUPLICATE])

        return EmailSendResponse(
            total_sent=len(sent_emails),
            successful=successful,
            failed=failed,
            duplicates=duplicates,
            emails=email_responses,
            send_time=send_time,
        )

    async def get_tracking_stats(self, days_back: int = 30) -> TrackingStatsResponse:
        """Récupère les statistiques de tracking"""
        await self.initialize()

        import aiomysql

        connection = await aiomysql.connect(
            host="127.0.0.1", port=3306, user="root", password="MLKqsd002", db="prospection_b2b", charset="utf8mb4"
        )

        try:
            async with connection.cursor() as cursor:
                cutoff_date = datetime.now() - timedelta(days=days_back)

                # Total emails envoyés
                await cursor.execute(
                    """
                    SELECT COUNT(*) FROM sent_emails 
                    WHERE sent_at >= %s AND status = 'sent'
                """,
                    (cutoff_date,),
                )
                result = await cursor.fetchone()
                total_sent = result[0]

                # Emails ouverts
                await cursor.execute(
                    """
                    SELECT COUNT(*) FROM sent_emails 
                    WHERE sent_at >= %s AND opened = TRUE
                """,
                    (cutoff_date,),
                )
                result = await cursor.fetchone()
                opened = result[0]

                # Emails cliqués
                await cursor.execute(
                    """
                    SELECT COUNT(*) FROM sent_emails 
                    WHERE sent_at >= %s AND clicked = TRUE
                """,
                    (cutoff_date,),
                )
                result = await cursor.fetchone()
                clicked = result[0]

                # Événements de tracking
                await cursor.execute(
                    """
                    SELECT COUNT(*) FROM email_tracking_events 
                    WHERE created_at >= %s
                """,
                    (cutoff_date,),
                )
                result = await cursor.fetchone()
                tracking_events = result[0]

        finally:
            connection.close()

        open_rate = round((opened / total_sent * 100) if total_sent > 0 else 0, 2)
        click_rate = round((clicked / total_sent * 100) if total_sent > 0 else 0, 2)

        return TrackingStatsResponse(
            period_days=days_back,
            total_emails_sent=total_sent,
            emails_opened=opened,
            emails_clicked=clicked,
            open_rate=open_rate,
            click_rate=click_rate,
            tracking_events=tracking_events,
        )

    async def process_followups(self, request: FollowUpRequest) -> FollowUpResponse:
        """Traite les relances"""
        await self.initialize()

        start_time = time.time()

        stats = await self.followup_manager.process_followups(request.dry_run)

        processing_time = time.time() - start_time

        return FollowUpResponse(
            emails_checked=stats["emails_checked"],
            followups_sent=stats["followups_sent"],
            followups_failed=stats["followups_failed"],
            processing_time=processing_time,
            dry_run=stats["dry_run"],
        )

    async def get_followup_stats(self, days_back: int = 30) -> FollowUpStatsResponse:
        """Récupère les statistiques de relance"""
        await self.initialize()

        stats = await self.followup_manager.get_followup_statistics(days_back)

        return FollowUpStatsResponse(
            period_days=stats["period_days"],
            emails_needing_followup=stats["emails_needing_followup"],
            followups_sent=stats["followups_sent"],
            replies_after_followup=stats["replies_after_followup"],
            followup_response_rate=stats["followup_response_rate"],
        )

    async def check_replies(self, hours_back: int = 24) -> ReplyCheckResponse:
        """Vérifie les réponses aux emails"""
        await self.initialize()

        start_time = time.time()

        replies = await self.email_tracker.check_replies(hours_back)

        check_time = time.time() - start_time

        # Convertir en réponses
        reply_responses = []
        for reply in replies:
            reply_responses.append(
                EmailReplyResponse(
                    from_email=reply.from_email,
                    from_name=reply.from_name,
                    subject=reply.subject,
                    body=reply.body[:500],  # Limiter la taille
                    reply_type=reply.reply_type,
                    received_at=reply.received_at,
                    original_email_id=reply.original_email_id,
                )
            )

        # Compter par type
        positive = len([r for r in replies if r.reply_type == "positive"])
        negative = len([r for r in replies if r.reply_type == "negative"])
        neutral = len([r for r in replies if r.reply_type == "neutral"])
        auto_replies = len([r for r in replies if r.reply_type == "auto_reply"])

        return ReplyCheckResponse(
            total_replies=len(replies),
            positive_replies=positive,
            negative_replies=negative,
            neutral_replies=neutral,
            auto_replies=auto_replies,
            replies=reply_responses,
            check_time=check_time,
        )

    async def get_campaign_stats(self, days_back: int = 30) -> CampaignStatsResponse:
        """Récupère les statistiques de campagne"""
        await self.initialize()

        import aiomysql

        connection = await aiomysql.connect(
            host="127.0.0.1", port=3306, user="root", password="MLKqsd002", db="prospection_b2b", charset="utf8mb4"
        )

        try:
            async with connection.cursor() as cursor:
                cutoff_date = datetime.now() - timedelta(days=days_back)

                # Emails envoyés
                await cursor.execute(
                    """
                    SELECT COUNT(*) FROM sent_emails
                    WHERE sent_at >= %s AND status = 'sent'
                """,
                    (cutoff_date,),
                )
                result = await cursor.fetchone()
                emails_sent = result[0]

                # Emails ouverts
                await cursor.execute(
                    """
                    SELECT COUNT(*) FROM sent_emails
                    WHERE sent_at >= %s AND opened = TRUE
                """,
                    (cutoff_date,),
                )
                result = await cursor.fetchone()
                emails_opened = result[0]

                # Emails cliqués
                await cursor.execute(
                    """
                    SELECT COUNT(*) FROM sent_emails
                    WHERE sent_at >= %s AND clicked = TRUE
                """,
                    (cutoff_date,),
                )
                result = await cursor.fetchone()
                emails_clicked = result[0]

                # Emails avec réponse
                await cursor.execute(
                    """
                    SELECT COUNT(*) FROM sent_emails
                    WHERE sent_at >= %s AND replied = TRUE
                """,
                    (cutoff_date,),
                )
                result = await cursor.fetchone()
                emails_replied = result[0]

                # Relances envoyées
                await cursor.execute(
                    """
                    SELECT COUNT(*) FROM sent_emails
                    WHERE followup_sent_at >= %s
                """,
                    (cutoff_date,),
                )
                result = await cursor.fetchone()
                followups_sent = result[0]

        finally:
            connection.close()

        # Calculer les taux
        open_rate = round((emails_opened / emails_sent * 100) if emails_sent > 0 else 0, 2)
        click_rate = round((emails_clicked / emails_sent * 100) if emails_sent > 0 else 0, 2)
        reply_rate = round((emails_replied / emails_sent * 100) if emails_sent > 0 else 0, 2)

        # Funnel de conversion
        conversion_funnel = {
            "emails_sent": emails_sent,
            "emails_opened": emails_opened,
            "emails_clicked": emails_clicked,
            "emails_replied": emails_replied,
        }

        return CampaignStatsResponse(
            period_days=days_back,
            leads_generated=0,  # À implémenter si nécessaire
            emails_sent=emails_sent,
            emails_opened=emails_opened,
            emails_clicked=emails_clicked,
            emails_replied=emails_replied,
            followups_sent=followups_sent,
            open_rate=open_rate,
            click_rate=click_rate,
            reply_rate=reply_rate,
            conversion_funnel=conversion_funnel,
        )

    async def get_health_status(self) -> HealthResponse:
        """Vérifie l'état de santé du système"""
        start_time = time.time()

        # Vérifier la base de données
        try:
            await self.initialize()
            database_status = "healthy"
        except Exception:
            database_status = "error"

        # Vérifier le service email
        try:
            config = self.email_sender.validate_configuration()
            email_status = "healthy" if config["can_send"] else "warning"
        except Exception:
            email_status = "error"

        # Vérifier l'IA
        try:
            import os

            ai_status = "healthy" if os.getenv("OPENAI_API_KEY") else "warning"
        except Exception:
            ai_status = "error"

        # Statut global
        statuses = [database_status, email_status, ai_status]
        if "error" in statuses:
            overall_status = "error"
        elif "warning" in statuses:
            overall_status = "warning"
        else:
            overall_status = "healthy"

        uptime = time.time() - start_time

        return HealthResponse(
            status=overall_status,
            timestamp=datetime.now(),
            database=database_status,
            email_service=email_status,
            ai_service=ai_status,
            tracking_service="healthy",  # Simplifié
            uptime_seconds=uptime,
        )
