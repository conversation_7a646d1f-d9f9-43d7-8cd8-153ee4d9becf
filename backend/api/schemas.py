"""
Schémas Pydantic pour l'API REST
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, EmailStr, Field

from models import CompanySize, EmailStatus, LeadSource


# Schémas de base
class APIResponse(BaseModel):
    """Réponse API standard"""

    success: bool
    message: str
    data: Optional[Union[Dict[str, Any], List[Any]]] = None
    error: Optional[str] = None


class PaginationParams(BaseModel):
    """Paramètres de pagination"""

    page: int = Field(default=1, ge=1, description="Numéro de page")
    limit: int = Field(default=20, ge=1, le=100, description="Nombre d'éléments par page")


class DateRangeParams(BaseModel):
    """Paramètres de plage de dates"""

    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    days_back: Optional[int] = Field(default=30, ge=1, le=365)


# Sch<PERSON><PERSON> pour les leads
class LeadGenerationRequest(BaseModel):
    """Requête de génération de leads"""

    max_results: int = Field(default=100, ge=1, le=1000)
    naf_codes: Optional[List[str]] = None
    regions: Optional[List[str]] = None
    recent_only: bool = False
    days_back: int = Field(default=30, ge=1, le=365)


class CompanyResponse(BaseModel):
    """Réponse entreprise"""

    siren: Optional[str] = None
    name: str
    city: Optional[str] = None
    email: Optional[EmailStr] = None
    website: Optional[str] = None
    naf_code: Optional[str] = None
    naf_label: Optional[str] = None
    employees: Optional[int] = None
    size: Optional[CompanySize] = None
    source: LeadSource
    created_at: datetime


class LeadGenerationResponse(BaseModel):
    """Réponse génération de leads"""

    total_generated: int
    leads: List[CompanyResponse]
    filters_applied: Dict[str, Any]
    generation_time: float


# Schémas pour l'analyse
class AnalysisRequest(BaseModel):
    """Requête d'analyse d'entreprises"""

    company_ids: Optional[List[str]] = None
    max_concurrent: int = Field(default=5, ge=1, le=10)


class PainPointResponse(BaseModel):
    """Point de douleur détecté"""

    category: str
    description: str
    severity: int = Field(ge=1, le=5)
    evidence: Optional[str] = None


class CompanyContextResponse(BaseModel):
    """Contexte d'entreprise analysé"""

    company: CompanyResponse
    pain_points: List[PainPointResponse]
    website_description: Optional[str] = None
    lead_score: Optional[int] = Field(ge=0, le=100)
    priority: Optional[str] = None
    analyzed_at: datetime


class AnalysisResponse(BaseModel):
    """Réponse d'analyse"""

    total_analyzed: int
    contexts: List[CompanyContextResponse]
    analysis_time: float
    statistics: Dict[str, Any]


# Schémas pour les emails
class EmailGenerationRequest(BaseModel):
    """Requête de génération d'emails"""

    context_ids: Optional[List[int]] = None
    max_concurrent: int = Field(default=3, ge=1, le=5)
    skip_good_websites: bool = True


class EmailTemplateResponse(BaseModel):
    """Template d'email généré"""

    subject: str
    body: str
    personalization_tokens: Dict[str, str]
    generated_at: datetime
    model_used: str
    temperature: float


class EmailGenerationResponse(BaseModel):
    """Réponse génération d'emails"""

    total_generated: int
    emails: List[Dict[str, Any]]  # Company + Template
    generation_time: float
    skipped_count: int


# Schémas pour l'envoi
class EmailSendRequest(BaseModel):
    """Requête d'envoi d'emails"""

    email_ids: Optional[List[int]] = None
    use_sendgrid: bool = False
    dry_run: bool = True
    max_concurrent: int = Field(default=1, ge=1, le=3)


class SentEmailResponse(BaseModel):
    """Email envoyé"""

    id: Optional[int] = None
    company_name: str
    email_to: EmailStr
    subject: str
    status: EmailStatus
    sent_at: datetime
    tracking_id: Optional[str] = None
    opened: Optional[bool] = None
    clicked: Optional[bool] = None
    replied: Optional[bool] = None
    error_message: Optional[str] = None


class EmailSendResponse(BaseModel):
    """Réponse d'envoi d'emails"""

    total_sent: int
    successful: int
    failed: int
    duplicates: int
    emails: List[SentEmailResponse]
    send_time: float


# Schémas pour le tracking
class TrackingStatsResponse(BaseModel):
    """Statistiques de tracking"""

    period_days: int
    total_emails_sent: int
    emails_opened: int
    emails_clicked: int
    open_rate: float
    click_rate: float
    tracking_events: int


class EmailTrackingResponse(BaseModel):
    """Tracking d'un email spécifique"""

    email_id: int
    tracking_id: Optional[str] = None
    opened: bool
    clicked: bool
    first_open: Optional[datetime] = None
    first_click: Optional[datetime] = None
    total_opens: int
    total_clicks: int


# Schémas pour les relances
class FollowUpRequest(BaseModel):
    """Requête de traitement des relances"""

    days_threshold: int = Field(default=7, ge=1, le=30)
    dry_run: bool = True
    max_concurrent: int = Field(default=1, ge=1, le=3)


class FollowUpStatsResponse(BaseModel):
    """Statistiques de relance"""

    period_days: int
    emails_needing_followup: int
    followups_sent: int
    replies_after_followup: int
    followup_response_rate: float


class FollowUpResponse(BaseModel):
    """Réponse traitement des relances"""

    emails_checked: int
    followups_sent: int
    followups_failed: int
    processing_time: float
    dry_run: bool


# Schémas pour les réponses
class EmailReplyResponse(BaseModel):
    """Réponse d'email détectée"""

    from_email: str
    from_name: str
    subject: str
    body: str
    reply_type: str  # positive, negative, neutral, auto_reply
    received_at: datetime
    original_email_id: Optional[int] = None


class ReplyCheckResponse(BaseModel):
    """Réponse vérification des réponses"""

    total_replies: int
    positive_replies: int
    negative_replies: int
    neutral_replies: int
    auto_replies: int
    replies: List[EmailReplyResponse]
    check_time: float


# Schémas pour les statistiques
class CampaignStatsResponse(BaseModel):
    """Statistiques de campagne"""

    period_days: int
    leads_generated: int
    emails_sent: int
    emails_opened: int
    emails_clicked: int
    emails_replied: int
    followups_sent: int
    open_rate: float
    click_rate: float
    reply_rate: float
    conversion_funnel: Dict[str, int]


class PerformanceStatsResponse(BaseModel):
    """Statistiques de performance"""

    total_companies: int
    total_emails_sent: int
    total_responses: int
    avg_response_time_hours: float
    best_performing_subjects: List[Dict[str, Any]]
    response_by_day: Dict[str, int]
    geographic_distribution: Dict[str, int]


# Schémas pour la configuration
class ConfigResponse(BaseModel):
    """Configuration du système"""

    smtp_configured: bool
    sendgrid_configured: bool
    openai_configured: bool
    database_connected: bool
    tracking_enabled: bool
    rate_limit: int
    version: str


class HealthResponse(BaseModel):
    """État de santé du système"""

    status: str
    timestamp: datetime
    database: str
    email_service: str
    ai_service: str
    tracking_service: str
    uptime_seconds: float


# Schémas pour les campagnes
class CampaignCreateRequest(BaseModel):
    """Création d'une campagne"""

    name: str
    description: Optional[str] = None
    filters: Dict[str, Any] = Field(default_factory=dict)
    max_leads: int = Field(default=100, ge=1, le=1000)
    auto_send: bool = False


class CampaignResponse(BaseModel):
    """Réponse campagne"""

    id: str
    name: str
    description: Optional[str] = None
    status: str  # created, running, completed, failed
    leads_generated: int
    emails_sent: int
    emails_opened: int
    emails_replied: int
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None


# Schémas pour les webhooks
class WebhookEventRequest(BaseModel):
    """Événement webhook"""

    event_type: str  # email_opened, email_clicked, email_replied
    tracking_id: str
    email_id: int
    timestamp: datetime
    data: Dict[str, Any] = Field(default_factory=dict)


class WebhookResponse(BaseModel):
    """Réponse webhook"""

    received: bool
    processed: bool
    message: str
