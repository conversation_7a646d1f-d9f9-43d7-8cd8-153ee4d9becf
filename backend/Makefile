# Makefile pour le système de prospection B2B
.PHONY: help install test lint format clean run pipeline docker-build docker-run

# Variables
PYTHON = python3.12
VENV = venv
PIP = $(VENV)/bin/pip
PYTHON_VENV = $(VENV)/bin/python
PYTEST = $(VENV)/bin/pytest
BLACK = $(VENV)/bin/black
FLAKE8 = $(VENV)/bin/flake8

# Couleurs pour l'affichage
RED = \033[0;31m
GREEN = \033[0;32m
YELLOW = \033[1;33m
BLUE = \033[0;34m
NC = \033[0m # No Color

# Aide par défaut
help:
	@echo "$(BLUE)🚀 Système de Prospection B2B - Commandes disponibles$(NC)"
	@echo ""
	@echo "$(GREEN)Installation et configuration :$(NC)"
	@echo "  make install          - Installe l'environnement virtuel et les dépendances"
	@echo "  make setup            - Configuration initiale (copie .env.example)"
	@echo "  make clean            - Nettoie les fichiers temporaires"
	@echo ""
	@echo "$(GREEN)Développement :$(NC)"
	@echo "  make test             - Lance tous les tests"
	@echo "  make test-unit        - Lance uniquement les tests unitaires"
	@echo "  make test-integration - Lance les tests d'intégration"
	@echo "  make lint             - Vérifie la qualité du code (flake8)"
	@echo "  make format           - Formate le code (black)"
	@echo "  make check            - Lint + format + test"
	@echo ""
	@echo "$(GREEN)Exécution :$(NC)"
	@echo "  make run              - Lance le CLI interactif"
	@echo "  make pipeline         - Exécute le pipeline complet"
	@echo "  make leads            - Génère des leads (50 par défaut)"
	@echo "  make analyze          - Analyse les entreprises"
	@echo "  make emails           - Génère les emails"
	@echo "  make send-test        - Test d'envoi (dry run)"
	@echo "  make send-real        - Envoi réel des emails"
	@echo "  make stats            - Affiche les statistiques"
	@echo ""
	@echo "$(GREEN)Docker :$(NC)"
	@echo "  make docker-build     - Build l'image Docker"
	@echo "  make docker-run       - Lance le container"
	@echo "  make docker-pipeline  - Pipeline complet en Docker"
	@echo "  make docker-compose   - Lance avec docker-compose"
	@echo ""

# Installation
install: $(VENV)/bin/activate

$(VENV)/bin/activate: requirements.txt
	@echo "$(YELLOW)📦 Installation de l'environnement virtuel...$(NC)"
	$(PYTHON) -m venv $(VENV)
	$(PIP) install --upgrade pip
	$(PIP) install -r requirements.txt
	@echo "$(GREEN)✅ Installation terminée$(NC)"

# Configuration initiale
setup:
	@echo "$(YELLOW)⚙️  Configuration initiale...$(NC)"
	@if [ ! -f .env ]; then \
		cp .env.example .env; \
		echo "$(GREEN)✅ Fichier .env créé depuis .env.example$(NC)"; \
		echo "$(YELLOW)⚠️  N'oubliez pas de configurer vos clés API dans .env$(NC)"; \
	else \
		echo "$(BLUE)ℹ️  Fichier .env déjà existant$(NC)"; \
	fi
	@mkdir -p logs data examples
	@echo "$(GREEN)✅ Dossiers créés$(NC)"

# Tests
test: $(VENV)/bin/activate
	@echo "$(YELLOW)🧪 Lancement des tests...$(NC)"
	$(PYTEST) tests/ -v --cov=. --cov-report=term-missing --cov-report=html

test-unit: $(VENV)/bin/activate
	@echo "$(YELLOW)🧪 Tests unitaires...$(NC)"
	$(PYTEST) tests/ -v -m "not integration"

test-integration: $(VENV)/bin/activate
	@echo "$(YELLOW)🧪 Tests d'intégration...$(NC)"
	$(PYTEST) tests/ -v -m integration

# Qualité du code
lint: $(VENV)/bin/activate
	@echo "$(YELLOW)🔍 Vérification du code avec flake8...$(NC)"
	$(FLAKE8) . --count --select=E9,F63,F7,F82 --show-source --statistics
	$(FLAKE8) . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics

format: $(VENV)/bin/activate
	@echo "$(YELLOW)🎨 Formatage du code avec black...$(NC)"
	$(BLACK) . --line-length=127

check: lint format test
	@echo "$(GREEN)✅ Vérifications terminées$(NC)"

# Exécution
run: $(VENV)/bin/activate
	@echo "$(BLUE)🚀 Lancement du CLI...$(NC)"
	$(PYTHON_VENV) cli.py --help

pipeline: $(VENV)/bin/activate
	@echo "$(BLUE)🚀 Exécution du pipeline complet...$(NC)"
	$(PYTHON_VENV) cli.py pipeline

leads: $(VENV)/bin/activate
	@echo "$(BLUE)🎯 Génération de leads...$(NC)"
	$(PYTHON_VENV) cli.py generate-leads --max-results 50

analyze: $(VENV)/bin/activate
	@echo "$(BLUE)🔍 Analyse des entreprises...$(NC)"
	$(PYTHON_VENV) cli.py analyze

emails: $(VENV)/bin/activate
	@echo "$(BLUE)✉️  Génération des emails...$(NC)"
	$(PYTHON_VENV) cli.py generate-emails

send-test: $(VENV)/bin/activate
	@echo "$(BLUE)📤 Test d'envoi (dry run)...$(NC)"
	$(PYTHON_VENV) cli.py send --dry-run

send-real: $(VENV)/bin/activate
	@echo "$(RED)📤 ENVOI RÉEL DES EMAILS...$(NC)"
	@echo "$(YELLOW)⚠️  Êtes-vous sûr ? Appuyez sur Ctrl+C pour annuler$(NC)"
	@sleep 3
	$(PYTHON_VENV) cli.py send --real

stats: $(VENV)/bin/activate
	@echo "$(BLUE)📊 Statistiques...$(NC)"
	$(PYTHON_VENV) cli.py stats

# Docker
docker-build:
	@echo "$(YELLOW)🐳 Build de l'image Docker...$(NC)"
	docker build -t prospection-b2b .

docker-run: docker-build
	@echo "$(BLUE)🐳 Lancement du container...$(NC)"
	docker run --rm -it \
		-v $(PWD)/data:/app/data \
		-v $(PWD)/logs:/app/logs \
		-v $(PWD)/.env:/app/.env:ro \
		prospection-b2b --help

docker-pipeline: docker-build
	@echo "$(BLUE)🐳 Pipeline complet en Docker...$(NC)"
	docker run --rm \
		-v $(PWD)/data:/app/data \
		-v $(PWD)/logs:/app/logs \
		-v $(PWD)/.env:/app/.env:ro \
		prospection-b2b pipeline

docker-compose:
	@echo "$(BLUE)🐳 Lancement avec docker-compose...$(NC)"
	docker-compose up --build

# Nettoyage
clean:
	@echo "$(YELLOW)🧹 Nettoyage...$(NC)"
	rm -rf $(VENV)
	rm -rf __pycache__
	rm -rf .pytest_cache
	rm -rf htmlcov
	rm -rf .coverage
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	@echo "$(GREEN)✅ Nettoyage terminé$(NC)"

# Commandes de développement avancées
dev-install: install
	@echo "$(YELLOW)🛠️  Installation des outils de développement...$(NC)"
	$(PIP) install pre-commit
	$(VENV)/bin/pre-commit install
	@echo "$(GREEN)✅ Hooks pre-commit installés$(NC)"

coverage: test
	@echo "$(BLUE)📊 Ouverture du rapport de couverture...$(NC)"
	@if command -v xdg-open > /dev/null; then \
		xdg-open htmlcov/index.html; \
	elif command -v open > /dev/null; then \
		open htmlcov/index.html; \
	else \
		echo "$(YELLOW)Ouvrez manuellement htmlcov/index.html$(NC)"; \
	fi

# Commandes de production
deploy-check:
	@echo "$(YELLOW)🔍 Vérification avant déploiement...$(NC)"
	@if [ ! -f .env ]; then \
		echo "$(RED)❌ Fichier .env manquant$(NC)"; \
		exit 1; \
	fi
	@if ! grep -q "OPENAI_API_KEY=" .env || grep -q "your_openai_api_key_here" .env; then \
		echo "$(RED)❌ OPENAI_API_KEY non configurée$(NC)"; \
		exit 1; \
	fi
	@echo "$(GREEN)✅ Configuration OK pour le déploiement$(NC)"

backup:
	@echo "$(YELLOW)💾 Sauvegarde des données...$(NC)"
	@mkdir -p backups
	@tar -czf backups/prospection-backup-$(shell date +%Y%m%d-%H%M%S).tar.gz \
		data/ logs/ .env config.yaml
	@echo "$(GREEN)✅ Sauvegarde créée dans backups/$(NC)"

# Aide pour les variables d'environnement
env-help:
	@echo "$(BLUE)📋 Variables d'environnement requises (APIs GRATUITES) :$(NC)"
	@echo ""
	@echo "$(GREEN)Obligatoires :$(NC)"
	@echo "  OPENAI_API_KEY        - Clé API OpenAI (SEULE API PAYANTE)"
	@echo "  SMTP_HOST             - Serveur SMTP (ex: smtp.gmail.com)"
	@echo "  SMTP_USERNAME         - Nom d'utilisateur SMTP"
	@echo "  SMTP_PASSWORD         - Mot de passe SMTP"
	@echo ""
	@echo "$(YELLOW)Optionnelles :$(NC)"
	@echo "  SENDGRID_API_KEY      - Clé API SendGrid (alternative SMTP)"
	@echo ""
	@echo "$(GREEN)✅ APIs GRATUITES utilisées automatiquement :$(NC)"
	@echo "  • API Sirene v3       - Données entreprises françaises"
	@echo "  • API Entreprise      - Enrichissement gouvernemental"
	@echo "  • OpenDataSoft        - Base Sirene publique"
	@echo "  • Recherche emails    - Scraping + DNS (gratuit)"
	@echo ""
	@echo "$(BLUE)Voir FREE_APIS_GUIDE.md pour plus de détails$(NC)"

# Commandes spécifiques aux APIs gratuites
test-free-apis:
	@echo "$(YELLOW)🧪 Test des APIs gratuites...$(NC)"
	$(PYTHON_VENV) test_free_apis.py

setup-free:
	@echo "$(YELLOW)⚙️  Configuration pour APIs gratuites uniquement...$(NC)"
	@if [ ! -f config.yaml ]; then \
		cp config.free-apis.yaml config.yaml; \
		echo "$(GREEN)✅ Configuration APIs gratuites copiée$(NC)"; \
	else \
		echo "$(BLUE)ℹ️  config.yaml existe déjà$(NC)"; \
	fi
	@make setup

pipeline-free: setup-free test-free-apis
	@echo "$(BLUE)🚀 Pipeline complet avec APIs gratuites uniquement...$(NC)"
	$(PYTHON_VENV) cli.py pipeline

demo-free:
	@echo "$(BLUE)🎯 Démonstration APIs gratuites (10 leads)...$(NC)"
	$(PYTHON_VENV) cli.py generate-leads --max-results 10
	$(PYTHON_VENV) cli.py analyze --concurrent 2
	$(PYTHON_VENV) cli.py generate-emails --concurrent 1
	$(PYTHON_VENV) cli.py send --dry-run
