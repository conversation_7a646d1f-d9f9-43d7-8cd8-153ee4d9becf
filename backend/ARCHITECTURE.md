# 🏗️ Architecture du Système de Prospection B2B

## Vue d'ensemble

Le système de prospection B2B est conçu comme un pipeline modulaire avec 4 étapes principales :

```mermaid
graph TD
    A[Lead Generation] --> B[Company Analysis]
    B --> C[Email Generation]
    C --> D[Email Sending]
    
    A --> A1[API Sirene]
    A --> A2[Clearbit]
    A --> A3[Hunter.io]
    
    B --> B1[Web Scraping]
    B --> B2[Tech Analysis]
    B --> B3[Pain Detection]
    
    C --> C1[OpenAI GPT-4o]
    C --> C2[Template Manager]
    
    D --> D1[SMTP/SendGrid]
    D --> D2[Anti-spam]
    D --> D3[Database]
```

## 📦 Modules principaux

### 1. Lead Generator (`lead_generator/`)

**Responsabilité** : Génération et enrichissement de leads depuis diverses sources

#### Composants :
- **`sirene.py`** : Interface avec l'API Sirene v3
  - Recherche par codes NAF/APE
  - Filtrage géographique et par taille
  - Rate limiting (7 req/s)
  - Parsing des données INSEE

- **`clearbit.py`** : Enrichissement via Clearbit
  - Enrichissement des données entreprise
  - Recherche d'emails professionnels
  - Détection de technologies

- **`hunter.py`** : Recherche d'emails via Hunter.io
  - Recherche par domaine
  - Vérification d'emails
  - Scoring de confiance

- **`lead_manager.py`** : Orchestrateur principal
  - Déduplication intelligente
  - Cache en mémoire
  - Export/import JSON
  - Enrichissement multi-sources

#### Flux de données :
```
API Sirene → Company Objects → Enrichment (Clearbit/Hunter) → Deduplicated Leads
```

### 2. Collector (`collector/`)

**Responsabilité** : Analyse approfondie des entreprises et détection d'opportunités

#### Composants :
- **`web_scraper.py`** : Scraping et analyse web
  - Extraction de contenu
  - Métadonnées (title, description)
  - Détection de CMS/frameworks
  - Analyse de qualité technique

- **`tech_analyzer.py`** : Analyse de stack technique
  - Signatures de technologies (WordPress, React, etc.)
  - Catégorisation (CMS, frameworks, analytics)
  - Détection d'outils obsolètes

- **`pain_detector.py`** : Détection de points de douleur
  - Règles basées sur l'analyse web
  - Scoring de sévérité (1-5)
  - Catégorisation (website, technology, process)

- **`company_analyzer.py`** : Orchestrateur d'analyse
  - Coordination des analyses
  - Calcul de score de lead (0-100)
  - Priorisation automatique

#### Algorithme de scoring :
```python
score = 50  # Base
+ size_bonus  # Taille d'entreprise optimale
+ email_bonus  # Présence d'email
+ website_bonus  # Site accessible
+ pain_points_bonus  # Opportunités détectées
+ tech_stack_bonus  # Technologies obsolètes
+ sector_bonus  # Secteur ciblé
```

### 3. Mailer (`mailer/`)

**Responsabilité** : Génération d'emails personnalisés avec IA

#### Composants :
- **`email_generator.py`** : Générateur principal
  - Interface OpenAI GPT-4o
  - Prompts optimisés B2B
  - Personnalisation contextuelle
  - Validation de longueur

- **`template_manager.py`** : Gestion de templates
  - Templates adaptatifs par pain point
  - Sélection automatique
  - Variations d'objets

#### Prompt Engineering :
```
System: Tu es un copywriter B2B francophone expert...
Context: {company_info + tech_stack + pain_points}
Constraints: <2200 chars, vouvoiement, structure imposée
Output: Objet + Corps personnalisé
```

### 4. Sender (`sender/`)

**Responsabilité** : Envoi d'emails et gestion de l'historique

#### Composants :
- **`email_sender.py`** : Gestionnaire d'envoi
  - Support SMTP et SendGrid
  - Rate limiting configurable
  - Gestion d'erreurs robuste

- **`database_manager.py`** : Persistance SQLite
  - Historique complet des envois
  - Métadonnées de tracking
  - Statistiques d'engagement

- **`anti_spam.py`** : Protection anti-spam
  - Cache en mémoire + DB
  - Règles configurables (90j, 3 contacts max)
  - Déduplication par SIREN/domaine

#### Schéma de base de données :
```sql
sent_emails (
    id, company_siren, company_domain, email_to,
    subject, body, sent_at, status, error_message,
    opened, clicked, replied
)

companies_cache (
    siren, name, domain, last_contacted, contact_count
)
```

## 🔄 Flux de données complet

### 1. Pipeline principal
```
1. Lead Generation
   ├── API Sirene (filtres NAF/région)
   ├── Enrichissement Clearbit/Hunter
   └── Export leads.json

2. Company Analysis
   ├── Web scraping (description, tech)
   ├── Pain point detection
   ├── Lead scoring
   └── Export analyzed_companies.json

3. Email Generation
   ├── Context building
   ├── OpenAI generation
   ├── Template selection
   └── Export emails.json

4. Email Sending
   ├── Anti-spam filtering
   ├── SMTP/SendGrid delivery
   ├── Database logging
   └── Statistics tracking
```

### 2. Formats de données

#### Company Object
```python
{
    "siren": "*********",
    "name": "TECH SOLUTIONS SARL",
    "domain": "tech-solutions.fr",
    "email": "<EMAIL>",
    "city": "Lyon",
    "naf_code": "6201Z",
    "employees": 25,
    "size": "small",
    "source": "sirene"
}
```

#### CompanyContext Object
```python
{
    "company": Company,
    "tech_stack": {
        "cms": ["WordPress"],
        "frameworks": ["React"],
        "analytics": ["Google Analytics"]
    },
    "pain_points": [
        {
            "category": "website",
            "description": "Site non responsive",
            "severity": 4
        }
    ],
    "lead_score": 78,
    "priority": "high"
}
```

## 🔧 Configuration et extensibilité

### Configuration centralisée (`config.yaml`)
```yaml
search_filters:
  sirene:
    naf_codes: ["6201Z", "6202A"]
    min_employees: 5
    max_employees: 500

openai:
  model: "gpt-4o"
  temperature: 0.7
  max_tokens: 2200

rate_limits:
  sirene_api: 7
  email_sending: 5
```

### Points d'extension

1. **Nouvelles sources de leads** : Implémenter l'interface `LeadSource`
2. **Analyseurs personnalisés** : Hériter de `BaseAnalyzer`
3. **Templates d'emails** : Ajouter dans `TemplateManager`
4. **Providers d'envoi** : Étendre `EmailSender`

## 🚀 Performance et scalabilité

### Optimisations implémentées
- **Rate limiting** : Respect des limites API
- **Cache intelligent** : Évite les requêtes redondantes
- **Traitement asynchrone** : I/O non-bloquant
- **Batch processing** : Traitement par lots
- **Déduplication** : Évite les doublons

### Métriques de performance
- **Lead generation** : ~100 leads/minute (API Sirene)
- **Company analysis** : ~5 analyses/minute (scraping)
- **Email generation** : ~20 emails/minute (OpenAI)
- **Email sending** : Configurable (défaut: 5/minute)

### Limites actuelles
- **SQLite** : Limite à ~100k emails historiques
- **Scraping** : Dépendant de la qualité des sites
- **OpenAI** : Coût par email (~$0.01)
- **Rate limits** : Variables selon les APIs

## 🔒 Sécurité et conformité

### Mesures de sécurité
- **Variables d'environnement** : Clés API sécurisées
- **Rate limiting** : Protection contre les abus
- **Validation d'entrée** : Pydantic models
- **Logs structurés** : Traçabilité complète

### Conformité RGPD
⚠️ **Non implémentée** - Usage personnel uniquement
- Pas de consentement explicite
- Pas de droit à l'oubli
- Pas de portabilité des données

## 📊 Monitoring et observabilité

### Logs structurés
```python
{
    "timestamp": "2024-01-15T10:30:00Z",
    "level": "INFO",
    "module": "lead_generator",
    "event": "lead_generated",
    "company": "TECH SOLUTIONS",
    "source": "sirene",
    "lead_score": 78
}
```

### Métriques clés
- Leads générés par source
- Taux de conversion analyse → email
- Taux d'envoi et d'ouverture
- Erreurs par module
- Performance des APIs

### Alertes recommandées
- Taux d'erreur > 10%
- Latence API > 30s
- Quota API atteint
- Échec d'envoi > 5%

## 🔮 Évolutions futures

### Roadmap technique
1. **Migration PostgreSQL** : Meilleure scalabilité
2. **API REST** : Interface web
3. **Queue system** : Redis/Celery
4. **ML scoring** : Amélioration du lead scoring
5. **A/B testing** : Optimisation des templates

### Intégrations possibles
- **CRM** : Salesforce, HubSpot
- **Analytics** : Google Analytics, Mixpanel
- **Monitoring** : Grafana, Prometheus
- **Notifications** : Slack, Discord
