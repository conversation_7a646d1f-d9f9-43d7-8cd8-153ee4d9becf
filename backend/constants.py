"""
Constantes globales du projet de prospection B2B
"""

# URLs des APIs
SIRENE_API_BASE_URL = "https://api.insee.fr/entreprises/sirene/V3.11"
SIRENE_API_BASE_URL_V3 = "https://api.insee.fr/entreprises/sirene/V3"  # Fallback
BODACC_API_URL = "https://www.bodacc.fr/api/explore/v2.1/catalog/datasets/annonces-commerciales/records"
DATA_GOUV_SIRENE_URL = "https://files.data.gouv.fr/insee-sirene/StockEtablissement_utf8.csv"

# Codes NAF par défaut pour l'IT
DEFAULT_IT_NAF_CODES = [
    "6201Z",  # Programmation informatique
    "6202A",  # Conseil en systèmes et logiciels informatiques
    "6202B",  # Tierce maintenance de systèmes et d'applications informatiques
    "6203Z",  # Gestion d'installations informatiques
    "6209Z",  # Autres activités informatiques
    "5829C",  # Édition de logiciels applicatifs
    "6311Z",  # Traitement de données, hébergement et activités connexes
    "6312Z",  # Portails Internet
]

# Régions françaises (codes INSEE)
FRENCH_REGIONS = {
    "01": "Guadeloupe",
    "02": "Martinique",
    "03": "Guyane",
    "04": "La Réunion",
    "06": "Mayotte",
    "11": "Île-de-France",
    "24": "Centre-Val de Loire",
    "27": "Bourgogne-Franche-Comté",
    "28": "Normandie",
    "32": "Hauts-de-France",
    "44": "Grand Est",
    "52": "Pays de la Loire",
    "53": "Bretagne",
    "75": "Nouvelle-Aquitaine",
    "76": "Occitanie",
    "84": "Auvergne-Rhône-Alpes",
    "93": "Provence-Alpes-Côte d'Azur",
    "94": "Corse",
}

# Limites de rate limiting
SIRENE_RATE_LIMIT = 30  # requêtes par minute
BODACC_RATE_LIMIT = 10  # requêtes par minute
OPENAI_RATE_LIMIT = 60  # requêtes par minute
EMAIL_RATE_LIMIT = 10  # emails par minute (augmenté pour les tests)

# Timeouts
HTTP_TIMEOUT = 30.0  # secondes
SMTP_TIMEOUT = 60.0  # secondes (augmenté)

# Tailles de batch
DEFAULT_BATCH_SIZE = 10
MAX_CONCURRENT_REQUESTS = 5
MAX_CONCURRENT_EMAILS = 3

# Chemins de fichiers
CONFIG_DIR = "config"
LOGS_DIR = "logs"
DATA_DIR = "data"
EXPORTS_DIR = "exports"

# Extensions de fichiers supportées
SUPPORTED_EXPORT_FORMATS = [".json", ".csv", ".xlsx"]

# Messages d'erreur communs
ERROR_MESSAGES = {
    "api_timeout": "Timeout de l'API - veuillez réessayer",
    "api_rate_limit": "Limite de taux atteinte - veuillez patienter",
    "invalid_email": "Adresse email invalide",
    "smtp_error": "Erreur d'envoi SMTP",
    "config_missing": "Fichier de configuration manquant",
    "invalid_naf": "Code NAF invalide",
    "no_results": "Aucun résultat trouvé",
}

# Patterns regex
EMAIL_PATTERN = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
PHONE_PATTERN = r"^(?:\+33|0)[1-9](?:[0-9]{8})$"
SIREN_PATTERN = r"^\d{9}$"
SIRET_PATTERN = r"^\d{14}$"
NAF_PATTERN = r"^\d{4}[A-Z]$"

# Scores de lead
LEAD_SCORE_THRESHOLDS = {"excellent": 80, "good": 60, "average": 40, "poor": 20}

# Pain points par catégorie
PAIN_POINT_CATEGORIES = {
    "performance": "Performance technique",
    "seo": "Référencement naturel",
    "design": "Design et UX",
    "security": "Sécurité",
    "mobile": "Compatibilité mobile",
    "content": "Contenu et rédaction",
    "conversion": "Taux de conversion",
    "analytics": "Suivi et analytics",
}

# Technologies détectables
DETECTABLE_TECHNOLOGIES = [
    "WordPress",
    "Drupal",
    "Joomla",
    "Magento",
    "PrestaShop",
    "React",
    "Vue.js",
    "Angular",
    "jQuery",
    "PHP",
    "Python",
    "Node.js",
    "Java",
    ".NET",
    "MySQL",
    "PostgreSQL",
    "MongoDB",
    "Apache",
    "Nginx",
    "IIS",
    "Cloudflare",
    "AWS",
    "Google Cloud",
    "Azure",
]

# Templates d'emails par défaut
DEFAULT_EMAIL_TEMPLATES = {
    "subject_templates": [
        "Optimisez la performance de votre site web avec {company_name}",
        "Boostez votre visibilité en ligne - {company_name}",
        "Modernisez votre présence digitale - {company_name}",
        "Augmentez vos conversions web - {company_name}",
    ],
    "greeting_templates": ["Bonjour,", "Bonjour,"],
}

# Statuts de campagne
CAMPAIGN_STATUSES = [
    "draft",  # Brouillon
    "active",  # Active
    "paused",  # En pause
    "completed",  # Terminée
    "archived",  # Archivée
]

# Types de contact
CONTACT_TYPES = ["email", "phone", "linkedin", "website"]  # Email  # Téléphone  # LinkedIn  # Formulaire web
