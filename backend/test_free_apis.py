#!/usr/bin/env python3
"""
Script de test pour vérifier que toutes les APIs gratuites fonctionnent
"""
import asyncio
import logging
import sys
from datetime import datetime

from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.table import Table

from lead_generator.email_finder_free import EmailValidator, SmartEmailFinder
from lead_generator.free_apis import (
    APIEntrepriseGouv,
    FreeEnrichmentManager,
    OpenDataSoft,
)

# Imports des modules
from lead_generator.sirene_auth import SireneAuthAPI
from models import Company, LeadSource

# Configuration du logging
logging.basicConfig(level=logging.WARNING)
logger = logging.getLogger(__name__)

console = Console()


class FreeAPITester:
    """Testeur pour toutes les APIs gratuites"""

    def __init__(self):
        self.results = {}
        self.start_time = datetime.now()

    async def test_sirene_api(self) -> dict:
        """Test de l'API Sirene"""
        console.print("🔍 Test API Sirene...")

        try:
            sirene = SireneAuthAPI(rate_limit=5)

            # Test de recherche basique
            companies = []
            async for company in sirene.search_companies(naf_codes=["6201Z"], limit=3):
                companies.append(company)
                if len(companies) >= 3:
                    break

            return {
                "status": "✅ OK",
                "companies_found": len(companies),
                "sample_company": companies[0].name if companies else "Aucune",
                "error": None,
            }

        except Exception as e:
            return {"status": "❌ ERREUR", "companies_found": 0, "sample_company": "N/A", "error": str(e)}

    async def test_api_entreprise(self) -> dict:
        """Test de l'API Entreprise"""
        console.print("🏢 Test API Entreprise...")

        try:
            api_entreprise = APIEntrepriseGouv()

            # Test avec un SIREN connu (exemple)
            test_siren = "*********"  # SIREN de test
            company_info = await api_entreprise.get_company_info(test_siren)

            return {
                "status": "✅ OK" if company_info else "⚠️ LIMITE",
                "data_found": bool(company_info),
                "sample_data": str(company_info)[:100] + "..." if company_info else "Aucune",
                "error": None,
            }

        except Exception as e:
            return {"status": "❌ ERREUR", "data_found": False, "sample_data": "N/A", "error": str(e)}

    async def test_opendatasoft(self) -> dict:
        """Test d'OpenDataSoft"""
        console.print("📊 Test OpenDataSoft...")

        try:
            opendatasoft = OpenDataSoft()

            # Test de recherche
            companies = await opendatasoft.search_companies_by_activity(activity_codes=["6201Z"], limit=3)

            return {
                "status": "✅ OK",
                "companies_found": len(companies),
                "sample_company": (
                    companies[0].get("fields", {}).get("denominationunitelegale", "N/A") if companies else "Aucune"
                ),
                "error": None,
            }

        except Exception as e:
            return {"status": "❌ ERREUR", "companies_found": 0, "sample_company": "N/A", "error": str(e)}

    async def test_email_finder(self) -> dict:
        """Test du rechercheur d'emails"""
        console.print("📧 Test recherche d'emails...")

        try:
            email_finder = SmartEmailFinder()

            # Test avec un domaine connu
            test_company = Company(name="Test Company", domain="example.com", source=LeadSource.MANUAL)

            emails = await email_finder.find_emails_for_company(test_company)
            best_emails = email_finder.get_best_emails(emails, max_emails=3)

            return {
                "status": "✅ OK",
                "emails_found": len(emails),
                "best_emails": best_emails[:2],  # Premiers 2 emails
                "error": None,
            }

        except Exception as e:
            return {"status": "❌ ERREUR", "emails_found": 0, "best_emails": [], "error": str(e)}

    async def test_email_validator(self) -> dict:
        """Test du validateur d'emails"""
        console.print("✅ Test validation d'emails...")

        try:
            # Tests de validation
            test_emails = ["<EMAIL>", "invalid-email", "<EMAIL>", "<EMAIL>"]

            results = []
            for email in test_emails:
                is_valid = EmailValidator.is_valid_format(email)
                is_business = EmailValidator.is_business_email(email)
                confidence = EmailValidator.get_email_confidence(email, "business-domain.fr")

                results.append({"email": email, "valid": is_valid, "business": is_business, "confidence": confidence})

            return {
                "status": "✅ OK",
                "tests_passed": len([r for r in results if r["valid"]]),
                "total_tests": len(results),
                "sample_results": results[:2],
                "error": None,
            }

        except Exception as e:
            return {"status": "❌ ERREUR", "tests_passed": 0, "total_tests": 0, "sample_results": [], "error": str(e)}

    async def test_enrichment_manager(self) -> dict:
        """Test du gestionnaire d'enrichissement"""
        console.print("🔄 Test gestionnaire d'enrichissement...")

        try:
            enrichment = FreeEnrichmentManager()

            # Test avec une entreprise basique
            test_company = Company(name="Test Tech Company", siren="*********", source=LeadSource.SIRENE)

            enriched = await enrichment.enrich_company(test_company)

            return {
                "status": "✅ OK",
                "enrichment_applied": enriched.name != test_company.name or enriched.domain is not None,
                "domain_found": bool(enriched.domain),
                "email_found": bool(enriched.email),
                "error": None,
            }

        except Exception as e:
            return {
                "status": "❌ ERREUR",
                "enrichment_applied": False,
                "domain_found": False,
                "email_found": False,
                "error": str(e),
            }

    async def run_all_tests(self):
        """Lance tous les tests"""
        console.print(
            Panel.fit(
                "🧪 Test des APIs Gratuites\n" "Vérification que toutes les sources fonctionnent correctement",
                title="Test Suite",
                border_style="blue",
            )
        )

        with Progress(SpinnerColumn(), TextColumn("[progress.description]{task.description}"), console=console) as progress:

            # Tests individuels
            tests = [
                ("sirene", self.test_sirene_api),
                ("api_entreprise", self.test_api_entreprise),
                ("opendatasoft", self.test_opendatasoft),
                ("email_finder", self.test_email_finder),
                ("email_validator", self.test_email_validator),
                ("enrichment", self.test_enrichment_manager),
            ]

            for test_name, test_func in tests:
                task = progress.add_task(f"Test {test_name}...", total=None)

                try:
                    result = await test_func()
                    self.results[test_name] = result
                    progress.update(task, description=f"✅ {test_name}")
                except Exception as e:
                    self.results[test_name] = {"status": "❌ ERREUR CRITIQUE", "error": str(e)}
                    progress.update(task, description=f"❌ {test_name}")

                await asyncio.sleep(0.5)  # Délai entre tests

    def display_results(self):
        """Affiche les résultats des tests"""
        console.print("\n")

        # Tableau des résultats
        table = Table(title="Résultats des Tests APIs Gratuites")
        table.add_column("API", style="cyan")
        table.add_column("Statut", style="bold")
        table.add_column("Détails", style="green")
        table.add_column("Erreur", style="red")

        for test_name, result in self.results.items():
            status = result.get("status", "❓ INCONNU")

            # Détails spécifiques par test
            details = ""
            if test_name == "sirene":
                details = f"{result.get('companies_found', 0)} entreprises trouvées"
            elif test_name == "api_entreprise":
                details = f"Données: {'Oui' if result.get('data_found') else 'Non'}"
            elif test_name == "opendatasoft":
                details = f"{result.get('companies_found', 0)} entreprises trouvées"
            elif test_name == "email_finder":
                details = f"{result.get('emails_found', 0)} emails trouvés"
            elif test_name == "email_validator":
                details = f"{result.get('tests_passed', 0)}/{result.get('total_tests', 0)} tests passés"
            elif test_name == "enrichment":
                details = f"Enrichissement: {'Oui' if result.get('enrichment_applied') else 'Non'}"

            error = (
                result.get("error", "")[:50] + "..."
                if result.get("error") and len(result.get("error", "")) > 50
                else result.get("error", "")
            )

            table.add_row(test_name.replace("_", " ").title(), status, details, error or "Aucune")

        console.print(table)

        # Résumé
        total_tests = len(self.results)
        successful_tests = len([r for r in self.results.values() if "✅" in r.get("status", "")])

        console.print(f"\n📊 Résumé: {successful_tests}/{total_tests} tests réussis")

        if successful_tests == total_tests:
            console.print("🎉 [bold green]Tous les tests sont passés ! Le système est prêt.[/bold green]")
        elif successful_tests >= total_tests * 0.8:
            console.print(
                "⚠️ [bold yellow]La plupart des tests sont passés. Système utilisable avec limitations.[/bold yellow]"
            )
        else:
            console.print("❌ [bold red]Plusieurs tests ont échoué. Vérifiez la configuration.[/bold red]")

        # Temps d'exécution
        duration = (datetime.now() - self.start_time).total_seconds()
        console.print(f"⏱️ Temps d'exécution: {duration:.1f} secondes")

    def get_recommendations(self):
        """Affiche des recommandations basées sur les résultats"""
        console.print("\n💡 Recommandations:")

        recommendations = []

        # Vérifier chaque API
        if "❌" in self.results.get("sirene", {}).get("status", ""):
            recommendations.append("• Vérifiez votre connexion internet pour l'API Sirene")

        if "❌" in self.results.get("api_entreprise", {}).get("status", ""):
            recommendations.append("• L'API Entreprise peut avoir des limitations. Utilisez principalement Sirene")

        if "❌" in self.results.get("email_finder", {}).get("status", ""):
            recommendations.append("• Installez dnspython: pip install dnspython")

        # Recommandations générales
        if len([r for r in self.results.values() if "✅" in r.get("status", "")]) >= 4:
            recommendations.append("• Système prêt ! Lancez: python cli.py pipeline")
            recommendations.append("• Configurez vos variables d'environnement dans .env")
            recommendations.append("• Ajustez les rate limits dans config.yaml si nécessaire")

        for rec in recommendations:
            console.print(rec)


async def main():
    """Fonction principale"""
    tester = FreeAPITester()

    try:
        await tester.run_all_tests()
        tester.display_results()
        tester.get_recommendations()

    except KeyboardInterrupt:
        console.print("\n⚠️ Tests interrompus par l'utilisateur")
        sys.exit(1)
    except Exception as e:
        console.print(f"\n❌ Erreur critique: {e}")
        sys.exit(1)


if __name__ == "__main__":
    console.print("🚀 Démarrage des tests APIs gratuites...\n")
    asyncio.run(main())
