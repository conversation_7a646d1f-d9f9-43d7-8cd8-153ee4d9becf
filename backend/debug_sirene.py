#!/usr/bin/env python3
"""
Script de diagnostic pour l'API Sirene
"""

import asyncio
import base64
import json
import os
from datetime import datetime

import httpx
from dotenv import load_dotenv
from rich.console import Console
from rich.panel import Panel
from rich.table import Table

# Charger les variables d'environnement
load_dotenv()

console = Console()

class SireneDebugger:
    """Diagnostiqueur pour l'API Sirene"""
    
    def __init__(self):
        self.consumer_key = os.getenv("INSEE_CONSUMER_KEY")
        self.consumer_secret = os.getenv("INSEE_CONSUMER_SECRET")
        self.token_url = "https://api.insee.fr/token"
        self.base_url_v3 = "https://api.insee.fr/entreprises/sirene/V3"
        self.base_url_v311 = "https://api.insee.fr/entreprises/sirene/V3.11"
        
    async def test_authentication(self):
        """Test de l'authentification OAuth2"""
        console.print("🔐 Test de l'authentification INSEE...")
        
        if not self.consumer_key or not self.consumer_secret:
            console.print("❌ Clés INSEE manquantes dans .env")
            return None
            
        try:
            credentials = f"{self.consumer_key}:{self.consumer_secret}"
            encoded_credentials = base64.b64encode(credentials.encode()).decode()
            
            headers = {
                "Authorization": f"Basic {encoded_credentials}",
                "Content-Type": "application/x-www-form-urlencoded"
            }
            
            data = {"grant_type": "client_credentials"}
            
            async with httpx.AsyncClient() as client:
                response = await client.post(self.token_url, headers=headers, data=data, timeout=30.0)
                
                if response.status_code == 200:
                    token_data = response.json()
                    console.print("✅ Authentification réussie")
                    console.print(f"   Token type: {token_data.get('token_type')}")
                    console.print(f"   Expires in: {token_data.get('expires_in')} secondes")
                    return token_data.get("access_token")
                else:
                    console.print(f"❌ Erreur authentification: {response.status_code}")
                    console.print(f"   Réponse: {response.text}")
                    return None
                    
        except Exception as e:
            console.print(f"❌ Exception: {e}")
            return None
            
    async def test_api_v3_without_auth(self):
        """Test de l'API V3 sans authentification"""
        console.print("🔍 Test API Sirene V3 (sans auth)...")
        
        test_queries = [
            "activitePrincipaleEtablissement:6201Z",
            "denominationUniteLegale:GOOGLE",
            "siren:552032534"  # SIREN de test
        ]
        
        for query in test_queries:
            try:
                params = {"q": query, "nombre": 1}
                
                async with httpx.AsyncClient() as client:
                    response = await client.get(f"{self.base_url_v3}/siret", params=params, timeout=30.0)
                    
                    console.print(f"   Query: {query}")
                    console.print(f"   Status: {response.status_code}")
                    
                    if response.status_code == 200:
                        data = response.json()
                        console.print(f"   ✅ Résultats: {len(data.get('etablissements', []))}")
                        return True
                    else:
                        console.print(f"   ❌ Erreur: {response.text[:100]}")
                        
            except Exception as e:
                console.print(f"   ❌ Exception: {e}")
                
        return False
        
    async def test_api_v311_with_auth(self, token):
        """Test de l'API V3.11 avec authentification"""
        console.print("🔍 Test API Sirene V3.11 (avec auth)...")
        
        if not token:
            console.print("❌ Pas de token disponible")
            return False
            
        test_queries = [
            # SIREN spécifique (fonctionne)
            "siren:552032534",
            # Test requête générale (tous les établissements)
            "*:*",
            # Test dénomination (exemple de la doc)
            "denominationUniteLegale:GOOGLE",
            # Test code commune (exemple de la doc)
            "codeCommuneEtablissement:92046",
            # Test avec periode() - codes NAF différents
            "periode(activitePrincipaleEtablissement:62.01Z)",  # Avec point
            "periode(activitePrincipaleEtablissement:6202A)",   # Autre code
            "periode(activitePrincipaleEtablissement:47.11F)",  # Commerce
            # Test état administratif avec periode()
            "periode(etatAdministratifEtablissement:A)"
        ]
        
        headers = {"Authorization": f"Bearer {token}", "Accept": "application/json"}
        success_count = 0

        for query in test_queries:
            try:
                params = {"q": query, "nombre": 3}
                
                async with httpx.AsyncClient() as client:
                    response = await client.get(f"{self.base_url_v311}/siret", params=params, headers=headers, timeout=30.0)
                    
                    console.print(f"   Query: {query}")
                    console.print(f"   Status: {response.status_code}")
                    
                    if response.status_code == 200:
                        data = response.json()
                        etablissements = data.get('etablissements', [])
                        console.print(f"   ✅ Résultats: {len(etablissements)}")
                        
                        if etablissements:
                            # Afficher un exemple
                            etab = etablissements[0]
                            unite_legale = etab.get('uniteLegale', {})
                            console.print(f"   Exemple: {unite_legale.get('denominationUniteLegale', 'N/A')}")
                            success_count += 1
                    else:
                        console.print(f"   ❌ Erreur: {response.text[:200]}")
                        
            except Exception as e:
                console.print(f"   ❌ Exception: {e}")
                
        return success_count > 0
        
    async def test_alternative_sources(self):
        """Test des sources alternatives"""
        console.print("🔍 Test des sources alternatives...")
        
        # Test OpenDataSoft
        try:
            url = "https://public.opendatasoft.com/api/records/1.0/search/"
            params = {
                "dataset": "sirene_v3",
                "q": "activite_principale:6201Z",
                "rows": 3
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.get(url, params=params, timeout=30.0)
                
                if response.status_code == 200:
                    data = response.json()
                    records = data.get('records', [])
                    console.print(f"   ✅ OpenDataSoft: {len(records)} résultats")
                    
                    if records:
                        record = records[0]['fields']
                        console.print(f"   Exemple: {record.get('l1_normalisee', 'N/A')}")
                        return True
                else:
                    console.print(f"   ❌ OpenDataSoft erreur: {response.status_code}")
                    
        except Exception as e:
            console.print(f"   ❌ OpenDataSoft exception: {e}")
            
        return False
        
    async def generate_test_data(self):
        """Génère des données de test si les APIs ne fonctionnent pas"""
        console.print("🔧 Génération de données de test...")
        
        test_companies = [
            {
                "siren": "123456789",
                "siret": "12345678901234",
                "name": "TECH SOLUTIONS LYON",
                "legal_name": "TECH SOLUTIONS LYON SARL",
                "domain": "tech-solutions-lyon.fr",
                "email": "<EMAIL>",
                "city": "Lyon",
                "naf_code": "6201Z",
                "naf_label": "Programmation informatique",
                "employees": 15,
                "size": "small",
                "source": "generated"
            },
            {
                "siren": "234567890",
                "siret": "23456789012345",
                "name": "DIGITAL SERVICES PARIS",
                "legal_name": "DIGITAL SERVICES PARIS SAS",
                "domain": "digital-services-paris.com",
                "email": "<EMAIL>",
                "city": "Paris",
                "naf_code": "6202A",
                "naf_label": "Conseil en systèmes et logiciels informatiques",
                "employees": 25,
                "size": "small",
                "source": "generated"
            },
            {
                "siren": "345678901",
                "siret": "34567890123456",
                "name": "WEB AGENCY MARSEILLE",
                "legal_name": "WEB AGENCY MARSEILLE EURL",
                "domain": "web-agency-marseille.fr",
                "email": "<EMAIL>",
                "city": "Marseille",
                "naf_code": "6201Z",
                "naf_label": "Programmation informatique",
                "employees": 8,
                "size": "micro",
                "source": "generated"
            }
        ]
        
        # Sauvegarder dans leads_generated.json
        output_data = {
            "export_date": datetime.now().isoformat(),
            "total_companies": len(test_companies),
            "source": "generated_test_data",
            "companies": test_companies
        }
        
        with open("leads_generated.json", "w", encoding="utf-8") as f:
            json.dump(output_data, f, indent=2, ensure_ascii=False)
            
        console.print(f"✅ {len(test_companies)} entreprises de test générées dans leads_generated.json")
        return True
        
    async def run_full_diagnosis(self):
        """Lance le diagnostic complet"""
        console.print(Panel.fit("🔍 DIAGNOSTIC API SIRENE", style="bold blue"))
        
        results = {}
        
        # Test authentification
        token = await self.test_authentication()
        results["auth"] = token is not None
        
        # Test API V3 sans auth
        v3_works = await self.test_api_v3_without_auth()
        results["v3_no_auth"] = v3_works
        
        # Test API V3.11 avec auth
        v311_works = await self.test_api_v311_with_auth(token)
        results["v311_with_auth"] = v311_works
        
        # Test sources alternatives
        alt_works = await self.test_alternative_sources()
        results["alternatives"] = alt_works
        
        # Résumé
        console.print("\n" + "="*60)
        console.print(Panel.fit("📊 RÉSUMÉ DU DIAGNOSTIC", style="bold green"))
        
        table = Table(title="Résultats des Tests")
        table.add_column("Test", style="bold")
        table.add_column("Statut")
        table.add_column("Recommandation")
        
        if results["auth"]:
            table.add_row("Authentification INSEE", "✅ OK", "Utiliser API V3.11")
        else:
            table.add_row("Authentification INSEE", "❌ ÉCHEC", "Vérifier clés .env")
            
        if results["v3_no_auth"]:
            table.add_row("API V3 (sans auth)", "✅ OK", "Utiliser comme fallback")
        else:
            table.add_row("API V3 (sans auth)", "❌ FERMÉE", "Ne pas utiliser")
            
        if results["v311_with_auth"]:
            table.add_row("API V3.11 (avec auth)", "✅ OK", "Source principale")
        else:
            table.add_row("API V3.11 (avec auth)", "❌ ÉCHEC", "Problème config")
            
        if results["alternatives"]:
            table.add_row("Sources alternatives", "✅ OK", "Utiliser comme backup")
        else:
            table.add_row("Sources alternatives", "⚠️ LIMITÉES", "Données de test")
            
        console.print(table)
        
        # Recommandations
        console.print("\n💡 Recommandations:")
        
        if results["v311_with_auth"]:
            console.print("✅ Utiliser l'API Sirene V3.11 avec authentification")
        elif results["alternatives"]:
            console.print("⚠️ Utiliser les sources alternatives (OpenDataSoft)")
        else:
            console.print("🔧 Générer des données de test")
            await self.generate_test_data()
            
        return results

async def main():
    """Point d'entrée principal"""
    debugger = SireneDebugger()
    await debugger.run_full_diagnosis()

if __name__ == "__main__":
    asyncio.run(main())
