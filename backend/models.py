"""
Modèles de données pour le système de prospection B2B
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, EmailStr, Field, HttpUrl


class LeadSource(str, Enum):
    """Sources de génération de leads"""

    SIRENE = "sirene"
    DATA_GOUV = "data_gouv"  # Data.gouv.fr
    MANUAL = "manual"
    GENERATED = "generated_realistic"  # Données générées pour le développement
    FOLLOWUP = "followup"  # Entreprises reconstituées pour les relances


class CompanySize(str, Enum):
    """Tailles d'entreprise"""

    MICRO = "micro"  # 1-9 employés
    SMALL = "small"  # 10-49 employés
    MEDIUM = "medium"  # 50-249 employés
    LARGE = "large"  # 250+ employés


class EmailStatus(str, Enum):
    """Statuts d'envoi d'email"""

    PENDING = "pending"
    SENT = "sent"
    OPENED = "opened"
    CLICKED = "clicked"
    REPLIED = "replied"
    FAILED = "failed"
    BOUNCED = "bounced"
    DUPLICATE = "duplicate"
    SCHEDULED_FOLLOWUP = "scheduled_followup"
    FOLLOWUP_SENT = "followup_sent"


class Company(BaseModel):
    """Modèle représentant une entreprise prospect"""

    siren: Optional[str] = None
    siret: Optional[str] = None
    name: str
    legal_name: Optional[str] = None
    domain: Optional[str] = None
    website: Optional[HttpUrl] = None
    email: Optional[EmailStr] = None
    phone: Optional[str] = None

    # Adresse
    address: Optional[str] = None
    city: Optional[str] = None
    postal_code: Optional[str] = None
    region: Optional[str] = None
    country: str = "FR"

    # Informations business
    naf_code: Optional[str] = None
    naf_label: Optional[str] = None
    employees: Optional[int] = None
    size: Optional[CompanySize] = None
    revenue: Optional[float] = None

    # Métadonnées
    source: LeadSource
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class TechStack(BaseModel):
    """Stack technique détectée sur le site web"""

    cms: List[str] = Field(default_factory=list)
    frameworks: List[str] = Field(default_factory=list)
    analytics: List[str] = Field(default_factory=list)
    ecommerce: List[str] = Field(default_factory=list)
    hosting: List[str] = Field(default_factory=list)
    other: List[str] = Field(default_factory=list)


class PainPoint(BaseModel):
    """Point de douleur identifié"""

    category: str  # website, technology, process
    description: str
    severity: int = Field(ge=1, le=5)  # 1=faible, 5=critique
    evidence: Optional[str] = None


class CompanyContext(BaseModel):
    """Contexte complet d'une entreprise pour la génération d'email"""

    company: Company
    tech_stack: Optional[TechStack] = None
    pain_points: List[PainPoint] = Field(default_factory=list)
    website_description: Optional[str] = None
    linkedin_info: Optional[Dict[str, Any]] = None

    # Scores calculés
    lead_score: Optional[int] = Field(ge=0, le=100, default=None)
    priority: Optional[str] = None  # high, medium, low

    # Métadonnées
    analyzed_at: datetime = Field(default_factory=datetime.now)
    analysis_version: str = "1.0"


class EmailTemplate(BaseModel):
    """Template d'email généré"""

    subject: str
    body: str
    personalization_tokens: Dict[str, str] = Field(default_factory=dict)
    generated_at: datetime = Field(default_factory=datetime.now)
    model_used: str = "gpt-4o"
    temperature: float = 0.7


class SentEmail(BaseModel):
    """Email envoyé avec historique"""

    id: Optional[int] = None
    company_name: str
    company_siren: Optional[str] = None
    company_domain: Optional[str] = None
    email_to: EmailStr
    subject: str
    body: str

    # Métadonnées d'envoi
    sent_at: datetime = Field(default_factory=datetime.now)
    status: EmailStatus = EmailStatus.PENDING
    error_message: Optional[str] = None
    provider: str = "smtp"

    # Tracking
    tracking_id: Optional[str] = None
    opened: Optional[bool] = None
    opened_at: Optional[datetime] = None
    clicked: Optional[bool] = None
    clicked_at: Optional[datetime] = None
    replied: Optional[bool] = None
    replied_at: Optional[datetime] = None

    # Relance
    followup_scheduled: Optional[bool] = None
    followup_scheduled_at: Optional[datetime] = None
    followup_sent_at: Optional[datetime] = None

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class ProspectionCampaign(BaseModel):
    """Campagne de prospection"""

    id: Optional[str] = None
    name: str
    description: Optional[str] = None

    # Configuration
    filters: Dict[str, Any] = Field(default_factory=dict)
    email_template_id: Optional[str] = None

    # Statistiques
    leads_generated: int = 0
    emails_sent: int = 0
    emails_opened: int = 0
    emails_replied: int = 0

    # Dates
    created_at: datetime = Field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}
