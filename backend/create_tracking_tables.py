#!/usr/bin/env python3
"""
Script pour créer les tables de tracking dans MySQL
"""

import asyncio
import aiomysql

async def create_tracking_tables():
    """Crée les tables nécessaires pour le tracking des emails"""
    
    connection = await aiomysql.connect(
        host="127.0.0.1",
        port=3306,
        user="root",
        password="MLKqsd002",
        db="prospection_b2b",
        charset="utf8mb4"
    )
    
    try:
        async with connection.cursor() as cursor:
            
            # 1. Ajouter les colonnes de tracking à la table sent_emails si elles n'existent pas
            print("Ajout des colonnes de tracking à sent_emails...")
            
            # Vérifier si les colonnes existent déjà
            await cursor.execute("SHOW COLUMNS FROM sent_emails LIKE 'opened_at'")
            if not await cursor.fetchone():
                await cursor.execute("ALTER TABLE sent_emails ADD COLUMN opened_at DATETIME NULL")
                print("✓ <PERSON>onne opened_at ajoutée")
            
            await cursor.execute("SHOW COLUMNS FROM sent_emails LIKE 'clicked_at'")
            if not await cursor.fetchone():
                await cursor.execute("ALTER TABLE sent_emails ADD COLUMN clicked_at DATETIME NULL")
                print("✓ Colonne clicked_at ajoutée")
            
            await cursor.execute("SHOW COLUMNS FROM sent_emails LIKE 'replied_at'")
            if not await cursor.fetchone():
                await cursor.execute("ALTER TABLE sent_emails ADD COLUMN replied_at DATETIME NULL")
                print("✓ Colonne replied_at ajoutée")
            
            # 2. Créer la table des événements de tracking
            print("Création de la table email_tracking_events...")
            await cursor.execute("""
                CREATE TABLE IF NOT EXISTS email_tracking_events (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    tracking_id VARCHAR(255) NOT NULL,
                    event_type ENUM('open', 'click', 'reply') NOT NULL,
                    url TEXT NULL,
                    user_agent TEXT NULL,
                    ip_address VARCHAR(45) NULL,
                    created_at DATETIME NOT NULL,
                    INDEX idx_tracking_id (tracking_id),
                    INDEX idx_event_type (event_type),
                    INDEX idx_created_at (created_at)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """)
            print("✓ Table email_tracking_events créée")
            
            # 3. Créer la table des réponses
            print("Création de la table email_replies...")
            await cursor.execute("""
                CREATE TABLE IF NOT EXISTS email_replies (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    email_id INT NOT NULL,
                    tracking_id VARCHAR(255) NOT NULL,
                    from_email VARCHAR(255) NOT NULL,
                    subject TEXT NULL,
                    body TEXT NULL,
                    received_at DATETIME NOT NULL,
                    processed BOOLEAN DEFAULT FALSE,
                    INDEX idx_email_id (email_id),
                    INDEX idx_tracking_id (tracking_id),
                    INDEX idx_from_email (from_email),
                    INDEX idx_received_at (received_at),
                    FOREIGN KEY (email_id) REFERENCES sent_emails(id) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """)
            print("✓ Table email_replies créée")
            
            # 4. Créer la table des métriques agrégées (pour optimiser les requêtes)
            print("Création de la table email_metrics_daily...")
            await cursor.execute("""
                CREATE TABLE IF NOT EXISTS email_metrics_daily (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    date DATE NOT NULL,
                    emails_sent INT DEFAULT 0,
                    emails_opened INT DEFAULT 0,
                    emails_clicked INT DEFAULT 0,
                    emails_replied INT DEFAULT 0,
                    open_rate DECIMAL(5,2) DEFAULT 0.00,
                    click_rate DECIMAL(5,2) DEFAULT 0.00,
                    reply_rate DECIMAL(5,2) DEFAULT 0.00,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    UNIQUE KEY unique_date (date),
                    INDEX idx_date (date)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """)
            print("✓ Table email_metrics_daily créée")
            
            # 5. Créer la table des tokens OpenAI
            print("Création de la table openai_usage...")
            await cursor.execute("""
                CREATE TABLE IF NOT EXISTS openai_usage (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    date DATE NOT NULL,
                    model VARCHAR(50) NOT NULL DEFAULT 'gpt-4o',
                    tokens_used INT NOT NULL DEFAULT 0,
                    cost_usd DECIMAL(10,6) NOT NULL DEFAULT 0.000000,
                    emails_generated INT NOT NULL DEFAULT 0,
                    operation_type ENUM('email_generation', 'personalization', 'other') DEFAULT 'email_generation',
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_date (date),
                    INDEX idx_model (model),
                    INDEX idx_operation_type (operation_type)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """)
            print("✓ Table openai_usage créée")
            
            await connection.commit()
            print("\n🎉 Toutes les tables de tracking ont été créées avec succès !")
            
            # Afficher un résumé des tables
            print("\n📊 Tables de tracking disponibles :")
            print("- sent_emails (avec colonnes opened_at, clicked_at, replied_at)")
            print("- email_tracking_events (événements détaillés)")
            print("- email_replies (réponses reçues)")
            print("- email_metrics_daily (métriques agrégées par jour)")
            print("- openai_usage (utilisation des tokens OpenAI)")
            
    except Exception as e:
        print(f"❌ Erreur lors de la création des tables: {e}")
        await connection.rollback()
        raise
    finally:
        connection.close()

if __name__ == "__main__":
    asyncio.run(create_tracking_tables())
