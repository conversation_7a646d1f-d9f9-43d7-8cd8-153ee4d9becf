# Dockerfile pour le système de prospection B2B
FROM python:3.12-slim

# Métadonnées
LABEL maintainer="<PERSON> Rochdi <<EMAIL>>"
LABEL description="Système de prospection B2B automatisée"
LABEL version="1.0"

# Variables d'environnement
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONPATH=/app

# Créer un utilisateur non-root
RUN groupadd -r prospection && useradd -r -g prospection prospection

# Installer les dépendances système
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    gnupg \
    unzip \
    && rm -rf /var/lib/apt/lists/*

# Installer Chrome pour Selenium (optionnel)
RUN wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | apt-key add - \
    && echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google.list \
    && apt-get update \
    && apt-get install -y google-chrome-stable \
    && rm -rf /var/lib/apt/lists/*

# Créer le répertoire de travail
WORKDIR /app

# Copier les fichiers de requirements
COPY requirements.txt .

# Installer les dépendances Python
RUN pip install --no-cache-dir --upgrade pip \
    && pip install --no-cache-dir -r requirements.txt

# Copier le code source
COPY . .

# Créer les répertoires nécessaires
RUN mkdir -p logs data examples templates \
    && chown -R prospection:prospection /app

# Copier les fichiers de configuration
COPY config.yaml .
COPY .env.example .env

# Passer à l'utilisateur non-root
USER prospection

# Exposer le port (si on ajoute une API web plus tard)
EXPOSE 8000

# Point d'entrée par défaut
ENTRYPOINT ["python", "cli.py"]

# Commande par défaut
CMD ["--help"]

# Healthcheck
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import sys; sys.exit(0)" || exit 1

# Volumes pour la persistance
VOLUME ["/app/data", "/app/logs"]
