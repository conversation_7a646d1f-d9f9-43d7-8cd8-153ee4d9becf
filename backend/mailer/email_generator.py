"""
Générateur d'emails personnalisés avec OpenAI
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import List

from openai import AsyncOpenAI

from models import CompanyContext, EmailTemplate

logger = logging.getLogger(__name__)


class EmailGenerator:
    """Générateur d'emails personnalisés avec OpenAI"""

    def __init__(self, api_key: str, model: str = "gpt-4o", temperature: float = 0.7, max_tokens: int = 2200):
        """
        Args:
            api_key: Clé API OpenAI
            model: Modèle à utiliser
            temperature: Température pour la génération
            max_tokens: Nombre maximum de tokens
        """
        self.client = AsyncOpenAI(api_key=api_key)
        self.model = model
        self.temperature = temperature
        self.max_tokens = max_tokens

        # Configuration de l'entreprise depuis les variables d'environnement
        import os

        self.company_info = {
            "name": os.getenv("COMPANY_NAME", "SL Conception"),
            "contact_name": os.getenv("USER_FULL_NAME", "<PERSON> Rochdi"),
            "phone": os.getenv("COMPANY_PHONE", "06 23 31 58 39"),
            "email": os.getenv("COMPANY_EMAIL", "<EMAIL>"),
            "website": os.getenv("COMPANY_WEBSITE", "www.slconception.fr"),
            "signature": os.getenv(
                "USER_SIGNATURE", "Sami Rochdi – 06 23 31 58 39 – <EMAIL> – www.slconception.fr"
            ),
        }

    def _build_system_prompt(self) -> str:
        """Construit le prompt système de base"""
        return f"""Tu es Rochdi Sami, développeur web indépendant français spécialisé dans la création de solutions web sur-mesure.

MISSION: Rédiger un email de prospection professionnel PERSONNALISÉ avec le nom de l'entreprise et sa ville, adapté à sa situation.

STRUCTURE DE BASE À RESPECTER:
"[SALUTATION PERSONNALISÉE],

Je me permets de vous contacter pour vous proposer mes services en tant que développeur web indépendant.

[PARAGRAPHE PERSONNALISÉ AVEC NOM ENTREPRISE]

Chaque projet est entièrement adapté à votre activité, à vos objectifs et à vos contraintes.

[EXEMPLES ADAPTÉS À LA SITUATION]

Si vous avez un projet en tête ou une idée à creuser, je serais ravi d'en discuter avec vous lors d'un échange sans engagement.

Rochdi Sami
Développeur Web – Entreprise Rochdi Sami
📞 06 23 31 58 39
📧 <EMAIL>
🌐 www.slconception.fr

Au plaisir d'échanger prochainement,
Bien cordialement,
Rochdi Sami"

RÈGLES DE SALUTATION:
- Si le nom d'entreprise ressemble à un nom de personne (prénom + nom), utiliser "Madame [NOM]" ou "Monsieur [NOM]"
- Exemples: "Madame Fournier" pour "MARIE FOURNIER", "Monsieur Geoffroy" pour "JOEL GEOFFROY"
- Sinon, utiliser "Madame, Monsieur" pour les entreprises classiques

PERSONNALISATION OBLIGATOIRE SELON LA SITUATION:

1. SI L'ENTREPRISE N'A PAS DE SITE WEB:
Paragraphe: "J'accompagne les entreprises dans le développement de leur présence en ligne. Aujourd'hui, disposer d'un site web professionnel est devenu essentiel pour présenter ses services, rassurer les clients et développer son activité. Je vous propose de créer un site vitrine sur-mesure, moderne et adapté à l'image de votre entreprise."
Exemples: Site vitrine professionnel, Présentation de services, Formulaire de contact, Optimisation mobile

2. SI L'ENTREPRISE A UN SITE INACCESSIBLE/PROBLÉMATIQUE:
Paragraphe: "J'accompagne les entreprises dans l'amélioration de leur présence en ligne. J'ai constaté que votre site web semble rencontrer des difficultés d'accès, ce qui peut nuire à votre image et faire perdre des clients potentiels. Je vous propose de créer un nouveau site web fiable, moderne et performant."
Exemples: Refonte complète du site, Amélioration des performances, Design moderne, Sécurisation

3. SI L'ENTREPRISE A BESOIN DE MODERNISATION INTERNE:
Paragraphe: "J'accompagne les entreprises dans la modernisation de leurs processus internes. De nombreuses entreprises reposent encore sur des fichiers Excel complexes, des formulaires papier ou des outils devenus obsolètes. Je vous propose de convertir ces supports en applications web sur-mesure pour optimiser le fonctionnement de votre entreprise."
Exemples: Gestion de planning, Suivi de stock, Outils de pilotage, Espaces intranet

RÈGLES STRICTES DE PERSONNALISATION:
- Choisir la bonne salutation selon le type de nom d'entreprise
- Utiliser "votre site web" au lieu de "le site web de [NOM]"
- Utiliser "votre entreprise" au lieu de mentionner le nom dans le texte
- NE PAS mentionner de région géographique
- Choisir le bon paragraphe selon la situation détectée
- Adapter les exemples à la situation
- Garder le même ton professionnel et personnalisé
- Signature exacte obligatoire
- Vouvoiement obligatoire
- L'email doit sembler écrit spécifiquement pour cette entreprise"""

    def _build_company_context_prompt(self, context: CompanyContext) -> str:
        """Construit le prompt avec le contexte de l'entreprise"""
        company = context.company

        # Informations de base
        context_parts = [f"Entreprise: {company.name}"]

        if company.city:
            context_parts.append(f"Localisation: {company.city}")

        if company.naf_label:
            context_parts.append(f"Secteur: {company.naf_label}")
        elif company.naf_code:
            context_parts.append(f"Code NAF: {company.naf_code}")

        if company.employees:
            context_parts.append(f"Effectifs: {company.employees} employés")
        elif company.size:
            size_labels = {
                "micro": "Très petite entreprise (1-9 employés)",
                "small": "Petite entreprise (10-49 employés)",
                "medium": "Moyenne entreprise (50-249 employés)",
                "large": "Grande entreprise (250+ employés)",
            }
            context_parts.append(f"Taille: {size_labels.get(company.size.value, company.size.value)}")

        # Description du site web
        if context.website_description:
            context_parts.append(f"Activité (site web): {context.website_description[:300]}")

        # Stack technique
        if context.tech_stack:
            tech_info = []
            if context.tech_stack.cms:
                tech_info.append(f"CMS: {', '.join(context.tech_stack.cms)}")
            if context.tech_stack.ecommerce:
                tech_info.append(f"E-commerce: {', '.join(context.tech_stack.ecommerce)}")
            if tech_info:
                context_parts.append(f"Technologies: {' | '.join(tech_info)}")

        # Pain points (opportunités)
        if context.pain_points:
            # Prendre les 3 pain points les plus sévères
            top_pains = sorted(context.pain_points, key=lambda x: x.severity, reverse=True)[:3]
            pain_descriptions = []
            for pain in top_pains:
                pain_descriptions.append(f"{pain.description} (sévérité: {pain.severity}/5)")
            context_parts.append(f"Opportunités détectées: {' | '.join(pain_descriptions)}")

        return "\n".join(context_parts)

    def _should_skip_company(self, context: CompanyContext) -> tuple[bool, str]:
        """
        Détermine si on doit éviter d'envoyer un email à cette entreprise

        Args:
            context: Contexte de l'entreprise

        Returns:
            (should_skip, reason) - True si on doit éviter, avec la raison
        """
        # Si pas de site web, c'est une opportunité
        if not context.company.domain:
            return False, ""

        # Si le site n'est pas accessible, c'est une opportunité
        if not context.pain_points:
            return False, ""

        # Compter les pain points par sévérité
        critical_pains = [p for p in context.pain_points if p.severity >= 4]
        medium_pains = [p for p in context.pain_points if p.severity == 3]

        # Si pas de pain points critiques ou moyens, le site est probablement correct
        if not critical_pains and not medium_pains:
            return True, "Site web de qualité correcte, pas de pain points majeurs détectés"

        # Si seulement des pain points mineurs (sévérité 1-2)
        minor_pains = [p for p in context.pain_points if p.severity <= 2]
        if len(minor_pains) == len(context.pain_points) and len(minor_pains) <= 2:
            return True, "Seulement des améliorations mineures possibles"

        # Vérifier si c'est un site moderne avec peu de problèmes
        website_pains = [p for p in context.pain_points if p.category == "website"]
        if len(website_pains) <= 1 and not critical_pains:
            return True, "Site web moderne avec peu de problèmes techniques"

        # Si le lead score est très faible, pas intéressant
        if context.lead_score and context.lead_score < 30:
            return True, f"Score de lead trop faible ({context.lead_score})"

        return False, ""

    def _extract_subject_and_body(self, generated_text: str) -> tuple[str, str]:
        """Extrait l'objet et le corps de l'email généré"""
        lines = generated_text.strip().split("\n")

        # Chercher l'objet
        subject = ""
        body_start_idx = 0

        for i, line in enumerate(lines):
            line_clean = line.strip()
            if line_clean.startswith("Objet:") or line_clean.startswith("Subject:"):
                subject = line_clean.split(":", 1)[1].strip()
                body_start_idx = i + 1
                break
            elif line_clean.startswith("**Objet:**") or line_clean.startswith("**Subject:**"):
                subject = line_clean.replace("**Objet:**", "").replace("**Subject:**", "").strip()
                body_start_idx = i + 1
                break

        # Si pas d'objet trouvé, utiliser la première ligne
        if not subject and lines:
            subject = lines[0].strip()
            body_start_idx = 1

        # Extraire le corps
        body_lines = lines[body_start_idx:]
        body = "\n".join(body_lines).strip()

        # Nettoyer l'objet des guillemets ou autres caractères
        subject = subject.strip("\"'")

        return subject, body

    async def generate_email(self, context: CompanyContext) -> EmailTemplate:
        """
        Génère un email personnalisé pour une entreprise

        Args:
            context: Contexte complet de l'entreprise

        Returns:
            Template d'email généré
        """
        try:
            system_prompt = self._build_system_prompt()
            company_context = self._build_company_context_prompt(context)

            user_prompt = f"""Génère un email de prospection PERSONNALISÉ pour cette entreprise:

{company_context}

INSTRUCTIONS SPÉCIFIQUES DE PERSONNALISATION:
- Commence par "Objet: [objet personnalisé avec le nom de l'entreprise]"
- Analyse la situation de l'entreprise pour choisir le bon paragraphe:
  * Si domain=null ou vide → Pas de site web (paragraphe 1)
  * Si pain points détectés sur le site → Site problématique (paragraphe 2)
  * Sinon → Modernisation interne (paragraphe 3)
- SALUTATION: Si le nom ressemble à "PRÉNOM NOM", utilise "Madame [NOM]" ou "Monsieur [NOM]"
- FORMULATION: Utilise "votre site web" et "votre entreprise" (pas "le site de [NOM]")
- NE PAS mentionner de région géographique
- Adapte les exemples selon la situation choisie
- L'email doit sembler écrit spécifiquement pour cette entreprise
- Garder le ton professionnel et respectueux
- Utilise la signature exacte fournie"""

            logger.debug(f"Génération email pour {context.company.name}")

            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "system", "content": system_prompt}, {"role": "user", "content": user_prompt}],
                temperature=self.temperature,
                max_tokens=self.max_tokens,
            )

            generated_text = response.choices[0].message.content
            subject, body = self._extract_subject_and_body(generated_text)

            # Créer le template
            template = EmailTemplate(
                subject=subject,
                body=body,
                personalization_tokens={
                    "company_name": context.company.name,
                    "company_city": context.company.city or "",
                    "lead_score": str(context.lead_score or 0),
                    "priority": context.priority or "medium",
                },
                generated_at=datetime.now(),
                model_used=self.model,
                temperature=self.temperature,
            )

            logger.info(f"Email généré pour {context.company.name} - Longueur: {len(body)} caractères")
            return template

        except Exception as e:
            logger.error(f"Erreur génération email pour {context.company.name}: {e}")
            # Retourner un template d'erreur
            return EmailTemplate(
                subject=f"Opportunité de collaboration - {context.company.name}",
                body=f"Erreur lors de la génération de l'email personnalisé: {str(e)}",
                personalization_tokens={},
            )

    async def generate_batch_emails(
        self, contexts: List[CompanyContext], max_concurrent: int = 3, skip_good_websites: bool = True
    ) -> tuple[List[EmailTemplate], List[CompanyContext]]:
        """
        Génère des emails pour un lot d'entreprises

        Args:
            contexts: Liste des contextes d'entreprises
            max_concurrent: Nombre maximum de générations simultanées
            skip_good_websites: Si True, évite les entreprises avec de bons sites

        Returns:
            (templates, filtered_contexts) - Templates générés et contextes filtrés
        """
        # Filtrer les entreprises si demandé
        filtered_contexts = []
        skipped_companies = []

        for context in contexts:
            if skip_good_websites:
                should_skip, reason = self._should_skip_company(context)
                if should_skip:
                    skipped_companies.append((context.company.name, reason))
                    logger.info(f"Entreprise ignorée - {context.company.name}: {reason}")
                    continue

            filtered_contexts.append(context)

        if skipped_companies:
            logger.info(f"🚫 {len(skipped_companies)} entreprises ignorées (sites corrects)")
            for company_name, reason in skipped_companies:
                logger.debug(f"  - {company_name}: {reason}")

        if not filtered_contexts:
            logger.warning("Aucune entreprise à traiter après filtrage")
            return [], []

        semaphore = asyncio.Semaphore(max_concurrent)

        async def generate_with_semaphore(context: CompanyContext) -> EmailTemplate:
            async with semaphore:
                # Petit délai pour éviter de surcharger l'API
                await asyncio.sleep(0.5)
                return await self.generate_email(context)

        logger.info(f"📧 Génération de {len(filtered_contexts)} emails (max {max_concurrent} simultanés)")

        tasks = [generate_with_semaphore(context) for context in filtered_contexts]
        templates = await asyncio.gather(*tasks, return_exceptions=True)

        # Filtrer les erreurs
        valid_templates = []
        valid_contexts = []
        for i, result in enumerate(templates):
            if isinstance(result, Exception):
                logger.error(f"Erreur génération email {filtered_contexts[i].company.name}: {result}")
                # Créer un template d'erreur
                error_template = EmailTemplate(
                    subject=f"Opportunité de collaboration - {filtered_contexts[i].company.name}",
                    body=f"Erreur lors de la génération: {str(result)}",
                    personalization_tokens={},
                )
                valid_templates.append(error_template)
                valid_contexts.append(filtered_contexts[i])
            else:
                valid_templates.append(result)
                valid_contexts.append(filtered_contexts[i])

        logger.info(f"✅ Génération terminée: {len(valid_templates)} emails générés")
        return valid_templates, valid_contexts

    async def save_emails_to_json(
        self, templates: List[EmailTemplate], contexts: List[CompanyContext], filename: str = "emails.json"
    ) -> None:
        """
        Sauvegarde les emails générés dans un fichier JSON

        Args:
            templates: Templates d'emails
            contexts: Contextes correspondants
            filename: Nom du fichier de sortie
        """
        try:
            emails_data = []

            for template, context in zip(templates, contexts):
                email_data = {
                    "company": {
                        "name": context.company.name,
                        "siren": context.company.siren,
                        "domain": context.company.domain,
                        "email": context.company.email,
                        "city": context.company.city,
                    },
                    "email": {
                        "subject": template.subject,
                        "body": template.body,
                        "generated_at": template.generated_at.isoformat(),
                        "model_used": template.model_used,
                        "character_count": len(template.body),
                    },
                    "analysis": {
                        "lead_score": context.lead_score,
                        "priority": context.priority,
                        "pain_points_count": len(context.pain_points) if context.pain_points else 0,
                    },
                }
                emails_data.append(email_data)

            # Métadonnées d'export
            export_data = {
                "export_date": datetime.now().isoformat(),
                "total_emails": len(emails_data),
                "model_used": self.model,
                "temperature": self.temperature,
                "emails": emails_data,
            }

            with open(filename, "w", encoding="utf-8") as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)

            logger.info(f"Export de {len(emails_data)} emails vers {filename}")

        except Exception as e:
            logger.error(f"Erreur sauvegarde emails vers {filename}: {e}")
            raise
