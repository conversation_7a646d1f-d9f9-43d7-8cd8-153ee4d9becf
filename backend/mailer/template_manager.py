"""
Gestionnaire de templates d'emails
"""

import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

from models import EmailTemplate

logger = logging.getLogger(__name__)


class TemplateManager:
    """Gestionnaire pour les templates d'emails"""

    def __init__(self, templates_dir: str = "templates"):
        """
        Args:
            templates_dir: Répertoire des templates
        """
        self.templates_dir = Path(templates_dir)
        self.templates_dir.mkdir(exist_ok=True)

        # Templates prédéfinis
        self.default_templates = self._load_default_templates()

    def _load_default_templates(self) -> Dict[str, Dict[str, Any]]:
        """Charge les templates par défaut"""
        return {
            "refonte_site": {
                "name": "Refonte de site web",
                "subject_template": "Modernisation de votre site web - {company_name}",
                "pain_points": ["site_obsolete", "non_responsive", "design_obsolete"],
                "solution": "refonte site web moderne et responsive",
                "benefits": ["Amélioration de l'image de marque", "Meilleure expérience utilisateur", "Optimisation SEO"],
            },
            "application_metier": {
                "name": "Application métier sur mesure",
                "subject_template": "Digitalisation de vos processus - {company_name}",
                "pain_points": ["processus_manuels", "excel", "pas_de_crm"],
                "solution": "application métier personnalisée",
                "benefits": ["Automatisation des tâches répétitives", "Réduction des erreurs", "Gain de productivité"],
            },
            "ecommerce": {
                "name": "Solution e-commerce",
                "subject_template": "Développement de votre boutique en ligne - {company_name}",
                "pain_points": ["pas_de_vente_en_ligne", "site_vitrine"],
                "solution": "plateforme e-commerce complète",
                "benefits": ["Nouveau canal de vente", "Automatisation des commandes", "Suivi des stocks en temps réel"],
            },
            "intranet": {
                "name": "Intranet d'entreprise",
                "subject_template": "Amélioration de la communication interne - {company_name}",
                "pain_points": ["communication_interne", "partage_documents"],
                "solution": "intranet collaboratif",
                "benefits": [
                    "Centralisation des informations",
                    "Amélioration de la collaboration",
                    "Accès sécurisé aux documents",
                ],
            },
            "maintenance": {
                "name": "Maintenance et support",
                "subject_template": "Support technique pour votre infrastructure - {company_name}",
                "pain_points": ["cms_obsolete", "securite_faible", "pas_de_maintenance"],
                "solution": "service de maintenance et support technique",
                "benefits": ["Sécurité renforcée", "Disponibilité optimale", "Mises à jour régulières"],
            },
        }

    def select_best_template(self, pain_points: List[str], company_context: Dict[str, Any]) -> str:
        """
        Sélectionne le meilleur template basé sur les pain points

        Args:
            pain_points: Liste des pain points détectés
            company_context: Contexte de l'entreprise

        Returns:
            Nom du template le plus approprié
        """
        if not pain_points:
            return "refonte_site"  # Template par défaut

        # Calculer les scores pour chaque template
        template_scores = {}

        for template_name, template_config in self.default_templates.items():
            score = 0
            template_pain_points = template_config.get("pain_points", [])

            # Score basé sur la correspondance des pain points
            for pain_point in pain_points:
                if any(tpp in pain_point.lower() for tpp in template_pain_points):
                    score += 10

            # Bonus spécifiques selon le contexte
            if template_name == "ecommerce":
                # Bonus si l'entreprise n'a pas de solution e-commerce
                if "ecommerce" not in company_context.get("tech_stack", {}).get("ecommerce", []):
                    score += 5

            elif template_name == "application_metier":
                # Bonus pour les entreprises de taille moyenne
                company_size = company_context.get("size")
                if company_size in ["small", "medium"]:
                    score += 8

            elif template_name == "maintenance":
                # Bonus si technologies obsolètes détectées
                tech_stack = company_context.get("tech_stack", {})
                if any("obsolete" in str(tech).lower() for tech in tech_stack.values()):
                    score += 7

            template_scores[template_name] = score

        # Retourner le template avec le meilleur score
        best_template = max(template_scores.items(), key=lambda x: x[1])

        logger.debug(f"Template sélectionné: {best_template[0]} (score: {best_template[1]})")
        return best_template[0]

    def generate_subject_variations(self, base_subject: str, company_name: str) -> List[str]:
        """
        Génère des variations d'objets d'email

        Args:
            base_subject: Objet de base
            company_name: Nom de l'entreprise

        Returns:
            Liste de variations d'objets
        """
        variations = [
            base_subject.format(company_name=company_name),
            f"Opportunité de collaboration - {company_name}",
            f"Proposition personnalisée pour {company_name}",
            f"Amélioration de votre présence digitale - {company_name}",
            f"Solutions sur mesure pour {company_name}",
            f"Développement web pour {company_name}",
            f"Modernisation de vos outils - {company_name}",
        ]

        # Supprimer les doublons tout en préservant l'ordre
        seen = set()
        unique_variations = []
        for variation in variations:
            if variation not in seen:
                seen.add(variation)
                unique_variations.append(variation)

        return unique_variations[:5]  # Limiter à 5 variations

    def save_template(self, template_name: str, template_data: Dict[str, Any]) -> None:
        """
        Sauvegarde un template personnalisé

        Args:
            template_name: Nom du template
            template_data: Données du template
        """
        try:
            template_file = self.templates_dir / f"{template_name}.json"

            template_data["created_at"] = datetime.now().isoformat()
            template_data["version"] = "1.0"

            with open(template_file, "w", encoding="utf-8") as f:
                json.dump(template_data, f, indent=2, ensure_ascii=False)

            logger.info(f"Template '{template_name}' sauvegardé")

        except Exception as e:
            logger.error(f"Erreur sauvegarde template '{template_name}': {e}")
            raise

    def load_template(self, template_name: str) -> Optional[Dict[str, Any]]:
        """
        Charge un template personnalisé

        Args:
            template_name: Nom du template

        Returns:
            Données du template ou None si non trouvé
        """
        try:
            template_file = self.templates_dir / f"{template_name}.json"

            if not template_file.exists():
                return None

            with open(template_file, "r", encoding="utf-8") as f:
                return json.load(f)

        except Exception as e:
            logger.error(f"Erreur chargement template '{template_name}': {e}")
            return None

    def list_templates(self) -> List[str]:
        """
        Liste tous les templates disponibles

        Returns:
            Liste des noms de templates
        """
        templates = list(self.default_templates.keys())

        # Ajouter les templates personnalisés
        for template_file in self.templates_dir.glob("*.json"):
            template_name = template_file.stem
            if template_name not in templates:
                templates.append(template_name)

        return sorted(templates)

    def get_template_info(self, template_name: str) -> Optional[Dict[str, Any]]:
        """
        Récupère les informations d'un template

        Args:
            template_name: Nom du template

        Returns:
            Informations du template
        """
        # Vérifier d'abord les templates par défaut
        if template_name in self.default_templates:
            return self.default_templates[template_name]

        # Puis les templates personnalisés
        return self.load_template(template_name)

    def validate_template(self, template_data: Dict[str, Any]) -> List[str]:
        """
        Valide la structure d'un template

        Args:
            template_data: Données du template à valider

        Returns:
            Liste des erreurs de validation (vide si valide)
        """
        errors = []

        required_fields = ["name", "subject_template", "solution", "benefits"]
        for field in required_fields:
            if field not in template_data:
                errors.append(f"Champ requis manquant: {field}")

        # Vérifier les types
        if "benefits" in template_data and not isinstance(template_data["benefits"], list):
            errors.append("Le champ 'benefits' doit être une liste")

        if "pain_points" in template_data and not isinstance(template_data["pain_points"], list):
            errors.append("Le champ 'pain_points' doit être une liste")

        return errors
