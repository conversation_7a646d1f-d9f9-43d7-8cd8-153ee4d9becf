"""
Tests pour les modèles de données
"""

from datetime import datetime

import pytest

from models import (
    Company,
    CompanyContext,
    CompanySize,
    EmailStatus,
    EmailTemplate,
    LeadSource,
    PainPoint,
    SentEmail,
    TechStack,
)


class TestCompany:
    """Tests pour le modèle Company"""

    def test_company_creation(self):
        """Test création d'une entreprise basique"""
        company = Company(name="Test Company", source=LeadSource.SIRENE)

        assert company.name == "Test Company"
        assert company.source == LeadSource.SIRENE
        assert company.country == "FR"  # Valeur par défaut
        assert isinstance(company.created_at, datetime)

    def test_company_with_all_fields(self):
        """Test création d'une entreprise avec tous les champs"""
        company = Company(
            siren="*********",
            siret="*********01234",
            name="Test Company SARL",
            legal_name="Test Company SARL",
            domain="test-company.fr",
            website="https://test-company.fr",
            email="<EMAIL>",
            phone="01 23 45 67 89",
            address="123 Rue de Test",
            city="Paris",
            postal_code="75001",
            region="Île-de-France",
            naf_code="6201Z",
            naf_label="Programmation informatique",
            employees=25,
            size=CompanySize.SMALL,
            revenue=500000.0,
            source=LeadSource.SIRENE,
        )

        assert company.siren == "*********"
        assert company.employees == 25
        assert company.size == CompanySize.SMALL
        assert company.revenue == 500000.0

    def test_company_size_enum(self):
        """Test de l'enum CompanySize"""
        assert CompanySize.MICRO.value == "micro"
        assert CompanySize.SMALL.value == "small"
        assert CompanySize.MEDIUM.value == "medium"
        assert CompanySize.LARGE.value == "large"


class TestTechStack:
    """Tests pour le modèle TechStack"""

    def test_empty_tech_stack(self):
        """Test création d'une stack technique vide"""
        tech_stack = TechStack()

        assert tech_stack.cms == []
        assert tech_stack.frameworks == []
        assert tech_stack.analytics == []
        assert tech_stack.ecommerce == []
        assert tech_stack.hosting == []
        assert tech_stack.other == []

    def test_tech_stack_with_data(self):
        """Test création d'une stack technique avec données"""
        tech_stack = TechStack(
            cms=["WordPress"],
            frameworks=["React", "jQuery"],
            analytics=["Google Analytics"],
            ecommerce=["WooCommerce"],
            hosting=["Cloudflare"],
            other=["Bootstrap"],
        )

        assert "WordPress" in tech_stack.cms
        assert "React" in tech_stack.frameworks
        assert len(tech_stack.frameworks) == 2


class TestPainPoint:
    """Tests pour le modèle PainPoint"""

    def test_pain_point_creation(self):
        """Test création d'un pain point"""
        pain_point = PainPoint(
            category="website", description="Site web obsolète", severity=4, evidence="Design des années 2000"
        )

        assert pain_point.category == "website"
        assert pain_point.severity == 4
        assert pain_point.evidence == "Design des années 2000"

    def test_pain_point_severity_validation(self):
        """Test validation de la sévérité"""
        # Sévérité valide
        pain_point = PainPoint(category="technology", description="CMS obsolète", severity=3)
        assert pain_point.severity == 3

        # Test des limites (devrait être géré par Pydantic)
        with pytest.raises(ValueError):
            PainPoint(category="technology", description="Test", severity=6)  # > 5

        with pytest.raises(ValueError):
            PainPoint(category="technology", description="Test", severity=0)  # < 1


class TestCompanyContext:
    """Tests pour le modèle CompanyContext"""

    def test_company_context_creation(self):
        """Test création d'un contexte d'entreprise"""
        company = Company(name="Test Company", source=LeadSource.SIRENE)

        context = CompanyContext(company=company, lead_score=75, priority="high")

        assert context.company.name == "Test Company"
        assert context.lead_score == 75
        assert context.priority == "high"
        assert isinstance(context.analyzed_at, datetime)

    def test_company_context_with_tech_and_pains(self):
        """Test contexte avec stack technique et pain points"""
        company = Company(name="Test Company", source=LeadSource.SIRENE)
        tech_stack = TechStack(cms=["WordPress"], analytics=["Google Analytics"])
        pain_points = [
            PainPoint(category="website", description="Site lent", severity=3),
            PainPoint(category="technology", description="Pas de CRM", severity=4),
        ]

        context = CompanyContext(
            company=company,
            tech_stack=tech_stack,
            pain_points=pain_points,
            website_description="Site web de l'entreprise",
            lead_score=80,
            priority="high",
        )

        assert len(context.pain_points) == 2
        assert context.tech_stack.cms == ["WordPress"]
        assert context.website_description == "Site web de l'entreprise"


class TestEmailTemplate:
    """Tests pour le modèle EmailTemplate"""

    def test_email_template_creation(self):
        """Test création d'un template d'email"""
        template = EmailTemplate(
            subject="Opportunité de collaboration",
            body="Bonjour,\n\nNous proposons...",
            personalization_tokens={"company_name": "Test Company"},
        )

        assert template.subject == "Opportunité de collaboration"
        assert "Bonjour" in template.body
        assert template.personalization_tokens["company_name"] == "Test Company"
        assert template.model_used == "gpt-4o"  # Valeur par défaut
        assert template.temperature == 0.7  # Valeur par défaut
        assert isinstance(template.generated_at, datetime)


class TestSentEmail:
    """Tests pour le modèle SentEmail"""

    def test_sent_email_creation(self):
        """Test création d'un email envoyé"""
        sent_email = SentEmail(
            company_siren="*********",
            company_domain="test-company.fr",
            email_to="<EMAIL>",
            subject="Test Subject",
            body="Test Body",
            status=EmailStatus.SENT,
        )

        assert sent_email.company_siren == "*********"
        assert sent_email.email_to == "<EMAIL>"
        assert sent_email.status == EmailStatus.SENT
        assert isinstance(sent_email.sent_at, datetime)

    def test_email_status_enum(self):
        """Test de l'enum EmailStatus"""
        assert EmailStatus.PENDING.value == "pending"
        assert EmailStatus.SENT.value == "sent"
        assert EmailStatus.FAILED.value == "failed"
        assert EmailStatus.BOUNCED.value == "bounced"
        assert EmailStatus.DUPLICATE.value == "duplicate"

    def test_sent_email_with_tracking(self):
        """Test email envoyé avec tracking"""
        sent_email = SentEmail(
            email_to="<EMAIL>",
            subject="Test",
            body="Test body",
            status=EmailStatus.SENT,
            opened=True,
            clicked=False,
            replied=False,
        )

        assert sent_email.opened is True
        assert sent_email.clicked is False
        assert sent_email.replied is False


class TestLeadSource:
    """Tests pour l'enum LeadSource"""

    def test_lead_source_values(self):
        """Test des valeurs de LeadSource"""
        assert LeadSource.SIRENE.value == "sirene"
        assert LeadSource.CLEARBIT.value == "clearbit"
        assert LeadSource.HUNTER.value == "hunter"
        assert LeadSource.APOLLO.value == "apollo"
        assert LeadSource.MANUAL.value == "manual"


if __name__ == "__main__":
    pytest.main([__file__])
