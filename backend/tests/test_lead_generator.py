"""
Tests pour le module de génération de leads
"""

from unittest.mock import AsyncMock, Mock

import pytest

from lead_generator.lead_manager import LeadManager
from models import Company, LeadSource



class TestLeadManager:
    """Tests pour le gestionnaire de leads"""

    @pytest.fixture
    def lead_manager(self):
        """Fixture pour le gestionnaire de leads"""
        return LeadManager(enable_enrichment=False)  # Désactiver l'enrichissement pour les tests

    def test_lead_manager_initialization(self, lead_manager):
        """Test initialisation du gestionnaire"""
        assert lead_manager.sirene_auth_api is not None
        assert lead_manager.enable_enrichment is False
        assert lead_manager._processed_companies == set()

    def test_generate_company_key(self, lead_manager):
        """Test génération de clé unique pour une entreprise"""
        # Entreprise avec SIREN
        company1 = Company(name="Test 1", siren="*********", source=LeadSource.SIRENE)
        key1 = lead_manager._generate_company_key(company1)
        assert key1 == "siren:*********"

        # Entreprise avec domaine
        company2 = Company(name="Test 2", domain="test.fr", source=LeadSource.SIRENE)
        key2 = lead_manager._generate_company_key(company2)
        assert key2 == "domain:test.fr"

        # Entreprise avec nom seulement
        company3 = Company(name="Test Company", source=LeadSource.SIRENE)
        key3 = lead_manager._generate_company_key(company3)
        assert key3 == "name:test company"

    def test_is_duplicate(self, lead_manager):
        """Test détection des doublons"""
        company1 = Company(name="Test", siren="*********", source=LeadSource.SIRENE)
        company2 = Company(name="Test", siren="*********", source=LeadSource.SIRENE)

        # Premier appel : pas de doublon
        assert lead_manager._is_duplicate(company1) is False

        # Deuxième appel avec même SIREN : doublon détecté
        assert lead_manager._is_duplicate(company2) is True

    def test_guess_domain(self, lead_manager):
        """Test de devinette de domaine"""
        # Nom simple
        domain1 = lead_manager._guess_domain("Test Company")
        assert domain1 == "testcompany.fr"

        # Nom avec SARL
        domain2 = lead_manager._guess_domain("Test Company SARL")
        assert domain2 == "testcompany.fr"

        # Nom avec mots vides
        domain3 = lead_manager._guess_domain("La Société Test et Cie")
        assert domain3 == "societetest.fr"

        # Nom trop court
        domain4 = lead_manager._guess_domain("AB")
        assert domain4 is None

        # Nom vide
        domain5 = lead_manager._guess_domain("")
        assert domain5 is None

    @pytest.mark.asyncio
    async def test_export_and_load_leads(self, lead_manager, tmp_path):
        """Test export et chargement de leads"""
        # Créer des leads de test
        companies = [
            Company(name="Company 1", siren="*********", source=LeadSource.SIRENE),
            Company(name="Company 2", siren="*********", source=LeadSource.SIRENE),
        ]

        # Exporter
        export_file = tmp_path / "test_leads.json"
        await lead_manager.export_leads(companies, str(export_file))

        # Vérifier que le fichier existe
        assert export_file.exists()

        # Charger
        loaded_companies = await lead_manager.load_leads(str(export_file))

        # Vérifier
        assert len(loaded_companies) == 2
        assert loaded_companies[0].name == "Company 1"
        assert loaded_companies[1].siren == "*********"

    @pytest.mark.asyncio
    async def test_load_nonexistent_file(self, lead_manager):
        """Test chargement d'un fichier inexistant"""
        companies = await lead_manager.load_leads("nonexistent.json")
        assert companies == []


class TestLeadEnrichment:
    """Tests pour l'enrichissement de leads"""

    @pytest.mark.asyncio
    async def test_enrich_company_disabled(self):
        """Test enrichissement désactivé"""
        lead_manager = LeadManager(enable_enrichment=False)

        company = Company(name="Test", domain="test.fr", source=LeadSource.SIRENE)
        enriched = await lead_manager._enrich_company(company)

        # L'entreprise ne devrait pas être modifiée
        assert enriched.name == "Test"
        assert enriched.domain == "test.fr"

    @pytest.mark.asyncio
    async def test_enrich_company_with_free_apis(self):
        """Test enrichissement avec APIs gratuites (mock)"""
        # Mock de l'enrichissement gratuit
        mock_enrichment = Mock()
        mock_enrichment.enrich_company = AsyncMock(
            return_value=Company(name="Test Enriched", domain="test.fr", email="<EMAIL>", source=LeadSource.SIRENE)
        )

        lead_manager = LeadManager(enable_enrichment=True)
        lead_manager.free_enrichment = mock_enrichment

        company = Company(name="Test", domain="test.fr", source=LeadSource.SIRENE)
        await lead_manager._enrich_company(company)

        # Vérifier que l'enrichissement a été appelé
        mock_enrichment.enrich_company.assert_called_once()





if __name__ == "__main__":
    pytest.main([__file__, "-v"])
