"""
Tests pour le module de génération de leads
"""

import asyncio
from unittest.mock import As<PERSON><PERSON><PERSON>, Mo<PERSON>, patch

import pytest

from lead_generator.lead_manager import LeadManager
from lead_generator.sirene import SireneAPI
from models import Company, CompanySize, LeadSource


class TestSireneAPI:
    """Tests pour l'API Sirene"""

    @pytest.fixture
    def sirene_api(self):
        """Fixture pour l'API Sirene"""
        return SireneAPI(rate_limit=10)

    def test_sirene_api_initialization(self, sirene_api):
        """Test initialisation de l'API Sirene"""
        assert sirene_api.rate_limit == 10
        assert sirene_api.BASE_URL == "https://api.insee.fr/entreprises/sirene/V3"

    def test_map_company_size(self, sirene_api):
        """Test mapping des tailles d'entreprise"""
        # Test des codes effectifs Sirene
        assert sirene_api._map_company_size("01") == CompanySize.MICRO
        assert sirene_api._map_company_size("12") == CompanySize.SMALL
        assert sirene_api._map_company_size("22") == CompanySize.MEDIUM
        assert sirene_api._map_company_size("41") == CompanySize.LARGE
        assert sirene_api._map_company_size("00") is None
        assert sirene_api._map_company_size(None) is None

    def test_parse_company(self, sirene_api):
        """Test parsing des données Sirene"""
        # Données d'exemple de l'API Sirene
        etablissement_data = {
            "siret": "*********01234",
            "uniteLegale": {
                "siren": "*********",
                "denominationUniteLegale": "TEST COMPANY SARL",
                "trancheEffectifsUniteLegale": "12",
            },
            "adresseEtablissement": {
                "numeroVoieEtablissement": "123",
                "typeVoieEtablissement": "RUE",
                "libelleVoieEtablissement": "DE LA PAIX",
                "codePostalEtablissement": "75001",
                "libelleCommuneEtablissement": "PARIS",
                "libelleRegionEtablissement": "ÎLE-DE-FRANCE",
            },
            "activitePrincipaleEtablissement": "6201Z",
        }

        company = sirene_api._parse_company(etablissement_data)

        assert company.siren == "*********"
        assert company.siret == "*********01234"
        assert company.name == "TEST COMPANY SARL"
        assert company.city == "PARIS"
        assert company.postal_code == "75001"
        assert company.naf_code == "6201Z"
        assert company.size == CompanySize.SMALL
        assert company.source == LeadSource.SIRENE

    @pytest.mark.asyncio
    async def test_rate_limiting(self, sirene_api):
        """Test du rate limiting"""
        # Mock de la méthode _make_request
        sirene_api._make_request = AsyncMock(return_value={"test": "data"})

        start_time = asyncio.get_event_loop().time()

        # Faire plusieurs requêtes
        await sirene_api._make_request("test", {})
        await sirene_api._make_request("test", {})

        end_time = asyncio.get_event_loop().time()

        # Vérifier qu'il y a eu un délai (rate limiting)
        # Avec un rate limit de 10/s, l'intervalle minimum est 0.1s
        assert end_time - start_time >= 0.1


class TestLeadManager:
    """Tests pour le gestionnaire de leads"""

    @pytest.fixture
    def lead_manager(self):
        """Fixture pour le gestionnaire de leads"""
        return LeadManager(enable_enrichment=False)  # Désactiver l'enrichissement pour les tests

    def test_lead_manager_initialization(self, lead_manager):
        """Test initialisation du gestionnaire"""
        assert lead_manager.sirene_api is not None
        assert lead_manager.enable_enrichment is False
        assert lead_manager._processed_companies == set()

    def test_generate_company_key(self, lead_manager):
        """Test génération de clé unique pour une entreprise"""
        # Entreprise avec SIREN
        company1 = Company(name="Test 1", siren="*********", source=LeadSource.SIRENE)
        key1 = lead_manager._generate_company_key(company1)
        assert key1 == "siren:*********"

        # Entreprise avec domaine
        company2 = Company(name="Test 2", domain="test.fr", source=LeadSource.SIRENE)
        key2 = lead_manager._generate_company_key(company2)
        assert key2 == "domain:test.fr"

        # Entreprise avec nom seulement
        company3 = Company(name="Test Company", source=LeadSource.SIRENE)
        key3 = lead_manager._generate_company_key(company3)
        assert key3 == "name:test company"

    def test_is_duplicate(self, lead_manager):
        """Test détection des doublons"""
        company1 = Company(name="Test", siren="*********", source=LeadSource.SIRENE)
        company2 = Company(name="Test", siren="*********", source=LeadSource.SIRENE)

        # Premier appel : pas de doublon
        assert lead_manager._is_duplicate(company1) is False

        # Deuxième appel avec même SIREN : doublon détecté
        assert lead_manager._is_duplicate(company2) is True

    def test_guess_domain(self, lead_manager):
        """Test de devinette de domaine"""
        # Nom simple
        domain1 = lead_manager._guess_domain("Test Company")
        assert domain1 == "testcompany.fr"

        # Nom avec SARL
        domain2 = lead_manager._guess_domain("Test Company SARL")
        assert domain2 == "testcompany.fr"

        # Nom avec mots vides
        domain3 = lead_manager._guess_domain("La Société Test et Cie")
        assert domain3 == "societetest.fr"

        # Nom trop court
        domain4 = lead_manager._guess_domain("AB")
        assert domain4 is None

        # Nom vide
        domain5 = lead_manager._guess_domain("")
        assert domain5 is None

    @pytest.mark.asyncio
    async def test_export_and_load_leads(self, lead_manager, tmp_path):
        """Test export et chargement de leads"""
        # Créer des leads de test
        companies = [
            Company(name="Company 1", siren="*********", source=LeadSource.SIRENE),
            Company(name="Company 2", siren="*********", source=LeadSource.SIRENE),
        ]

        # Exporter
        export_file = tmp_path / "test_leads.json"
        await lead_manager.export_leads(companies, str(export_file))

        # Vérifier que le fichier existe
        assert export_file.exists()

        # Charger
        loaded_companies = await lead_manager.load_leads(str(export_file))

        # Vérifier
        assert len(loaded_companies) == 2
        assert loaded_companies[0].name == "Company 1"
        assert loaded_companies[1].siren == "*********"

    @pytest.mark.asyncio
    async def test_load_nonexistent_file(self, lead_manager):
        """Test chargement d'un fichier inexistant"""
        companies = await lead_manager.load_leads("nonexistent.json")
        assert companies == []


class TestLeadEnrichment:
    """Tests pour l'enrichissement de leads"""

    @pytest.mark.asyncio
    async def test_enrich_company_disabled(self):
        """Test enrichissement désactivé"""
        lead_manager = LeadManager(enable_enrichment=False)

        company = Company(name="Test", domain="test.fr", source=LeadSource.SIRENE)
        enriched = await lead_manager._enrich_company(company)

        # L'entreprise ne devrait pas être modifiée
        assert enriched.name == "Test"
        assert enriched.domain == "test.fr"

    @pytest.mark.asyncio
    async def test_enrich_company_with_clearbit(self):
        """Test enrichissement avec Clearbit (mock)"""
        # Mock de l'API Clearbit
        mock_clearbit = Mock()
        mock_clearbit.enrich_company_data = AsyncMock(
            return_value=Company(name="Test Enriched", domain="test.fr", email="<EMAIL>", source=LeadSource.CLEARBIT)
        )

        lead_manager = LeadManager(enable_enrichment=True)
        lead_manager.clearbit_api = mock_clearbit

        company = Company(name="Test", domain="test.fr", source=LeadSource.SIRENE)
        enriched = await lead_manager._enrich_company(company)

        # Vérifier que l'enrichissement a été appelé
        mock_clearbit.enrich_company_data.assert_called_once()


@pytest.mark.integration
class TestSireneIntegration:
    """Tests d'intégration avec l'API Sirene (nécessitent une connexion internet)"""

    @pytest.mark.asyncio
    async def test_sirene_api_real_request(self):
        """Test avec une vraie requête à l'API Sirene"""
        sirene_api = SireneAPI(rate_limit=1)  # Limite basse pour les tests

        try:
            # Rechercher quelques entreprises
            companies = []
            async for company in sirene_api.search_companies(naf_codes=["6201Z"], limit=5):  # Programmation informatique
                companies.append(company)
                if len(companies) >= 2:  # Limiter pour les tests
                    break

            # Vérifications basiques
            assert len(companies) > 0
            for company in companies:
                assert company.name is not None
                assert company.source == LeadSource.SIRENE
                assert company.naf_code == "6201Z"

        except Exception as e:
            pytest.skip(f"API Sirene non accessible: {e}")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
