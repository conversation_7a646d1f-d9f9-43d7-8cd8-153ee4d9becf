#!/usr/bin/env python3
"""
Test de l'API OpenDataSoft v2.1 (nouvelle version)
"""

import asyncio
import json

import httpx
from rich.console import Console
from rich.table import Table

console = Console()

class OpenDataSoftV2Tester:
    """Testeur pour l'API OpenDataSoft v2.1"""
    
    def __init__(self):
        self.base_url = "https://data.opendatasoft.com/api/explore/v2.1"
        
    async def list_datasets(self):
        """Liste les datasets disponibles"""
        console.print("🔍 Recherche des datasets Sirene disponibles...")
        
        try:
            url = f"{self.base_url}/catalog/datasets"
            params = {
                "where": "title like 'sirene' or title like 'entreprise'",
                "limit": 10
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.get(url, params=params, timeout=30.0)
                
                if response.status_code == 200:
                    data = response.json()
                    datasets = data.get('results', [])
                    
                    console.print(f"✅ {len(datasets)} datasets trouvés:")
                    
                    for dataset in datasets:
                        dataset_id = dataset.get('dataset_id', 'N/A')
                        title = dataset.get('metas', {}).get('default', {}).get('title', 'N/A')
                        console.print(f"   • {dataset_id}: {title}")
                        
                    return datasets
                else:
                    console.print(f"❌ Erreur: {response.status_code}")
                    return []
                    
        except Exception as e:
            console.print(f"❌ Exception: {e}")
            return []
            
    async def test_sirene_dataset(self, dataset_id):
        """Test un dataset Sirene spécifique"""
        console.print(f"🔍 Test du dataset: {dataset_id}")
        
        try:
            # Test recherche basique
            url = f"{self.base_url}/catalog/datasets/{dataset_id}/records"
            params = {
                "limit": 5
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.get(url, params=params, timeout=30.0)
                
                if response.status_code == 200:
                    data = response.json()
                    records = data.get('results', [])
                    
                    console.print(f"   ✅ {len(records)} enregistrements trouvés")
                    
                    if records:
                        # Analyser la structure
                        first_record = records[0]
                        fields = first_record.get('record', {}).get('fields', {})
                        
                        console.print("   📊 Champs disponibles:")
                        for field_name, field_value in list(fields.items())[:10]:
                            console.print(f"      • {field_name}: {str(field_value)[:50]}...")
                            
                        return True, fields.keys()
                else:
                    console.print(f"   ❌ Erreur: {response.status_code}")
                    return False, []
                    
        except Exception as e:
            console.print(f"   ❌ Exception: {e}")
            return False, []
            
    async def test_search_by_naf(self, dataset_id, naf_field):
        """Test de recherche par code NAF"""
        console.print(f"🔍 Test recherche NAF sur {dataset_id}...")
        
        naf_codes = ["6201Z", "6202A", "4711F"]
        results = []
        
        for naf_code in naf_codes:
            try:
                url = f"{self.base_url}/catalog/datasets/{dataset_id}/records"
                params = {
                    "where": f"{naf_field} = '{naf_code}'",
                    "limit": 10
                }
                
                async with httpx.AsyncClient() as client:
                    response = await client.get(url, params=params, timeout=30.0)
                    
                    if response.status_code == 200:
                        data = response.json()
                        records = data.get('results', [])
                        count = len(records)
                        
                        example = ""
                        if records:
                            fields = records[0].get('record', {}).get('fields', {})
                            # Chercher un champ nom
                            name_fields = ['denomination', 'nom', 'raison_sociale', 'l1_normalisee']
                            for name_field in name_fields:
                                if name_field in fields:
                                    example = fields[name_field]
                                    break
                            
                        results.append({
                            "naf": naf_code,
                            "count": count,
                            "example": example or "N/A",
                            "status": "✅" if count > 0 else "❌"
                        })
                        
                        console.print(f"   {naf_code}: {count} résultats")
                        if example:
                            console.print(f"      Exemple: {example}")
                    else:
                        results.append({
                            "naf": naf_code,
                            "count": 0,
                            "example": f"Erreur {response.status_code}",
                            "status": "❌"
                        })
                        
                await asyncio.sleep(0.5)
                
            except Exception as e:
                results.append({
                    "naf": naf_code,
                    "count": 0,
                    "example": f"Exception: {e}",
                    "status": "❌"
                })
                
        return results
        
    async def test_geographic_search(self, dataset_id, geo_fields):
        """Test de recherche géographique"""
        console.print(f"🔍 Test recherche géographique sur {dataset_id}...")
        
        # Essayer différents champs géographiques
        test_locations = [
            ("Paris", ["75", "75001", "PARIS"]),
            ("Lyon", ["69", "69001", "LYON"]),
        ]
        
        results = []
        
        for city, codes in test_locations:
            best_result = {"count": 0, "field": "", "value": ""}
            
            for geo_field in geo_fields:
                for code in codes:
                    try:
                        url = f"{self.base_url}/catalog/datasets/{dataset_id}/records"
                        params = {
                            "where": f"{geo_field} = '{code}'",
                            "limit": 5
                        }
                        
                        async with httpx.AsyncClient() as client:
                            response = await client.get(url, params=params, timeout=30.0)
                            
                            if response.status_code == 200:
                                data = response.json()
                                count = len(data.get('results', []))
                                
                                if count > best_result["count"]:
                                    best_result = {
                                        "count": count,
                                        "field": geo_field,
                                        "value": code
                                    }
                                    
                        await asyncio.sleep(0.2)
                        
                    except Exception:
                        continue
                        
            results.append({
                "city": city,
                "count": best_result["count"],
                "best_field": best_result["field"],
                "best_value": best_result["value"],
                "status": "✅" if best_result["count"] > 0 else "❌"
            })
            
            console.print(f"   {city}: {best_result['count']} résultats")
            
        return results
        
    async def run_full_test(self):
        """Lance tous les tests"""
        console.print("🚀 Test complet de l'API OpenDataSoft v2.1")
        console.print("="*60)
        
        # 1. Lister les datasets
        datasets = await self.list_datasets()
        
        if not datasets:
            console.print("❌ Aucun dataset Sirene trouvé")
            return
            
        # 2. Tester chaque dataset (prioriser les datasets consolidés)
        working_datasets = []

        # Prioriser les datasets avec "consolidee" dans le nom
        prioritized_datasets = sorted(datasets, key=lambda d:
            0 if 'consolidee' in d.get('metas', {}).get('default', {}).get('title', '').lower() else 1)

        for dataset in prioritized_datasets[:5]:  # Tester les 5 premiers
            dataset_id = dataset.get('dataset_id')
            if dataset_id:
                works, fields = await self.test_sirene_dataset(dataset_id)
                if works:
                    working_datasets.append({
                        "id": dataset_id,
                        "fields": list(fields),
                        "title": dataset.get('metas', {}).get('default', {}).get('title', 'N/A')
                    })
                    
        if not working_datasets:
            console.print("❌ Aucun dataset fonctionnel trouvé")
            return
            
        # 3. Tester le meilleur dataset
        best_dataset = working_datasets[0]
        console.print(f"\n🎯 Test approfondi du dataset: {best_dataset['id']}")
        
        # Identifier les champs NAF et géographiques
        fields = best_dataset['fields']
        naf_fields = [f for f in fields if 'naf' in f.lower() or 'activite' in f.lower()]
        geo_fields = [f for f in fields if any(geo in f.lower() for geo in ['commune', 'ville', 'postal', 'region', 'departement'])]
        
        console.print(f"   📊 Champs NAF détectés: {naf_fields}")
        console.print(f"   📊 Champs géo détectés: {geo_fields}")
        
        # Tests spécialisés
        naf_results = []
        geo_results = []
        
        if naf_fields:
            naf_results = await self.test_search_by_naf(best_dataset['id'], naf_fields[0])
            
        if geo_fields:
            geo_results = await self.test_geographic_search(best_dataset['id'], geo_fields[:3])
        
        # Résumé final
        console.print("\n" + "="*60)
        console.print("📊 RÉSUMÉ FINAL")
        
        console.print(f"\n✅ Datasets fonctionnels: {len(working_datasets)}")
        for ds in working_datasets:
            console.print(f"   • {ds['id']}: {ds['title']}")
            
        if naf_results:
            table_naf = Table(title="Résultats Recherche NAF")
            table_naf.add_column("Code NAF", style="bold")
            table_naf.add_column("Statut")
            table_naf.add_column("Résultats")
            table_naf.add_column("Exemple")
            
            for result in naf_results:
                table_naf.add_row(
                    result["naf"],
                    result["status"],
                    str(result["count"]),
                    result["example"][:30] + "..." if len(result["example"]) > 30 else result["example"]
                )
            
            console.print(table_naf)
            
        if geo_results:
            table_geo = Table(title="Résultats Recherche Géographique")
            table_geo.add_column("Ville", style="bold")
            table_geo.add_column("Statut")
            table_geo.add_column("Résultats")
            table_geo.add_column("Meilleur champ")
            
            for result in geo_results:
                table_geo.add_row(
                    result["city"],
                    result["status"],
                    str(result["count"]),
                    f"{result['best_field']}={result['best_value']}"
                )
            
            console.print(table_geo)
        
        # Recommandations
        console.print("\n💡 RECOMMANDATIONS:")
        
        working_naf = [r for r in naf_results if r["count"] > 0]
        working_geo = [r for r in geo_results if r["count"] > 0]
        
        if working_datasets and working_naf and working_geo:
            console.print("🎉 OpenDataSoft v2.1 fonctionne parfaitement !")
            console.print(f"✅ Utiliser le dataset: {best_dataset['id']}")
            console.print("✅ Remplacer l'API Sirene par OpenDataSoft v2.1")
            console.print("✅ API moderne et fiable")
        elif working_datasets and (working_naf or working_geo):
            console.print("⚠️ OpenDataSoft v2.1 fonctionne partiellement")
            console.print(f"✅ Dataset utilisable: {best_dataset['id']}")
            console.print("⚠️ Certaines recherches limitées")
        else:
            console.print("❌ OpenDataSoft v2.1 ne répond pas aux besoins")

async def main():
    """Point d'entrée principal"""
    tester = OpenDataSoftV2Tester()
    await tester.run_full_test()

if __name__ == "__main__":
    asyncio.run(main())
