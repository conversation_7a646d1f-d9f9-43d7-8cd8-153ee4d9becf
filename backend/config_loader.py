"""
Module de chargement de configuration pour le système de prospection
"""

import os
import yaml
from typing import Dict, Any, Optional


class ConfigLoader:
    """Gestionnaire de configuration centralisé"""
    
    def __init__(self, config_path: str = "config/config.yaml"):
        self.config_path = config_path
        self._config = None
        self.load_config()
    
    def load_config(self) -> None:
        """Charge la configuration depuis le fichier YAML"""
        try:
            with open(self.config_path, "r", encoding="utf-8") as f:
                self._config = yaml.safe_load(f)
        except FileNotFoundError:
            print(f"⚠️ Fichier de configuration non trouvé: {self.config_path}")
            self._config = {}
        except Exception as e:
            print(f"⚠️ Erreur chargement configuration: {e}")
            self._config = {}
    
    def get(self, key: str, default: Any = None) -> Any:
        """Récupère une valeur de configuration"""
        if not self._config:
            return default
        
        keys = key.split(".")
        value = self._config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def get_mysql_url(self) -> str:
        """Construit l'URL MySQL depuis les variables d'environnement"""
        host = os.getenv("MYSQL_HOST", "localhost")
        port = os.getenv("MYSQL_PORT", "3306")
        user = os.getenv("MYSQL_USER", "root")
        password = os.getenv("MYSQL_PASSWORD", "")
        database = os.getenv("MYSQL_DATABASE", "prospection")
        
        return f"mysql+aiomysql://{user}:{password}@{host}:{port}/{database}"
    
    @property
    def MYSQL_DATABASE(self) -> str:
        """Nom de la base de données MySQL"""
        return os.getenv("MYSQL_DATABASE", "prospection")


# Instance globale
config = ConfigLoader()
