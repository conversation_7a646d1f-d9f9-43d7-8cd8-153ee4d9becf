"""
Vérificateur simple de réponses aux emails de prospection
Version simplifiée sans IMAP
"""

import asyncio
import logging
import re
from dataclasses import dataclass
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from logging_config import LoggerMixin
from sender.mysql_manager import MySQLManager

logger = logging.getLogger(__name__)


@dataclass
class SimpleReplyCheck:
    """Résultat de vérification de réponse"""

    email_sent_to: str
    company_name: str
    sent_at: datetime
    days_since_sent: int
    expected_reply_indicators: List[str]


class SimpleReplyChecker(LoggerMixin):
    """Vérificateur simple de réponses"""

    def __init__(self):
        self.mysql_manager = None
        self._initialized = False

    async def initialize(self) -> None:
        """Initialise le checker"""
        if self._initialized:
            return

        try:
            self.mysql_manager = MySQLManager()
            await self.mysql_manager.initialize()
            self._initialized = True
            self.log_info("Simple reply checker initialisé")
        except Exception as e:
            self.log_error("Erreur initialisation checker", error=e)
            raise

    async def get_sent_emails_awaiting_reply(self, days_back: int = 7) -> List[SimpleReplyCheck]:
        """
        Récupère les emails envoyés qui pourraient avoir des réponses

        Args:
            days_back: Nombre de jours en arrière

        Returns:
            Liste des emails en attente de réponse
        """
        await self.initialize()

        try:
            # Récupérer les emails envoyés avec succès
            sent_emails = await self.mysql_manager.get_sent_emails(limit=100, offset=0, status=None)  # Tous les statuts

            # Filtrer les emails récents et réussis
            cutoff_date = datetime.now() - timedelta(days=days_back)
            awaiting_replies = []

            for email in sent_emails:
                if email.sent_at >= cutoff_date and email.status.value == "sent" and email.email_to and email.email_to != "":

                    days_since = (datetime.now() - email.sent_at).days

                    # Générer des indicateurs de réponse possibles
                    reply_indicators = self._generate_reply_indicators(email.subject, email.company_name)

                    awaiting_replies.append(
                        SimpleReplyCheck(
                            email_sent_to=email.email_to,
                            company_name=email.company_name,
                            sent_at=email.sent_at,
                            days_since_sent=days_since,
                            expected_reply_indicators=reply_indicators,
                        )
                    )

            return awaiting_replies

        except Exception as e:
            self.log_error("Erreur récupération emails", error=e)
            return []

    def _generate_reply_indicators(self, original_subject: str, company_name: str) -> List[str]:
        """
        Génère les indicateurs possibles de réponse

        Args:
            original_subject: Sujet de l'email original
            company_name: Nom de l'entreprise

        Returns:
            Liste des indicateurs de réponse
        """
        indicators = []

        # Réponses directes avec "Re:"
        indicators.append(f"Re: {original_subject}")
        indicators.append(f"RE: {original_subject}")

        # Variations du nom de l'entreprise
        indicators.append(company_name.lower())
        indicators.append(company_name.upper())

        # Mots-clés de réponse positive
        positive_keywords = [
            "intéressé",
            "interested",
            "merci",
            "thank you",
            "rendez-vous",
            "meeting",
            "appelez",
            "call",
            "discuter",
            "discuss",
            "projet",
            "project",
            "devis",
            "quote",
            "tarif",
            "price",
        ]
        indicators.extend(positive_keywords)

        # Mots-clés de réponse négative
        negative_keywords = [
            "pas intéressé",
            "not interested",
            "non merci",
            "no thank",
            "déjà",
            "already",
            "stop",
            "unsubscribe",
            "ne pas contacter",
            "do not contact",
        ]
        indicators.extend(negative_keywords)

        return indicators

    async def generate_reply_monitoring_guide(self, days_back: int = 7) -> str:
        """
        Génère un guide pour surveiller manuellement les réponses

        Args:
            days_back: Nombre de jours en arrière

        Returns:
            Guide de surveillance formaté
        """
        await self.initialize()

        awaiting_replies = await self.get_sent_emails_awaiting_reply(days_back)

        if not awaiting_replies:
            return "✅ Aucun email en attente de réponse dans les derniers jours."

        guide = f"""
📧 GUIDE DE SURVEILLANCE DES RÉPONSES
=====================================

Emails envoyés dans les {days_back} derniers jours : {len(awaiting_replies)}

INSTRUCTIONS :
1. Vérifiez votre boîte de réception Gmail
2. Recherchez les emails contenant ces indicateurs :

"""

        for i, check in enumerate(awaiting_replies, 1):
            guide += f"""
--- EMAIL {i} ---
Entreprise: {check.company_name}
Envoyé à: {check.email_sent_to}
Date d'envoi: {check.sent_at.strftime('%d/%m/%Y %H:%M')}
Jours écoulés: {check.days_since_sent}

🔍 RECHERCHER DANS GMAIL :
• from:{check.email_sent_to}
• "{check.company_name}"
• Sujet contenant "Re:" ou "RE:"

⚠️  INDICATEURS DE RÉPONSE :
"""

            # Grouper les indicateurs
            positive_indicators = [
                ind
                for ind in check.expected_reply_indicators
                if ind
                in [
                    "intéressé",
                    "interested",
                    "merci",
                    "thank you",
                    "rendez-vous",
                    "meeting",
                    "appelez",
                    "call",
                    "discuter",
                    "discuss",
                    "projet",
                    "project",
                ]
            ]

            negative_indicators = [
                ind
                for ind in check.expected_reply_indicators
                if ind in ["pas intéressé", "not interested", "non merci", "déjà", "already", "stop", "unsubscribe"]
            ]

            if positive_indicators:
                guide += f"✅ POSITIF: {', '.join(positive_indicators[:3])}\n"

            if negative_indicators:
                guide += f"❌ NÉGATIF: {', '.join(negative_indicators[:3])}\n"

            guide += "\n"

        guide += """
📊 ACTIONS À PRENDRE :
===================

SI RÉPONSE POSITIVE :
• Noter l'entreprise comme intéressée
• Programmer un suivi
• Préparer une proposition

SI RÉPONSE NÉGATIVE :
• Marquer comme non intéressée
• Éviter de recontacter
• Analyser les raisons du refus

SI PAS DE RÉPONSE :
• Attendre 7-10 jours
• Envisager un email de relance
• Vérifier si l'email est arrivé

💡 CONSEIL :
Utilisez les filtres Gmail pour automatiser la détection :
Créez un libellé "Réponses Prospection" avec ces critères.
"""

        return guide

    async def get_reply_statistics(self, days_back: int = 30) -> Dict[str, Any]:
        """
        Calcule les statistiques de réponse estimées

        Args:
            days_back: Nombre de jours en arrière

        Returns:
            Statistiques de réponse
        """
        await self.initialize()

        try:
            # Récupérer tous les emails envoyés
            all_emails = await self.mysql_manager.get_sent_emails(limit=1000)

            # Filtrer par période
            cutoff_date = datetime.now() - timedelta(days=days_back)
            recent_emails = [e for e in all_emails if e.sent_at >= cutoff_date]

            # Calculer les statistiques
            total_sent = len([e for e in recent_emails if e.status.value == "sent"])
            total_failed = len([e for e in recent_emails if e.status.value == "failed"])

            # Estimer les réponses (à mettre à jour manuellement)
            # Pour l'instant, on utilise des moyennes du secteur
            estimated_response_rate = 0.02  # 2% de taux de réponse moyen
            estimated_positive_rate = 0.005  # 0.5% de taux de réponse positive

            estimated_responses = int(total_sent * estimated_response_rate)
            estimated_positive = int(total_sent * estimated_positive_rate)

            return {
                "period_days": days_back,
                "total_emails_sent": total_sent,
                "total_emails_failed": total_failed,
                "estimated_total_responses": estimated_responses,
                "estimated_positive_responses": estimated_positive,
                "estimated_response_rate": f"{estimated_response_rate*100:.1f}%",
                "estimated_positive_rate": f"{estimated_positive_rate*100:.1f}%",
                "note": "Statistiques estimées - à confirmer manuellement",
            }

        except Exception as e:
            self.log_error("Erreur calcul statistiques", error=e)
            return {}


async def main():
    """Test du checker simple"""
    checker = SimpleReplyChecker()

    print("🔍 Test du vérificateur simple de réponses...")

    # Générer le guide de surveillance
    guide = await checker.generate_reply_monitoring_guide(7)
    print(guide)

    # Afficher les statistiques
    stats = await checker.get_reply_statistics(30)
    print("\n📊 STATISTIQUES ESTIMÉES :")
    for key, value in stats.items():
        print(f"{key}: {value}")


if __name__ == "__main__":
    asyncio.run(main())
