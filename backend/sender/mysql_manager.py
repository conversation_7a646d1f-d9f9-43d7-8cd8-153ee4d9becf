"""
Gestionnaire de base de données MySQL pour le système de prospection B2B
"""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, DateTime, Integer, String, Text, func, select, JSON
from sqlalchemy.dialects.mysql import LONGTEXT, ENUM
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column

from config import config
from logging_config import LoggerMixin
from models import EmailStatus, SentEmail

logger = logging.getLogger(__name__)


class Base(DeclarativeBase):
    """Base pour les modèles SQLAlchemy"""

    pass


class SentEmailTable(Base):
    """Table des emails envoyés"""

    __tablename__ = "sent_emails"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    company_name: Mapped[str] = mapped_column(String(255), nullable=False)
    company_siren: Mapped[Optional[str]] = mapped_column(String(20), nullable=True)
    email_to: Mapped[str] = mapped_column(String(255), nullable=False)
    subject: Mapped[str] = mapped_column(String(500), nullable=False)
    body: Mapped[str] = mapped_column(LONGTEXT, nullable=False)
    status: Mapped[str] = mapped_column(String(50), nullable=False)
    sent_at: Mapped[datetime] = mapped_column(DateTime, nullable=False)
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    provider: Mapped[str] = mapped_column(String(50), nullable=False, default="smtp")

    # Tracking
    tracking_id: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    opened: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)
    opened_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    clicked: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)
    clicked_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    replied: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)
    replied_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)

    # Relance
    followup_scheduled: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)
    followup_scheduled_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    followup_sent_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)

    def to_sent_email(self) -> SentEmail:
        """Convertit vers le modèle Pydantic"""
        return SentEmail(
            id=self.id,
            company_name=self.company_name,
            company_siren=self.company_siren,
            email_to=self.email_to,
            subject=self.subject,
            body=self.body,
            status=EmailStatus(self.status),
            sent_at=self.sent_at,
            error_message=self.error_message,
            provider=self.provider,
            tracking_id=self.tracking_id,
            opened=self.opened,
            opened_at=self.opened_at,
            clicked=self.clicked,
            clicked_at=self.clicked_at,
            replied=self.replied,
            replied_at=self.replied_at,
            followup_scheduled=self.followup_scheduled,
            followup_scheduled_at=self.followup_scheduled_at,
            followup_sent_at=self.followup_sent_at,
        )


class EmailTrackingEventTable(Base):
    """Table des événements de tracking"""

    __tablename__ = "email_tracking_events"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    email_id: Mapped[int] = mapped_column(Integer, nullable=False)
    tracking_id: Mapped[str] = mapped_column(String(255), nullable=False)
    event_type: Mapped[str] = mapped_column(ENUM('open', 'click'), nullable=False)
    event_data: Mapped[Optional[str]] = mapped_column(JSON, nullable=True)
    user_agent: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    ip_address: Mapped[Optional[str]] = mapped_column(String(45), nullable=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, nullable=False, default=datetime.now)


class TrackedLinkTable(Base):
    """Table des liens trackés"""

    __tablename__ = "tracked_links"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    email_id: Mapped[int] = mapped_column(Integer, nullable=False)
    tracking_id: Mapped[str] = mapped_column(String(255), nullable=False)
    original_url: Mapped[str] = mapped_column(Text, nullable=False)
    link_id: Mapped[str] = mapped_column(String(255), nullable=False)
    created_at: Mapped[datetime] = mapped_column(DateTime, nullable=False, default=datetime.now)


class MySQLManager(LoggerMixin):
    """Gestionnaire de base de données MySQL"""

    def __init__(self):
        self.engine = None
        self.session_factory = None
        self._initialized = False

    async def initialize(self) -> None:
        """Initialise la connexion MySQL"""
        if self._initialized:
            return

        try:
            # Configuration depuis le module config centralisé
            database_url = config.get_mysql_url()

            # Créer le moteur async
            self.engine = create_async_engine(
                database_url,
                echo=False,  # Mettre à True pour debug SQL
                pool_size=10,
                max_overflow=20,
                pool_pre_ping=True,
                pool_recycle=3600,
            )

            # Créer la factory de sessions
            self.session_factory = async_sessionmaker(self.engine, class_=AsyncSession, expire_on_commit=False)

            # Créer les tables
            await self._create_tables()

            self._initialized = True
            self.log_info(f"Base de données MySQL initialisée: {config.MYSQL_DATABASE}")

        except Exception as e:
            self.log_error("Erreur initialisation MySQL", error=e)
            raise

    async def _create_tables(self) -> None:
        """Crée les tables si elles n'existent pas"""
        try:
            async with self.engine.begin() as conn:
                await conn.run_sync(Base.metadata.create_all)
            self.log_info("Tables MySQL créées/vérifiées")
        except Exception as e:
            self.log_error("Erreur création tables", error=e)
            raise

    async def save_sent_email(self, sent_email: SentEmail) -> int:
        """
        Sauvegarde un email envoyé

        Args:
            sent_email: Email à sauvegarder

        Returns:
            ID de l'email sauvegardé
        """
        try:
            async with self.session_factory() as session:
                email_table = SentEmailTable(
                    company_name=sent_email.company_name,
                    company_siren=sent_email.company_siren,
                    email_to=sent_email.email_to,
                    subject=sent_email.subject,
                    body=sent_email.body,
                    status=sent_email.status.value,
                    sent_at=sent_email.sent_at,
                    error_message=sent_email.error_message,
                    provider=sent_email.provider,
                    tracking_id=sent_email.tracking_id,
                    opened=sent_email.opened,
                    opened_at=sent_email.opened_at,
                    clicked=sent_email.clicked,
                    clicked_at=sent_email.clicked_at,
                    replied=sent_email.replied,
                    replied_at=sent_email.replied_at,
                    followup_scheduled=sent_email.followup_scheduled,
                    followup_scheduled_at=sent_email.followup_scheduled_at,
                    followup_sent_at=sent_email.followup_sent_at,
                )

                session.add(email_table)
                await session.commit()
                await session.refresh(email_table)

                self.log_info(f"Email sauvegardé avec ID: {email_table.id}")
                return email_table.id

        except Exception as e:
            self.log_error("Erreur sauvegarde email", error=e)
            raise

    async def update_email_tracking_id(self, email_id: int, tracking_id: str) -> None:
        """
        Met à jour le tracking_id d'un email

        Args:
            email_id: ID de l'email
            tracking_id: ID de tracking
        """
        try:
            async with self.session_factory() as session:
                # Utiliser SQLAlchemy ORM au lieu de SQL brut
                result = await session.execute(
                    select(SentEmailTable).where(SentEmailTable.id == email_id)
                )
                email_record = result.scalar_one_or_none()

                if email_record:
                    email_record.tracking_id = tracking_id
                    await session.commit()
                    self.log_info(f"Tracking ID mis à jour pour email {email_id}")
                else:
                    self.log_warning(f"Email {email_id} non trouvé pour mise à jour tracking")

        except Exception as e:
            self.log_error("Erreur mise à jour tracking ID", error=e)
            raise

    async def get_sent_emails(
        self, limit: int = 100, offset: int = 0, company_name: Optional[str] = None, status: Optional[EmailStatus] = None
    ) -> List[SentEmail]:
        """
        Récupère les emails envoyés

        Args:
            limit: Nombre maximum d'emails
            offset: Décalage pour pagination
            company_name: Filtrer par nom d'entreprise
            status: Filtrer par statut

        Returns:
            Liste des emails envoyés
        """
        try:
            async with self.session_factory() as session:
                query = select(SentEmailTable).order_by(SentEmailTable.sent_at.desc())

                if company_name:
                    query = query.where(SentEmailTable.company_name.ilike(f"%{company_name}%"))

                if status:
                    query = query.where(SentEmailTable.status == status.value)

                query = query.limit(limit).offset(offset)

                result = await session.execute(query)
                email_tables = result.scalars().all()

                return [email_table.to_sent_email() for email_table in email_tables]

        except Exception as e:
            self.log_error("Erreur récupération emails", error=e)
            return []

    async def check_email_sent(self, company_siren: str, email_to: str) -> bool:
        """
        Vérifie si un email a déjà été envoyé à cette entreprise

        Args:
            company_siren: SIREN de l'entreprise
            email_to: Adresse email

        Returns:
            True si un email a déjà été envoyé
        """
        try:
            async with self.session_factory() as session:
                query = select(func.count(SentEmailTable.id)).where(
                    SentEmailTable.company_siren == company_siren,
                    SentEmailTable.email_to == email_to,
                    SentEmailTable.status == EmailStatus.SENT.value,
                )

                result = await session.execute(query)
                count = result.scalar()

                return count > 0

        except Exception as e:
            self.log_error("Erreur vérification email", error=e)
            return False

    async def get_statistics(self, days_back: int = 30) -> Dict[str, Any]:
        """
        Récupère les statistiques détaillées d'envoi avec tracking

        Args:
            days_back: Nombre de jours en arrière pour les statistiques

        Returns:
            Dictionnaire avec les statistiques complètes
        """
        try:
            from datetime import timedelta

            async with self.session_factory() as session:
                # Date de début pour la période
                start_date = datetime.now() - timedelta(days=days_back)

                # Total emails dans la période
                total_query = select(func.count(SentEmailTable.id)).where(
                    SentEmailTable.sent_at >= start_date
                )
                total_result = await session.execute(total_query)
                total_sent = total_result.scalar() or 0

                # Emails réussis
                success_query = select(func.count(SentEmailTable.id)).where(
                    SentEmailTable.status == EmailStatus.SENT.value,
                    SentEmailTable.sent_at >= start_date
                )
                success_result = await session.execute(success_query)
                successful = success_result.scalar() or 0

                # Emails échoués
                failed_query = select(func.count(SentEmailTable.id)).where(
                    SentEmailTable.status == EmailStatus.FAILED.value,
                    SentEmailTable.sent_at >= start_date
                )
                failed_result = await session.execute(failed_query)
                failed = failed_result.scalar() or 0

                # Emails ouverts (vraies métriques)
                opened_query = select(func.count(SentEmailTable.id)).where(
                    SentEmailTable.opened == True,
                    SentEmailTable.sent_at >= start_date
                )
                opened_result = await session.execute(opened_query)
                opened = opened_result.scalar() or 0

                # Emails cliqués (vraies métriques)
                clicked_query = select(func.count(SentEmailTable.id)).where(
                    SentEmailTable.clicked == True,
                    SentEmailTable.sent_at >= start_date
                )
                clicked_result = await session.execute(clicked_query)
                clicked = clicked_result.scalar() or 0

                # Emails avec réponse (vraies métriques)
                replied_query = select(func.count(SentEmailTable.id)).where(
                    SentEmailTable.replied == True,
                    SentEmailTable.sent_at >= start_date
                )
                replied_result = await session.execute(replied_query)
                replied = replied_result.scalar() or 0

                # Emails aujourd'hui
                today = datetime.now().date()
                today_query = select(func.count(SentEmailTable.id)).where(
                    func.date(SentEmailTable.sent_at) == today
                )
                today_result = await session.execute(today_query)
                today_emails = today_result.scalar() or 0

                # Calculer les taux
                success_rate = round((successful / total_sent * 100) if total_sent > 0 else 0, 2)
                open_rate = round((opened / successful * 100) if successful > 0 else 0, 2)
                click_rate = round((clicked / successful * 100) if successful > 0 else 0, 2)
                reply_rate = round((replied / successful * 100) if successful > 0 else 0, 2)

                # Répartition quotidienne
                daily_breakdown = []
                for i in range(days_back):
                    date = (datetime.now() - timedelta(days=i)).date()
                    daily_query = select(func.count(SentEmailTable.id)).where(
                        func.date(SentEmailTable.sent_at) == date
                    )
                    daily_result = await session.execute(daily_query)
                    count = daily_result.scalar() or 0
                    daily_breakdown.append({"date": str(date), "count": count})

                # Inverser pour avoir l'ordre chronologique
                daily_breakdown.reverse()

                return {
                    "period_days": days_back,
                    "total_sent": total_sent,
                    "successful": successful,
                    "failed": failed,
                    "opened": opened,
                    "clicked": clicked,
                    "replied": replied,
                    "today_emails": today_emails,
                    "success_rate": success_rate,
                    "open_rate": open_rate,
                    "click_rate": click_rate,
                    "reply_rate": reply_rate,
                    "daily_breakdown": daily_breakdown
                }

        except Exception as e:
            self.log_error("Erreur récupération statistiques", error=e)
            return {
                "period_days": days_back,
                "total_sent": 0,
                "successful": 0,
                "failed": 0,
                "opened": 0,
                "clicked": 0,
                "replied": 0,
                "today_emails": 0,
                "success_rate": 0,
                "open_rate": 0,
                "click_rate": 0,
                "reply_rate": 0,
                "daily_breakdown": []
            }

    async def get_stats(self) -> Dict[str, Any]:
        """
        Récupère les statistiques d'envoi (méthode legacy)

        Returns:
            Dictionnaire avec les statistiques
        """
        try:
            async with self.session_factory() as session:
                # Total emails
                total_query = select(func.count(SentEmailTable.id))
                total_result = await session.execute(total_query)
                total = total_result.scalar()

                # Emails réussis
                success_query = select(func.count(SentEmailTable.id)).where(SentEmailTable.status == EmailStatus.SENT.value)
                success_result = await session.execute(success_query)
                success = success_result.scalar()

                # Emails échoués
                failed_query = select(func.count(SentEmailTable.id)).where(SentEmailTable.status == EmailStatus.FAILED.value)
                failed_result = await session.execute(failed_query)
                failed = failed_result.scalar()

                # Emails aujourd'hui
                today = datetime.now().date()
                today_query = select(func.count(SentEmailTable.id)).where(func.date(SentEmailTable.sent_at) == today)
                today_result = await session.execute(today_query)
                today_count = today_result.scalar()

                return {
                    "total_emails": total,
                    "successful_emails": success,
                    "failed_emails": failed,
                    "today_emails": today_count,
                    "success_rate": round((success / total * 100) if total > 0 else 0, 2),
                }

        except Exception as e:
            self.log_error("Erreur récupération stats", error=e)
            return {"total_emails": 0, "successful_emails": 0, "failed_emails": 0, "today_emails": 0, "success_rate": 0}

    async def close(self) -> None:
        """Ferme la connexion à la base de données"""
        if self.engine:
            await self.engine.dispose()
            self._initialized = False
            self.log_info("Connexion MySQL fermée")

    async def __aenter__(self):
        """Support du context manager async"""
        await self.initialize()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Support du context manager async"""
        await self.close()
        return None
