"""
Module de statistiques avancées pour le système de prospection
Vue complète : emails, relances, tracking, performance
"""

import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from logging_config import LoggerMixin
from sender.mysql_manager import MySQLManager

logger = logging.getLogger(__name__)


class AdvancedStatsManager(LoggerMixin):
    """Gestionnaire de statistiques avancées"""

    def __init__(self):
        """Initialise le gestionnaire de statistiques"""
        self.mysql_manager = None
        self._initialized = False

    async def initialize(self) -> None:
        """Initialise la connexion MySQL"""
        if self._initialized:
            return

        try:
            self.mysql_manager = MySQLManager()
            await self.mysql_manager.initialize()
            self._initialized = True
            self.log_info("Gestionnaire de statistiques avancées initialisé")
        except Exception as e:
            self.log_error("Erreur initialisation statistiques avancées", error=e)
            raise

    async def get_comprehensive_stats(self, days_back: int = 30) -> Dict[str, Any]:
        """
        Récupère des statistiques complètes du système

        Args:
            days_back: Nombre de jours en arrière

        Returns:
            Statistiques complètes
        """
        await self.initialize()

        try:
            # Récupérer toutes les statistiques en parallèle
            email_stats = await self._get_email_stats(days_back)
            tracking_stats = await self._get_tracking_stats(days_back)
            followup_stats = await self._get_followup_stats(days_back)
            performance_stats = await self._get_performance_stats(days_back)
            daily_stats = await self._get_daily_breakdown(days_back)

            return {
                "period_days": days_back,
                "generated_at": datetime.now().isoformat(),
                "email_stats": email_stats,
                "tracking_stats": tracking_stats,
                "followup_stats": followup_stats,
                "performance_stats": performance_stats,
                "daily_breakdown": daily_stats,
                "summary": self._generate_summary(email_stats, tracking_stats, followup_stats)
            }

        except Exception as e:
            self.log_error("Erreur récupération statistiques complètes", error=e)
            raise

    async def _get_email_stats(self, days_back: int) -> Dict[str, Any]:
        """Statistiques d'envoi d'emails"""
        cutoff_date = datetime.now() - timedelta(days=days_back)

        async with self.mysql_manager.session_factory() as session:
            from sender.mysql_manager import SentEmailTable
            from sqlalchemy import select, func, and_

            # Total emails envoyés
            total_query = select(func.count(SentEmailTable.id)).where(
                SentEmailTable.sent_at >= cutoff_date
            )
            total_result = await session.execute(total_query)
            total_sent = total_result.scalar() or 0

            # Emails par statut
            status_query = select(
                SentEmailTable.status,
                func.count(SentEmailTable.id).label('count')
            ).where(
                SentEmailTable.sent_at >= cutoff_date
            ).group_by(SentEmailTable.status)
            
            status_result = await session.execute(status_query)
            status_breakdown = {row.status: row.count for row in status_result}

            # Emails aujourd'hui
            today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            today_query = select(func.count(SentEmailTable.id)).where(
                SentEmailTable.sent_at >= today_start
            )
            today_result = await session.execute(today_query)
            today_sent = today_result.scalar() or 0

            return {
                "total_sent": total_sent,
                "today_sent": today_sent,
                "status_breakdown": status_breakdown,
                "success_rate": (status_breakdown.get('sent', 0) / total_sent * 100) if total_sent > 0 else 0
            }

    async def _get_tracking_stats(self, days_back: int) -> Dict[str, Any]:
        """Statistiques de tracking"""
        cutoff_date = datetime.now() - timedelta(days=days_back)

        async with self.mysql_manager.session_factory() as session:
            from sender.mysql_manager import SentEmailTable
            from sqlalchemy import select, func, and_

            # Emails avec tracking
            tracking_query = select(
                func.count(SentEmailTable.id).label('total'),
                func.sum(func.if_(SentEmailTable.opened == True, 1, 0)).label('opened'),
                func.sum(func.if_(SentEmailTable.clicked == True, 1, 0)).label('clicked'),
                func.sum(func.if_(SentEmailTable.replied == True, 1, 0)).label('replied')
            ).where(
                and_(
                    SentEmailTable.sent_at >= cutoff_date,
                    SentEmailTable.tracking_id.isnot(None)
                )
            )

            tracking_result = await session.execute(tracking_query)
            row = tracking_result.first()

            total_tracked = row.total or 0
            opened = row.opened or 0
            clicked = row.clicked or 0
            replied = row.replied or 0

            return {
                "total_tracked": total_tracked,
                "emails_opened": opened,
                "emails_clicked": clicked,
                "emails_replied": replied,
                "open_rate": (opened / total_tracked * 100) if total_tracked > 0 else 0,
                "click_rate": (clicked / total_tracked * 100) if total_tracked > 0 else 0,
                "reply_rate": (replied / total_tracked * 100) if total_tracked > 0 else 0
            }

    async def _get_followup_stats(self, days_back: int) -> Dict[str, Any]:
        """Statistiques de relances"""
        cutoff_date = datetime.now() - timedelta(days=days_back)

        async with self.mysql_manager.session_factory() as session:
            from sender.mysql_manager import SentEmailTable
            from sqlalchemy import select, func, and_, or_

            # Relances envoyées
            followups_sent_query = select(func.count(SentEmailTable.id)).where(
                SentEmailTable.followup_sent_at >= cutoff_date
            )
            followups_sent_result = await session.execute(followups_sent_query)
            followups_sent = followups_sent_result.scalar() or 0

            # Emails nécessitant une relance
            needs_followup_query = select(func.count(SentEmailTable.id)).where(
                and_(
                    SentEmailTable.status.in_(['sent', 'opened', 'clicked']),
                    SentEmailTable.sent_at <= datetime.now() - timedelta(days=7),
                    or_(SentEmailTable.replied.is_(None), SentEmailTable.replied == False),
                    SentEmailTable.followup_sent_at.is_(None)
                )
            )
            needs_followup_result = await session.execute(needs_followup_query)
            needs_followup = needs_followup_result.scalar() or 0

            # Réponses après relance
            replies_after_followup_query = select(func.count(SentEmailTable.id)).where(
                and_(
                    SentEmailTable.followup_sent_at >= cutoff_date,
                    SentEmailTable.replied == True,
                    SentEmailTable.replied_at > SentEmailTable.followup_sent_at
                )
            )
            replies_after_result = await session.execute(replies_after_followup_query)
            replies_after_followup = replies_after_result.scalar() or 0

            return {
                "followups_sent": followups_sent,
                "needs_followup": needs_followup,
                "replies_after_followup": replies_after_followup,
                "followup_success_rate": (replies_after_followup / followups_sent * 100) if followups_sent > 0 else 0
            }

    async def _get_performance_stats(self, days_back: int) -> Dict[str, Any]:
        """Statistiques de performance"""
        cutoff_date = datetime.now() - timedelta(days=days_back)

        async with self.mysql_manager.session_factory() as session:
            from sender.mysql_manager import SentEmailTable
            from sqlalchemy import select, func, and_

            # Simplifier les requêtes pour éviter les erreurs SQL
            # Compter les réponses
            replies_query = select(func.count(SentEmailTable.id)).where(
                and_(
                    SentEmailTable.sent_at >= cutoff_date,
                    SentEmailTable.replied == True
                )
            )
            replies_result = await session.execute(replies_query)
            total_replies = replies_result.scalar() or 0

            # Meilleur jour de la semaine (simplifié)
            best_day_query = select(
                func.date_format(SentEmailTable.sent_at, '%W').label('day_name'),
                func.count(SentEmailTable.id).label('count')
            ).where(
                SentEmailTable.sent_at >= cutoff_date
            ).group_by(
                func.date_format(SentEmailTable.sent_at, '%W')
            ).order_by(
                func.count(SentEmailTable.id).desc()
            ).limit(1)

            best_day_result = await session.execute(best_day_query)
            best_day_row = best_day_result.first()
            best_day = best_day_row.day_name if best_day_row else "N/A"

            return {
                "total_replies": total_replies,
                "best_sending_day": best_day,
                "total_companies_contacted": await self._get_unique_companies_count(cutoff_date)
            }

    async def _get_daily_breakdown(self, days_back: int) -> List[Dict[str, Any]]:
        """Répartition quotidienne des activités"""
        cutoff_date = datetime.now() - timedelta(days=days_back)

        async with self.mysql_manager.session_factory() as session:
            from sender.mysql_manager import SentEmailTable
            from sqlalchemy import select, func, and_

            daily_query = select(
                func.date(SentEmailTable.sent_at).label('date'),
                func.count(SentEmailTable.id).label('emails_sent'),
                func.sum(func.if_(SentEmailTable.opened == True, 1, 0)).label('emails_opened'),
                func.sum(func.if_(SentEmailTable.replied == True, 1, 0)).label('emails_replied')
            ).where(
                SentEmailTable.sent_at >= cutoff_date
            ).group_by(
                func.date(SentEmailTable.sent_at)
            ).order_by(
                func.date(SentEmailTable.sent_at).desc()
            )

            daily_result = await session.execute(daily_query)
            
            daily_stats = []
            for row in daily_result:
                daily_stats.append({
                    "date": row.date.strftime("%Y-%m-%d"),
                    "emails_sent": row.emails_sent,
                    "emails_opened": row.emails_opened or 0,
                    "emails_replied": row.emails_replied or 0,
                    "open_rate": (row.emails_opened / row.emails_sent * 100) if row.emails_sent > 0 else 0
                })

            return daily_stats

    async def _get_unique_companies_count(self, cutoff_date: datetime) -> int:
        """Compte le nombre d'entreprises uniques contactées"""
        async with self.mysql_manager.session_factory() as session:
            from sender.mysql_manager import SentEmailTable
            from sqlalchemy import select, func

            query = select(func.count(func.distinct(SentEmailTable.company_siren))).where(
                SentEmailTable.sent_at >= cutoff_date
            )
            result = await session.execute(query)
            return result.scalar() or 0

    def _generate_summary(self, email_stats: Dict, tracking_stats: Dict, followup_stats: Dict) -> Dict[str, str]:
        """Génère un résumé textuel des performances"""
        summary = {}

        # Performance globale
        if email_stats["success_rate"] >= 95:
            summary["email_performance"] = "Excellent"
        elif email_stats["success_rate"] >= 85:
            summary["email_performance"] = "Bon"
        else:
            summary["email_performance"] = "À améliorer"

        # Engagement
        open_rate = tracking_stats["open_rate"]
        if open_rate >= 25:
            summary["engagement"] = "Très bon"
        elif open_rate >= 15:
            summary["engagement"] = "Correct"
        else:
            summary["engagement"] = "Faible"

        # Relances
        if followup_stats["followup_success_rate"] >= 10:
            summary["followup_effectiveness"] = "Efficace"
        elif followup_stats["followup_success_rate"] >= 5:
            summary["followup_effectiveness"] = "Modéré"
        else:
            summary["followup_effectiveness"] = "À optimiser"

        return summary

    async def close(self) -> None:
        """Ferme les connexions"""
        if self.mysql_manager:
            await self.mysql_manager.close()
        self._initialized = False
