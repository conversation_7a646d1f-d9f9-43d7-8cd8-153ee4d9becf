"""
Gestionnaire de relances automatiques
"""

import asyncio
import logging
import os
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from logging_config import LoggerMixin
from mailer.email_generator import EmailGenerator
from models import CompanyContext, EmailStatus, EmailTemplate, SentEmail
from sender.email_sender import <PERSON>ail<PERSON>ender
from sender.mysql_manager import MySQLManager

logger = logging.getLogger(__name__)


class FollowUpManager(LoggerMixin):
    """Gestionnaire de relances automatiques"""

    def __init__(self):
        self.mysql_manager = None
        self.email_generator = None
        self.email_sender = None
        self._initialized = False

    async def initialize(self) -> None:
        """Initialise le gestionnaire de relances"""
        if self._initialized:
            return

        try:
            self.mysql_manager = MySQLManager()
            await self.mysql_manager.initialize()

            # EmailGenerator et EmailSender seront initialisés à la demande
            self.email_generator = None
            self.email_sender = None

            self._initialized = True
            self.log_info("Follow-up manager initialisé")
        except Exception as e:
            self.log_error("Erreur initialisation follow-up", error=e)
            raise

    async def get_emails_needing_followup(self, days_threshold: int = 7) -> List[SentEmail]:
        """
        Récupère les emails qui ont besoin d'une relance

        Args:
            days_threshold: Nombre de jours après envoi pour déclencher une relance

        Returns:
            Liste des emails nécessitant une relance
        """
        await self.initialize()

        try:
            # Date limite pour la relance
            cutoff_date = datetime.now() - timedelta(days=days_threshold)

            # Récupérer les emails envoyés sans réponse ni relance
            # Utiliser une connexion directe pour les requêtes complexes
            import aiomysql

            connection = await aiomysql.connect(
                host="127.0.0.1", port=3306, user="root", password="MLKqsd002", db="prospection_b2b", charset="utf8mb4"
            )

            try:
                async with connection.cursor(aiomysql.DictCursor) as cursor:
                    query = """
                        SELECT * FROM sent_emails
                        WHERE status IN ('sent', 'opened', 'clicked')
                        AND sent_at <= %s
                        AND (replied IS NULL OR replied = FALSE)
                        AND (followup_sent_at IS NULL)
                        AND (followup_scheduled IS NULL OR followup_scheduled = FALSE)
                        ORDER BY sent_at ASC
                    """

                    await cursor.execute(query, (cutoff_date,))
                    email_rows = await cursor.fetchall()
            finally:
                connection.close()

                emails_needing_followup = []
                for row in email_rows:
                    # Créer directement l'objet SentEmail depuis le dictionnaire
                    sent_email = SentEmail(
                        id=row["id"],
                        company_name=row["company_name"],
                        company_siren=row["company_siren"],
                        email_to=row["email_to"],
                        subject=row["subject"],
                        body=row["body"],
                        status=EmailStatus(row["status"]),
                        sent_at=row["sent_at"],
                        error_message=row["error_message"],
                        provider=row["provider"],
                        tracking_id=row.get("tracking_id"),
                        opened=row.get("opened"),
                        opened_at=row.get("opened_at"),
                        clicked=row.get("clicked"),
                        clicked_at=row.get("clicked_at"),
                        replied=row.get("replied"),
                        replied_at=row.get("replied_at"),
                        followup_scheduled=row.get("followup_scheduled"),
                        followup_scheduled_at=row.get("followup_scheduled_at"),
                        followup_sent_at=row.get("followup_sent_at"),
                    )
                    emails_needing_followup.append(sent_email)

                self.log_info(f"Trouvé {len(emails_needing_followup)} emails nécessitant une relance")
                return emails_needing_followup

        except Exception as e:
            self.log_error("Erreur récupération emails relance", error=e)
            return []

    async def schedule_followup(self, email_id: int, scheduled_at: datetime = None) -> bool:
        """
        Programme une relance pour un email

        Args:
            email_id: ID de l'email
            scheduled_at: Date de programmation (par défaut: maintenant + 7 jours)

        Returns:
            True si programmé avec succès
        """
        await self.initialize()

        if scheduled_at is None:
            scheduled_at = datetime.now() + timedelta(days=7)

        try:
            async with self.mysql_manager.session_factory() as session:
                await session.execute(
                    """
                    UPDATE sent_emails 
                    SET followup_scheduled = TRUE,
                        followup_scheduled_at = %s,
                        status = 'scheduled_followup'
                    WHERE id = %s
                """,
                    (scheduled_at, email_id),
                )

                await session.commit()
                self.log_info(f"Relance programmée pour email {email_id} le {scheduled_at}")
                return True

        except Exception as e:
            self.log_error("Erreur programmation relance", error=e)
            return False

    async def generate_followup_template(self, original_email: SentEmail) -> EmailTemplate:
        """
        Génère un template de relance personnalisé avec ChatGPT basé sur l'email original

        Args:
            original_email: Email original

        Returns:
            Template de relance personnalisé
        """
        try:
            # Initialiser le générateur d'emails si nécessaire
            if self.email_generator is None:
                openai_api_key = os.getenv("OPENAI_API_KEY")
                if not openai_api_key:
                    self.log_warning("Pas de clé OpenAI, utilisation du template statique")
                    return self._generate_static_followup_template(original_email)

                self.email_generator = EmailGenerator(openai_api_key)

            # Créer le contexte pour la relance avec historique
            followup_context = await self._build_followup_context(original_email)

            # Générer l'email de relance avec ChatGPT
            followup_template = await self._generate_ai_followup(original_email, followup_context)

            return followup_template

        except Exception as e:
            self.log_error("Erreur génération relance IA, utilisation template statique", error=e)
            return self._generate_static_followup_template(original_email)

    def _generate_static_followup_template(self, original_email: SentEmail) -> EmailTemplate:
        """
        Génère un template de relance statique (fallback)

        Args:
            original_email: Email original

        Returns:
            Template de relance statique
        """
        # Template de relance simple et professionnel
        followup_subject = f"Suivi - {original_email.subject}"

        # Déterminer la salutation
        company_name = original_email.company_name
        if self._is_person_name(company_name):
            # Extraire le nom de famille
            parts = company_name.split()
            if len(parts) >= 2:
                last_name = parts[-1]
                salutation = f"Madame, Monsieur {last_name},"
            else:
                salutation = "Madame, Monsieur,"
        else:
            salutation = "Madame, Monsieur,"

        followup_body = f"""{salutation}

Je me permets de revenir vers vous concernant mon message du {original_email.sent_at.strftime('%d/%m/%Y')} au sujet de votre présence en ligne.

N'ayant pas eu de retour de votre part, je souhaitais simplement m'assurer que mon message vous était bien parvenu et savoir si vous aviez eu l'occasion d'y réfléchir.

Je reste à votre disposition pour échanger sur vos projets web et vous accompagner dans leur réalisation.

Si vous n'êtes pas intéressé par nos services, n'hésitez pas à me le faire savoir, je respecterai votre choix.

Bien cordialement,

Rochdi Sami
Développeur Web – Entreprise Rochdi Sami
📞 06 23 31 58 39
📧 <EMAIL>
🌐 www.slconception.fr"""

        return EmailTemplate(
            subject=followup_subject,
            body=followup_body,
            personalization_tokens={
                "company_name": company_name,
                "original_date": original_email.sent_at.strftime("%d/%m/%Y"),
            },
        )

    def _is_person_name(self, company_name: str) -> bool:
        """Vérifie si le nom d'entreprise ressemble à un nom de personne"""
        # Liste de prénoms courants français
        common_first_names = [
            "MARIE",
            "JEAN",
            "MICHEL",
            "PIERRE",
            "PHILIPPE",
            "ALAIN",
            "BERNARD",
            "CHRISTOPHE",
            "DANIEL",
            "DAVID",
            "ERIC",
            "FRANCOIS",
            "FREDERIC",
            "GERARD",
            "HENRI",
            "JACQUES",
            "JOEL",
            "LAURENT",
            "MARC",
            "NICOLAS",
            "OLIVIER",
            "PASCAL",
            "PATRICK",
            "PAUL",
            "ROBERT",
            "STEPHANE",
            "THIERRY",
        ]

        parts = company_name.upper().split()
        if len(parts) >= 2:
            first_part = parts[0]
            return first_part in common_first_names

        return False

    async def send_followup(self, original_email: SentEmail) -> bool:
        """
        Envoie une relance pour un email

        Args:
            original_email: Email original

        Returns:
            True si envoyé avec succès
        """
        await self.initialize()

        try:
            # Initialiser l'email sender si nécessaire
            if self.email_sender is None:
                from sender.database_manager import DatabaseManager
                from sender.anti_spam import AntiSpamManager

                # Créer les dépendances
                db_manager = DatabaseManager()
                await db_manager.initialize()

                anti_spam_manager = AntiSpamManager(db_manager)

                # Créer l'EmailSender avec les bonnes dépendances
                self.email_sender = EmailSender(
                    db_manager=db_manager,
                    anti_spam_manager=anti_spam_manager,
                    smtp_host=os.getenv("SMTP_HOST"),
                    smtp_port=int(os.getenv("SMTP_PORT", 587)),
                    smtp_username=os.getenv("SMTP_USERNAME"),
                    smtp_password=os.getenv("SMTP_PASSWORD"),
                    smtp_use_tls=os.getenv("SMTP_USE_TLS", "true").lower() == "true",
                )
                await self.email_sender.initialize()

            # Générer le template de relance avec IA
            followup_template = await self.generate_followup_template(original_email)

            # Créer un nouvel email de relance
            from models import Company, LeadSource

            # Reconstituer l'objet Company
            company = Company(
                name=original_email.company_name,
                siren=original_email.company_siren,
                domain=getattr(original_email, 'company_domain', None),
                email=original_email.email_to,
                source=LeadSource.FOLLOWUP,
            )

            # Envoyer la relance
            result = await self.email_sender.send_email(company, followup_template, dry_run=False)

            if result.status == EmailStatus.SENT:
                # Marquer l'email original comme ayant reçu une relance
                await self._mark_followup_sent(original_email.id)
                self.log_info(f"Relance envoyée avec succès pour {original_email.company_name}")
                return True
            else:
                self.log_error(f"Échec envoi relance: {result.error_message}")
                return False

        except Exception as e:
            self.log_error("Erreur envoi relance", error=e)
            return False

    async def _mark_followup_sent(self, original_email_id: int) -> None:
        """Marque qu'une relance a été envoyée"""
        try:
            async with self.mysql_manager.session_factory() as session:
                await session.execute(
                    """
                    UPDATE sent_emails 
                    SET followup_sent_at = %s,
                        status = 'followup_sent'
                    WHERE id = %s
                """,
                    (datetime.now(), original_email_id),
                )

                await session.commit()

        except Exception as e:
            self.log_error("Erreur marquage relance", error=e)

    async def _build_followup_context(self, original_email: SentEmail) -> Dict[str, Any]:
        """
        Construit le contexte pour la génération de relance avec historique complet

        Args:
            original_email: Email original

        Returns:
            Contexte de relance avec historique
        """
        # Récupérer l'historique complet des emails pour cette entreprise
        email_history = await self._get_email_history(original_email.company_siren, original_email.email_to)

        return {
            "company_name": original_email.company_name,
            "company_siren": original_email.company_siren,
            "company_domain": getattr(original_email, 'company_domain', None),
            "email_to": original_email.email_to,
            "original_subject": original_email.subject,
            "original_body": original_email.body,
            "original_sent_date": original_email.sent_at.strftime("%d/%m/%Y"),
            "days_since_sent": (datetime.now() - original_email.sent_at).days,
            "email_opened": getattr(original_email, 'opened', False),
            "email_clicked": getattr(original_email, 'clicked', False),
            "email_history": email_history,
            "total_emails_sent": len(email_history),
        }

    async def _get_email_history(self, company_siren: str, email_to: str) -> List[Dict[str, Any]]:
        """
        Récupère l'historique complet des emails pour une entreprise

        Args:
            company_siren: SIREN de l'entreprise
            email_to: Email de destination

        Returns:
            Liste des emails précédents
        """
        try:
            import aiomysql

            connection = await aiomysql.connect(
                host="127.0.0.1", port=3306, user="root", password="MLKqsd002", db="prospection_b2b", charset="utf8mb4"
            )

            try:
                async with connection.cursor(aiomysql.DictCursor) as cursor:
                    query = """
                        SELECT subject, body, sent_at, status, opened, clicked, replied
                        FROM sent_emails
                        WHERE (company_siren = %s OR email_to = %s)
                        ORDER BY sent_at ASC
                    """

                    await cursor.execute(query, (company_siren, email_to))
                    email_rows = await cursor.fetchall()
            finally:
                connection.close()

            history = []
            for row in email_rows:
                history.append({
                    "subject": row["subject"],
                    "body": row["body"],
                    "sent_date": row["sent_at"].strftime("%d/%m/%Y"),
                    "status": row["status"],
                    "opened": bool(row.get("opened")),
                    "clicked": bool(row.get("clicked")),
                    "replied": bool(row.get("replied")),
                })

            return history

        except Exception as e:
            self.log_error("Erreur récupération historique emails", error=e)
            return []

    async def _generate_ai_followup(self, original_email: SentEmail, context: Dict[str, Any]) -> EmailTemplate:
        """
        Génère un email de relance avec ChatGPT

        Args:
            original_email: Email original
            context: Contexte de relance

        Returns:
            Template de relance généré par IA
        """
        try:
            from openai import AsyncOpenAI

            client = AsyncOpenAI(api_key=os.getenv("OPENAI_API_KEY"))

            system_prompt = """Tu es un expert en prospection B2B spécialisé dans les emails de relance efficaces.
Tu dois créer des emails de relance courts, directs et naturels.

STYLE REQUIS:
- Ton décontracté mais professionnel (comme entre collègues)
- Email TRÈS COURT (maximum 4-5 lignes)
- Pas de formules commerciales lourdes
- Référence naturelle au premier contact
- Apporte UN seul point de valeur concret
- Signature simple et directe

STRUCTURE OBLIGATOIRE:
1. Salutation simple
2. Référence rapide au premier email (1 ligne max)
3. UN point de valeur concret et spécifique (1-2 lignes)
4. Question directe ou proposition d'échange (1 ligne)
5. Signature

ÉVITER ABSOLUMENT:
- Les formules "J'espère que vous allez bien"
- Les phrases trop longues
- Les explications techniques détaillées
- Le ton trop commercial ou insistant"""

            # Construire l'historique des emails pour le prompt
            history_text = ""
            if context.get('email_history'):
                history_text = "\nHISTORIQUE COMPLET DES EMAILS ENVOYÉS:\n"
                for i, email in enumerate(context['email_history'], 1):
                    status_info = []
                    if email['opened']:
                        status_info.append("OUVERT")
                    if email['clicked']:
                        status_info.append("CLIQUÉ")
                    if email['replied']:
                        status_info.append("RÉPONDU")

                    status_str = f" ({', '.join(status_info)})" if status_info else " (non ouvert)"

                    history_text += f"""
EMAIL {i} - {email['sent_date']}{status_str}:
Objet: {email['subject']}
Contenu: {email['body'][:200]}...
"""

            user_prompt = f"""Génère un email de relance COURT et DIRECT pour cette entreprise:

ENTREPRISE: {context['company_name']}
NOMBRE D'EMAILS DÉJÀ ENVOYÉS: {context.get('total_emails_sent', 1)}
DERNIER EMAIL: {context['original_sent_date']} (il y a {context['days_since_sent']} jours)
EMAIL OUVERT: {'Oui' if context.get('email_opened') else 'Non'}
{history_text}

ANALYSE DE L'HISTORIQUE:
- Évite de répéter les sujets déjà abordés
- Adapte le ton selon le nombre d'emails déjà envoyés
- Si c'est le 2e email: ton décontracté et nouveau angle
- Si c'est le 3e+ email: ton plus direct et proposition concrète

INSTRUCTIONS SPÉCIFIQUES:
1. Email TRÈS COURT (3-4 lignes maximum)
2. Salutation simple: "Bonjour,"
3. Référence rapide SANS répéter ce qui a déjà été dit
4. Apporte UN point de valeur NOUVEAU et concret
5. Question directe ou proposition d'action
6. Ton naturel et pas commercial

NOUVEAUX ANGLES POSSIBLES:
- Exemples concrets de réalisations
- Témoignages clients rapides
- Offre d'audit gratuit
- Proposition de call de 15min
- Partage d'une ressource utile

SIGNATURE SIMPLE:
Rochdi Sami
📞 06 23 31 58 39
🌐 slconception.fr

Format:
Objet: [objet court et accrocheur]

[Email de 3-4 lignes maximum]"""

            response = await client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.7,
                max_tokens=1500
            )

            content = response.choices[0].message.content.strip()

            # Parser la réponse
            lines = content.split('\n')
            subject_line = lines[0] if lines[0].startswith('Objet:') else f"Suivi - {original_email.subject}"
            subject = subject_line.replace('Objet:', '').strip()

            # Le corps est tout sauf la première ligne
            body = '\n'.join(lines[1:]).strip()

            return EmailTemplate(
                subject=subject,
                body=body,
                personalization_tokens={
                    "company_name": context['company_name'],
                    "original_date": context['original_sent_date'],
                    "days_since": str(context['days_since_sent']),
                },
            )

        except Exception as e:
            self.log_error("Erreur génération IA relance", error=e)
            raise

    async def process_followups(self, dry_run: bool = True) -> Dict[str, Any]:
        """
        Traite toutes les relances en attente

        Args:
            dry_run: Si True, simule l'envoi sans envoyer réellement

        Returns:
            Statistiques du traitement
        """
        await self.initialize()

        self.log_info(f"Traitement des relances (dry_run={dry_run})")

        # Récupérer les emails nécessitant une relance
        emails_needing_followup = await self.get_emails_needing_followup()

        stats = {
            "emails_checked": len(emails_needing_followup),
            "followups_sent": 0,
            "followups_failed": 0,
            "dry_run": dry_run,
        }

        for email in emails_needing_followup:
            try:
                if dry_run:
                    self.log_info(f"[DRY RUN] Relance pour {email.company_name} ({email.email_to})")
                    stats["followups_sent"] += 1
                else:
                    success = await self.send_followup(email)
                    if success:
                        stats["followups_sent"] += 1
                    else:
                        stats["followups_failed"] += 1

                # Délai entre les envois
                if not dry_run:
                    await asyncio.sleep(2)

            except Exception as e:
                self.log_error(f"Erreur traitement relance pour {email.company_name}", error=e)
                stats["followups_failed"] += 1

        self.log_info(f"Traitement terminé: {stats}")
        return stats

    async def get_followup_statistics(self, days_back: int = 30) -> Dict[str, Any]:
        """
        Récupère les statistiques de relance

        Args:
            days_back: Nombre de jours en arrière

        Returns:
            Statistiques de relance
        """
        await self.initialize()

        try:
            cutoff_date = datetime.now() - timedelta(days=days_back)

            import aiomysql

            connection = await aiomysql.connect(
                host="127.0.0.1", port=3306, user="root", password="MLKqsd002", db="prospection_b2b", charset="utf8mb4"
            )

            try:
                async with connection.cursor() as cursor:
                    # Emails nécessitant une relance
                    await cursor.execute(
                        """
                        SELECT COUNT(*) as count FROM sent_emails
                        WHERE status IN ('sent', 'opened', 'clicked')
                        AND sent_at <= %s
                        AND (replied IS NULL OR replied = FALSE)
                        AND (followup_sent_at IS NULL)
                    """,
                        (datetime.now() - timedelta(days=7),),
                    )

                    result = await cursor.fetchone()
                    needing_followup = result[0]

                    # Relances déjà envoyées
                    await cursor.execute(
                        """
                        SELECT COUNT(*) as count FROM sent_emails
                        WHERE followup_sent_at >= %s
                    """,
                        (cutoff_date,),
                    )

                    result = await cursor.fetchone()
                    followups_sent = result[0]

                    # Emails avec réponse après relance
                    await cursor.execute(
                        """
                        SELECT COUNT(*) as count FROM sent_emails
                        WHERE followup_sent_at >= %s
                        AND replied = TRUE
                        AND replied_at > followup_sent_at
                    """,
                        (cutoff_date,),
                    )

                    result = await cursor.fetchone()
                    replies_after_followup = result[0]
            finally:
                connection.close()

                return {
                    "period_days": days_back,
                    "emails_needing_followup": needing_followup,
                    "followups_sent": followups_sent,
                    "replies_after_followup": replies_after_followup,
                    "followup_response_rate": round(
                        (replies_after_followup / followups_sent * 100) if followups_sent > 0 else 0, 2
                    ),
                }

        except Exception as e:
            self.log_error("Erreur statistiques relance", error=e)
            return {
                "period_days": days_back,
                "emails_needing_followup": 0,
                "followups_sent": 0,
                "replies_after_followup": 0,
                "followup_response_rate": 0,
            }
