"""
Gestionnaire de base de données MySQL uniquement
"""

import logging
import os
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from .mysql_manager import MySQLManager
from models import EmailStatus, SentEmail

logger = logging.getLogger(__name__)


class DatabaseManager:
    """Gestionnaire de base de données MySQL uniquement"""

    def __init__(self):
        """Initialise le gestionnaire MySQL"""
        self._mysql_manager = None
        self._initialized = False

    async def initialize(self) -> None:
        """Initialise la base de données MySQL"""
        if self._initialized:
            return

        try:
            self._mysql_manager = MySQLManager()
            await self._mysql_manager.initialize()
            self._initialized = True
            logger.info("Base de données MySQL initialisée")
        except Exception as e:
            logger.error(f"Erreur initialisation MySQL: {e}")
            raise

    async def is_company_already_contacted(
        self, siren: Optional[str] = None, domain: Optional[str] = None, days_threshold: int = 90
    ) -> bool:
        """
        Vérifie si une entreprise a déjà été contactée récemment

        Args:
            siren: SIREN de l'entreprise
            domain: Domaine de l'entreprise
            days_threshold: Nombre de jours pour considérer un contact comme récent

        Returns:
            True si déjà contactée récemment
        """
        await self.initialize()
        
        if not siren and not domain:
            return False

        return await self._mysql_manager.is_company_already_contacted(siren, domain, days_threshold)

    async def save_sent_email(self, sent_email: SentEmail) -> int:
        """
        Sauvegarde un email envoyé

        Args:
            sent_email: Email envoyé à sauvegarder

        Returns:
            ID de l'email sauvegardé
        """
        await self.initialize()
        return await self._mysql_manager.save_sent_email(sent_email)

    async def update_email_status(self, email_id: int, status: EmailStatus, error_message: Optional[str] = None) -> None:
        """
        Met à jour le statut d'un email

        Args:
            email_id: ID de l'email
            status: Nouveau statut
            error_message: Message d'erreur (optionnel)
        """
        await self.initialize()
        await self._mysql_manager.update_email_status(email_id, status, error_message)

    async def update_email_tracking_id(self, email_id: int, tracking_id: str) -> None:
        """
        Met à jour le tracking_id d'un email

        Args:
            email_id: ID de l'email
            tracking_id: ID de tracking
        """
        await self.initialize()
        await self._mysql_manager.update_email_tracking_id(email_id, tracking_id)

    async def get_sent_emails(
        self, limit: int = 100, status: Optional[EmailStatus] = None, days_back: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        Récupère la liste des emails envoyés

        Args:
            limit: Nombre maximum d'emails à retourner
            status: Filtrer par statut (optionnel)
            days_back: Nombre de jours en arrière (optionnel)

        Returns:
            Liste des emails avec leurs métadonnées
        """
        await self.initialize()
        return await self._mysql_manager.get_sent_emails(limit, status, days_back)

    async def get_statistics(self, days_back: int = 30) -> Dict[str, Any]:
        """
        Récupère les statistiques d'envoi

        Args:
            days_back: Nombre de jours en arrière pour les statistiques

        Returns:
            Dictionnaire avec les statistiques
        """
        await self.initialize()
        return await self._mysql_manager.get_statistics(days_back)

    async def update_email_metrics(self, email_id: int, opened: bool = None, clicked: bool = None, replied: bool = None) -> None:
        """
        Met à jour les métriques d'un email (ouverture, clic, réponse)

        Args:
            email_id: ID de l'email
            opened: Email ouvert (optionnel)
            clicked: Lien cliqué (optionnel)
            replied: Réponse reçue (optionnel)
        """
        await self.initialize()
        
        try:
            import aiomysql
            connection = await aiomysql.connect(
                host="127.0.0.1", port=3306, user="root", password="MLKqsd002",
                db="prospection_b2b", charset="utf8mb4"
            )

            try:
                # Construire la requête dynamiquement
                updates = []
                params = []

                if opened is not None:
                    updates.append("opened = %s")
                    params.append(opened)

                if clicked is not None:
                    updates.append("clicked = %s")
                    params.append(clicked)

                if replied is not None:
                    updates.append("replied = %s")
                    params.append(replied)

                if updates:
                    query = f"UPDATE sent_emails SET {', '.join(updates)} WHERE id = %s"
                    params.append(email_id)

                    async with connection.cursor() as cursor:
                        await cursor.execute(query, params)
                        await connection.commit()

                    logger.info(f"Métriques mises à jour pour email {email_id}")
            finally:
                connection.close()

        except Exception as e:
            logger.error(f"Erreur mise à jour métriques email {email_id}: {e}")
            raise

    async def track_email_open(self, tracking_id: str, user_agent: str = None, ip_address: str = None) -> bool:
        """
        Enregistre l'ouverture d'un email via le pixel de tracking

        Args:
            tracking_id: ID de tracking de l'email
            user_agent: User agent du navigateur
            ip_address: Adresse IP du client

        Returns:
            True si l'ouverture a été enregistrée
        """
        await self.initialize()

        try:
            import aiomysql
            connection = await aiomysql.connect(
                host="127.0.0.1", port=3306, user="root", password="MLKqsd002",
                db="prospection_b2b", charset="utf8mb4"
            )

            try:
                # Mettre à jour l'email comme ouvert
                async with connection.cursor() as cursor:
                    await cursor.execute("""
                        UPDATE sent_emails
                        SET opened = TRUE, opened_at = NOW()
                        WHERE tracking_id = %s AND opened IS NULL
                    """, (tracking_id,))

                    # Enregistrer l'événement de tracking
                    await cursor.execute("""
                        INSERT INTO email_tracking_events
                        (tracking_id, event_type, user_agent, ip_address, created_at)
                        VALUES (%s, 'open', %s, %s, NOW())
                    """, (tracking_id, user_agent, ip_address))

                    await connection.commit()

                logger.info(f"Ouverture d'email enregistrée: {tracking_id}")
                return True

            finally:
                connection.close()

        except Exception as e:
            logger.error(f"Erreur enregistrement ouverture email {tracking_id}: {e}")
            return False

    async def track_email_click(self, tracking_id: str, url: str, user_agent: str = None, ip_address: str = None) -> bool:
        """
        Enregistre le clic sur un lien dans un email

        Args:
            tracking_id: ID de tracking de l'email
            url: URL cliquée
            user_agent: User agent du navigateur
            ip_address: Adresse IP du client

        Returns:
            True si le clic a été enregistré
        """
        await self.initialize()

        try:
            import aiomysql
            connection = await aiomysql.connect(
                host="127.0.0.1", port=3306, user="root", password="MLKqsd002",
                db="prospection_b2b", charset="utf8mb4"
            )

            try:
                # Mettre à jour l'email comme cliqué
                async with connection.cursor() as cursor:
                    await cursor.execute("""
                        UPDATE sent_emails
                        SET clicked = TRUE, clicked_at = NOW()
                        WHERE tracking_id = %s AND clicked IS NULL
                    """, (tracking_id,))

                    # Enregistrer l'événement de tracking
                    await cursor.execute("""
                        INSERT INTO email_tracking_events
                        (tracking_id, event_type, url, user_agent, ip_address, created_at)
                        VALUES (%s, 'click', %s, %s, %s, NOW())
                    """, (tracking_id, url, user_agent, ip_address))

                    await connection.commit()

                logger.info(f"Clic d'email enregistré: {tracking_id} -> {url}")
                return True

            finally:
                connection.close()

        except Exception as e:
            logger.error(f"Erreur enregistrement clic email {tracking_id}: {e}")
            return False

    async def track_email_reply(self, email_address: str, subject: str, body: str) -> bool:
        """
        Enregistre une réponse à un email

        Args:
            email_address: Adresse email de l'expéditeur de la réponse
            subject: Sujet de la réponse
            body: Corps de la réponse

        Returns:
            True si la réponse a été enregistrée
        """
        await self.initialize()

        try:
            import aiomysql
            connection = await aiomysql.connect(
                host="127.0.0.1", port=3306, user="root", password="MLKqsd002",
                db="prospection_b2b", charset="utf8mb4"
            )

            try:
                # Trouver l'email original basé sur l'adresse email
                async with connection.cursor() as cursor:
                    await cursor.execute("""
                        SELECT id, tracking_id FROM sent_emails
                        WHERE email_to = %s
                        ORDER BY sent_at DESC
                        LIMIT 1
                    """, (email_address,))

                    result = await cursor.fetchone()
                    if result:
                        email_id, tracking_id = result

                        # Mettre à jour l'email comme ayant reçu une réponse
                        await cursor.execute("""
                            UPDATE sent_emails
                            SET replied = TRUE, replied_at = NOW()
                            WHERE id = %s
                        """, (email_id,))

                        # Enregistrer la réponse
                        await cursor.execute("""
                            INSERT INTO email_replies
                            (email_id, tracking_id, from_email, subject, body, received_at)
                            VALUES (%s, %s, %s, %s, %s, NOW())
                        """, (email_id, tracking_id, email_address, subject, body[:1000]))  # Limiter le body

                        await connection.commit()

                        logger.info(f"Réponse d'email enregistrée: {email_address}")
                        return True

            finally:
                connection.close()

        except Exception as e:
            logger.error(f"Erreur enregistrement réponse email {email_address}: {e}")
            return False

    async def cleanup_old_records(self, days_to_keep: int = 365) -> int:
        """
        Nettoie les anciens enregistrements

        Args:
            days_to_keep: Nombre de jours à conserver

        Returns:
            Nombre d'enregistrements supprimés
        """
        await self.initialize()
        return await self._mysql_manager.cleanup_old_records(days_to_keep)
