"""
Gestionnaire d'envoi d'emails SMTP et SendGrid
"""

import asyncio
import logging
from datetime import datetime
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import formataddr
from typing import Any, Dict, List, Optional

import aiosmtplib
import sendgrid
from sendgrid.helpers.mail import Content, Email, Mail, To

from models import Company, EmailStatus, EmailTemplate, SentEmail

from .anti_spam import AntiSpamManager
from .database_manager import DatabaseManager
from .email_tracking import EmailTrackingManager

logger = logging.getLogger(__name__)


class EmailSender:
    """Gestionnaire d'envoi d'emails avec support SMTP et SendGrid"""

    def __init__(
        self,
        db_manager: DatabaseManager,
        anti_spam_manager: AntiSpamManager,
        # Configuration SMTP
        smtp_host: Optional[str] = None,
        smtp_port: int = 587,
        smtp_username: Optional[str] = None,
        smtp_password: Optional[str] = None,
        smtp_use_tls: bool = True,
        # Configuration SendGrid
        sendgrid_api_key: Optional[str] = None,
        # Configuration générale
        from_email: str = "<EMAIL>",
        from_name: str = "Sami Rochdi",
        rate_limit: int = 5,  # emails par minute
        # Configuration de test
        test_mode: bool = True,  # Mode test par défaut
        test_email: str = "<EMAIL>",  # Email de test
    ):
        """
        Args:
            db_manager: Gestionnaire de base de données
            anti_spam_manager: Gestionnaire anti-spam
            smtp_host: Serveur SMTP
            smtp_port: Port SMTP
            smtp_username: Nom d'utilisateur SMTP
            smtp_password: Mot de passe SMTP
            smtp_use_tls: Utiliser TLS
            sendgrid_api_key: Clé API SendGrid
            from_email: Email expéditeur
            from_name: Nom expéditeur
            rate_limit: Limite d'envoi (emails par minute)
        """
        self.db_manager = db_manager
        self.anti_spam_manager = anti_spam_manager
        self.tracking_manager = EmailTrackingManager()

        # Configuration SMTP
        self.smtp_config = {
            "host": smtp_host,
            "port": smtp_port,
            "username": smtp_username,
            "password": smtp_password,
            "use_tls": smtp_use_tls,
        }

        # Configuration SendGrid
        self.sendgrid_client = None
        if sendgrid_api_key:
            self.sendgrid_client = sendgrid.SendGridAPIClient(api_key=sendgrid_api_key)

        # Configuration générale
        self.from_email = from_email
        self.from_name = from_name
        self.rate_limit = rate_limit

        # Configuration de test
        self.test_mode = test_mode
        self.test_email = test_email

        # Rate limiting
        self._last_send_time = 0
        self._send_semaphore = asyncio.Semaphore(1)  # Un envoi à la fois

    async def _rate_limit_delay(self) -> None:
        """Applique le rate limiting"""
        if self.rate_limit <= 0:
            return

        current_time = asyncio.get_event_loop().time()
        time_since_last = current_time - self._last_send_time
        min_interval = 60.0 / self.rate_limit  # Intervalle minimum en secondes

        if time_since_last < min_interval:
            delay = min_interval - time_since_last
            logger.debug(f"Rate limiting: attente de {delay:.2f}s")
            await asyncio.sleep(delay)

        self._last_send_time = asyncio.get_event_loop().time()

    def _create_email_message(self, to_email: str, subject: str, body: str, to_name: Optional[str] = None, tracking_id: Optional[str] = None) -> MIMEMultipart:
        """Crée un message email MIME"""
        msg = MIMEMultipart("alternative")

        # Headers
        msg["From"] = formataddr((self.from_name, self.from_email))
        msg["To"] = formataddr((to_name or to_email, to_email))
        msg["Subject"] = subject

        # Corps du message (texte et HTML)
        text_part = MIMEText(body, "plain", "utf-8")

        # Convertir le texte en HTML basique
        html_body = body.replace("\n", "<br>\n")

        # Ajouter le pixel de tracking si un tracking_id est fourni
        tracking_pixel = ""
        if tracking_id:
            # URL du pixel de tracking (sera remplacée par l'URL réelle du serveur)
            tracking_pixel = f'<img src="http://localhost:8000/api/v1/metrics/tracking/pixel/{tracking_id}" width="1" height="1" style="display:none;" alt="">'

        html_body = f"""
        <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            {html_body}
            {tracking_pixel}
        </body>
        </html>
        """
        html_part = MIMEText(html_body, "html", "utf-8")

        msg.attach(text_part)
        msg.attach(html_part)

        return msg

    async def _send_via_smtp(
        self, to_email: str, subject: str, body: str, to_name: Optional[str] = None, tracking_id: Optional[str] = None
    ) -> tuple[bool, Optional[str]]:
        """Envoie un email via SMTP"""
        if not all([self.smtp_config["host"], self.smtp_config["username"], self.smtp_config["password"]]):
            return False, "Configuration SMTP incomplète"

        try:
            msg = self._create_email_message(to_email, subject, body, to_name, tracking_id)

            # Envoi asynchrone avec STARTTLS pour Gmail
            await aiosmtplib.send(
                msg,
                hostname=self.smtp_config["host"],
                port=self.smtp_config["port"],
                username=self.smtp_config["username"],
                password=self.smtp_config["password"],
                start_tls=self.smtp_config["use_tls"],  # STARTTLS au lieu de TLS direct
            )

            logger.info(f"Email envoyé via SMTP à {to_email}")
            return True, None

        except Exception as e:
            error_msg = f"Erreur envoi SMTP: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def _send_via_sendgrid(
        self, to_email: str, subject: str, body: str, to_name: Optional[str] = None, tracking_id: Optional[str] = None
    ) -> tuple[bool, Optional[str]]:
        """Envoie un email via SendGrid"""
        if not self.sendgrid_client:
            return False, "SendGrid non configuré"

        try:
            from_email_obj = Email(self.from_email, self.from_name)
            to_email_obj = To(to_email, to_name)

            # Contenu texte et HTML avec pixel de tracking
            plain_content = Content("text/plain", body)

            html_body = body.replace("\n", "<br>\n")
            # Ajouter le pixel de tracking si un tracking_id est fourni
            if tracking_id:
                tracking_pixel = f'<img src="http://localhost:8000/api/v1/metrics/tracking/pixel/{tracking_id}" width="1" height="1" style="display:none;" alt="">'
                html_body += tracking_pixel

            html_content = Content("text/html", html_body)

            mail = Mail(from_email_obj, to_email_obj, subject, plain_content)
            mail.add_content(html_content)

            # Envoi
            response = await asyncio.get_event_loop().run_in_executor(None, self.sendgrid_client.send, mail)

            if response.status_code in [200, 202]:
                logger.info(f"Email envoyé via SendGrid à {to_email}")
                return True, None
            else:
                error_msg = f"Erreur SendGrid: {response.status_code} - {response.body}"
                logger.error(error_msg)
                return False, error_msg

        except Exception as e:
            error_msg = f"Erreur envoi SendGrid: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    async def send_email(
        self, company: Company, template: EmailTemplate, use_sendgrid: bool = False, dry_run: bool = False
    ) -> SentEmail:
        """
        Envoie un email à une entreprise

        Args:
            company: Entreprise destinataire
            template: Template d'email
            use_sendgrid: Utiliser SendGrid au lieu de SMTP
            dry_run: Mode test (ne pas envoyer réellement)

        Returns:
            Objet SentEmail avec le résultat
        """
        # Vérifier que l'entreprise a un email
        if not company.email:
            return SentEmail(
                company_name=company.name,
                company_siren=company.siren,
                company_domain=company.domain,
                email_to="",
                subject=template.subject,
                body=template.body,
                status=EmailStatus.FAILED,
                error_message="Pas d'adresse email pour cette entreprise",
            )

        # Vérification anti-spam
        can_contact, reason = await self.anti_spam_manager.can_contact_company(company)
        if not can_contact:
            return SentEmail(
                company_name=company.name,
                company_siren=company.siren,
                company_domain=company.domain,
                email_to=company.email,
                subject=template.subject,
                body=template.body,
                status=EmailStatus.DUPLICATE,
                error_message=f"Anti-spam: {reason}",
            )

        # En mode test, rediriger vers l'email de test
        actual_email = self.test_email if self.test_mode else company.email
        actual_name = f"[TEST] {company.name}" if self.test_mode else company.name

        # Modifier le sujet et le corps en mode test
        test_subject = template.subject
        test_body = template.body

        if self.test_mode:
            test_subject = f"[TEST] {template.subject}"
            test_body = f"""
🔧 **MODE TEST ACTIVÉ** 🔧

Email destiné à : {company.name} ({company.email})
SIREN : {company.siren}
Ville : {company.city if hasattr(company, 'city') else 'N/A'}

--- CONTENU ORIGINAL ---

{template.body}

--- FIN DU CONTENU ---

Cet email a été redirigé vers {self.test_email} pour des raisons de sécurité.
"""

        # Générer un tracking_id unique
        import uuid
        tracking_id = str(uuid.uuid4())

        # Créer l'objet SentEmail
        sent_email = SentEmail(
            company_name=company.name,
            company_siren=company.siren,
            company_domain=company.domain,
            email_to=actual_email,  # Email réel ou de test
            subject=test_subject,
            body=test_body,
            sent_at=datetime.now(),
            status=EmailStatus.PENDING,
            tracking_id=tracking_id,
        )

        if dry_run:
            logger.info(f"[DRY RUN] Email pour {company.name} ({company.email})")
            sent_email.status = EmailStatus.SENT
            return sent_email

        # Rate limiting et envoi
        async with self._send_semaphore:
            await self._rate_limit_delay()

            # Choisir la méthode d'envoi
            if use_sendgrid and self.sendgrid_client:
                success, error = await self._send_via_sendgrid(actual_email, test_subject, test_body, actual_name, tracking_id)
            else:
                success, error = await self._send_via_smtp(actual_email, test_subject, test_body, actual_name, tracking_id)

            # Mettre à jour le statut
            if success:
                sent_email.status = EmailStatus.SENT
                # Marquer comme contacté dans l'anti-spam
                await self.anti_spam_manager.mark_company_contacted(company)
            else:
                sent_email.status = EmailStatus.FAILED
                sent_email.error_message = error

            # Sauvegarder en base d'abord pour obtenir l'ID
            try:
                email_id = await self.db_manager.save_sent_email(sent_email)
                sent_email.id = email_id

                # Ajouter le tracking si l'email a été envoyé avec succès
                if success and email_id:
                    try:
                        tracking_data = await self.tracking_manager.create_tracking_data(email_id, sent_email.body)
                        sent_email.tracking_id = tracking_data.tracking_id

                        # Mettre à jour l'email avec le tracking_id
                        await self.db_manager.update_email_tracking_id(email_id, tracking_data.tracking_id)

                        logger.info(f"Tracking ajouté pour email {email_id}: {tracking_data.tracking_id}")
                    except Exception as e:
                        logger.error(f"Erreur ajout tracking: {e}")

            except Exception as e:
                logger.error(f"Erreur sauvegarde email: {e}")

            return sent_email

    async def send_batch_emails(
        self,
        companies_and_templates: List[tuple[Company, EmailTemplate]],
        use_sendgrid: bool = False,
        dry_run: bool = False,
        max_concurrent: int = 1,
    ) -> List[SentEmail]:
        """
        Envoie un lot d'emails

        Args:
            companies_and_templates: Liste de tuples (entreprise, template)
            use_sendgrid: Utiliser SendGrid
            dry_run: Mode test
            max_concurrent: Nombre maximum d'envois simultanés

        Returns:
            Liste des emails envoyés
        """
        semaphore = asyncio.Semaphore(max_concurrent)

        async def send_with_semaphore(company: Company, template: EmailTemplate) -> SentEmail:
            async with semaphore:
                return await self.send_email(company, template, use_sendgrid, dry_run)

        logger.info(f"Envoi de {len(companies_and_templates)} emails (dry_run={dry_run})")

        tasks = [send_with_semaphore(company, template) for company, template in companies_and_templates]

        sent_emails = await asyncio.gather(*tasks, return_exceptions=True)

        # Filtrer les erreurs
        valid_sent_emails = []
        for i, result in enumerate(sent_emails):
            if isinstance(result, Exception):
                company, template = companies_and_templates[i]
                logger.error(f"Erreur envoi email à {company.name}: {result}")
                # Créer un SentEmail d'erreur
                error_email = SentEmail(
                    company_name=company.name,
                    company_siren=company.siren,
                    company_domain=company.domain,
                    email_to=company.email or "",
                    subject=template.subject,
                    body=template.body,
                    status=EmailStatus.FAILED,
                    error_message=str(result),
                )
                valid_sent_emails.append(error_email)
            else:
                valid_sent_emails.append(result)

        # Statistiques
        successful = len([e for e in valid_sent_emails if e.status == EmailStatus.SENT])
        failed = len([e for e in valid_sent_emails if e.status == EmailStatus.FAILED])
        duplicates = len([e for e in valid_sent_emails if e.status == EmailStatus.DUPLICATE])

        logger.info(f"Envoi terminé: {successful} réussis, {failed} échoués, {duplicates} doublons")

        return valid_sent_emails

    async def get_sending_statistics(self, days_back: int = 30) -> Dict[str, Any]:
        """
        Récupère les statistiques d'envoi

        Args:
            days_back: Nombre de jours en arrière

        Returns:
            Statistiques d'envoi
        """
        return await self.db_manager.get_statistics(days_back)

    def validate_configuration(self) -> Dict[str, bool]:
        """
        Valide la configuration d'envoi

        Returns:
            Dictionnaire avec le statut de chaque configuration
        """
        smtp_valid = all([self.smtp_config["host"], self.smtp_config["username"], self.smtp_config["password"]])

        sendgrid_valid = self.sendgrid_client is not None

        return {
            "smtp_configured": smtp_valid,
            "sendgrid_configured": sendgrid_valid,
            "can_send": smtp_valid or sendgrid_valid,
            "from_email_set": bool(self.from_email),
            "rate_limit_set": self.rate_limit > 0,
        }
