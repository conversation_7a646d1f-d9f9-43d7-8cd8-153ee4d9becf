"""
Système de tracking des réponses aux emails de prospection
"""

import asyncio
import email
import imaplib
import logging
import os
import re
from dataclasses import dataclass
from datetime import datetime, timedelta
from email.header import decode_header
from typing import Any, Dict, List, Optional

from logging_config import LoggerMixin
from models import EmailStatus
from sender.mysql_manager import MySQLManager

logger = logging.getLogger(__name__)


@dataclass
class EmailReply:
    """Réponse détectée à un email de prospection"""

    message_id: str
    from_email: str
    from_name: str
    subject: str
    body: str
    received_at: datetime
    original_email_id: Optional[int] = None
    reply_type: str = "unknown"  # positive, negative, neutral, auto_reply


class EmailTracker(LoggerMixin):
    """Système de tracking des réponses aux emails"""

    def __init__(self):
        self.imap_server = None
        self.mysql_manager = None
        self._initialized = False

        # Configuration IMAP Gmail
        self.imap_host = "imap.gmail.com"
        self.imap_port = 993
        self.email_address = os.getenv("SMTP_USER", "")
        self.email_password = os.getenv("SMTP_PASSWORD", "")

    async def initialize(self) -> None:
        """Initialise le tracker"""
        if self._initialized:
            return

        try:
            # Initialiser MySQL
            self.mysql_manager = MySQLManager()
            await self.mysql_manager.initialize()

            self._initialized = True
            self.log_info("Email tracker initialisé")

        except Exception as e:
            self.log_error("Erreur initialisation tracker", error=e)
            raise

    def connect_imap(self) -> bool:
        """Connexion IMAP à Gmail"""
        try:
            self.imap_server = imaplib.IMAP4_SSL(self.imap_host, self.imap_port)
            self.imap_server.login(self.email_address, self.email_password)
            self.log_info("Connexion IMAP réussie")
            return True
        except Exception as e:
            self.log_error("Erreur connexion IMAP", error=e)
            return False

    def disconnect_imap(self) -> None:
        """Déconnexion IMAP"""
        if self.imap_server:
            try:
                self.imap_server.close()
                self.imap_server.logout()
                self.log_info("Déconnexion IMAP réussie")
            except Exception as e:
                self.log_error("Erreur déconnexion IMAP", error=e)

    def get_recent_emails(self, hours_back: int = 24) -> List[EmailReply]:
        """
        Récupère les emails reçus récemment

        Args:
            hours_back: Nombre d'heures à remonter

        Returns:
            Liste des emails reçus
        """
        if not self.connect_imap():
            return []

        try:
            # Sélectionner la boîte de réception
            self.imap_server.select("INBOX")

            # Calculer la date de recherche
            since_date = (datetime.now() - timedelta(hours=hours_back)).strftime("%d-%b-%Y")

            # Rechercher les emails récents
            status, messages = self.imap_server.search(None, f"SINCE {since_date}")

            if status != "OK":
                self.log_error("Erreur recherche emails")
                return []

            email_replies = []
            message_ids = messages[0].split()

            self.log_info(f"Traitement de {len(message_ids)} emails récents")

            for msg_id in message_ids[-50:]:  # Limiter aux 50 derniers
                try:
                    email_reply = self._process_email(msg_id)
                    if email_reply:
                        email_replies.append(email_reply)
                except Exception as e:
                    self.log_error(f"Erreur traitement email {msg_id}", error=e)

            return email_replies

        except Exception as e:
            self.log_error("Erreur récupération emails", error=e)
            return []
        finally:
            self.disconnect_imap()

    def _process_email(self, msg_id: bytes) -> Optional[EmailReply]:
        """
        Traite un email individuel

        Args:
            msg_id: ID du message

        Returns:
            EmailReply si c'est une réponse à nos emails
        """
        try:
            # Récupérer l'email
            status, msg_data = self.imap_server.fetch(msg_id, "(RFC822)")

            if status != "OK":
                return None

            # Parser l'email
            email_message = email.message_from_bytes(msg_data[0][1])

            # Extraire les informations
            from_header = email_message.get("From", "")
            subject_header = email_message.get("Subject", "")
            date_header = email_message.get("Date", "")
            message_id = email_message.get("Message-ID", "")
            in_reply_to = email_message.get("In-Reply-To", "")
            references = email_message.get("References", "")

            # Décoder le sujet
            subject = self._decode_header(subject_header)

            # Extraire l'email de l'expéditeur
            from_email = self._extract_email(from_header)
            from_name = self._extract_name(from_header)

            # Vérifier si c'est une réponse à nos emails
            if not self._is_reply_to_our_email(subject, in_reply_to, references, from_email):
                return None

            # Extraire le corps de l'email
            body = self._extract_body(email_message)

            # Parser la date
            received_at = self._parse_date(date_header)

            # Analyser le type de réponse
            reply_type = self._analyze_reply_type(subject, body)

            return EmailReply(
                message_id=message_id,
                from_email=from_email,
                from_name=from_name,
                subject=subject,
                body=body,
                received_at=received_at,
                reply_type=reply_type,
            )

        except Exception as e:
            self.log_error(f"Erreur traitement email {msg_id}", error=e)
            return None

    def _decode_header(self, header: str) -> str:
        """Décode un header d'email"""
        try:
            decoded_parts = decode_header(header)
            decoded_string = ""
            for part, encoding in decoded_parts:
                if isinstance(part, bytes):
                    decoded_string += part.decode(encoding or "utf-8")
                else:
                    decoded_string += part
            return decoded_string
        except Exception:
            return header

    def _extract_email(self, from_header: str) -> str:
        """Extrait l'adresse email du header From"""
        match = re.search(r"<(.+?)>", from_header)
        if match:
            return match.group(1)

        # Si pas de <>, peut-être que c'est juste l'email
        if "@" in from_header:
            return from_header.strip()

        return ""

    def _extract_name(self, from_header: str) -> str:
        """Extrait le nom du header From"""
        if "<" in from_header:
            name = from_header.split("<")[0].strip()
            return self._decode_header(name.strip('"'))
        return ""

    def _is_reply_to_our_email(self, subject: str, in_reply_to: str, references: str, from_email: str) -> bool:
        """
        Vérifie si l'email est une réponse à nos emails de prospection

        Args:
            subject: Sujet de l'email
            in_reply_to: Header In-Reply-To
            references: Header References
            from_email: Email de l'expéditeur

        Returns:
            True si c'est une réponse à nos emails
        """
        # Vérifier si le sujet contient "Re:" et des mots-clés de nos emails
        subject_lower = subject.lower()

        # Mots-clés de nos sujets d'emails
        our_keywords = ["présence en ligne", "site web", "modernisation", "développeur web", "rochdi sami"]

        # Vérifier si c'est un "Re:" avec nos mots-clés
        if subject_lower.startswith("re:") and any(keyword in subject_lower for keyword in our_keywords):
            return True

        # Vérifier les headers de réponse (plus fiable)
        if in_reply_to or references:
            return True

        # Vérifier si l'expéditeur est dans notre base de données
        # (à implémenter avec une requête MySQL)

        return False

    def _extract_body(self, email_message) -> str:
        """Extrait le corps de l'email"""
        try:
            if email_message.is_multipart():
                for part in email_message.walk():
                    if part.get_content_type() == "text/plain":
                        payload = part.get_payload(decode=True)
                        if payload:
                            return payload.decode("utf-8", errors="ignore")
            else:
                payload = email_message.get_payload(decode=True)
                if payload:
                    return payload.decode("utf-8", errors="ignore")
        except Exception as e:
            logger.error(f"Erreur extraction corps email: {e}")

        return ""

    def _parse_date(self, date_header: str) -> datetime:
        """Parse la date de l'email"""
        try:
            from email.utils import parsedate_to_datetime

            return parsedate_to_datetime(date_header)
        except Exception:
            return datetime.now()

    def _analyze_reply_type(self, subject: str, body: str) -> str:
        """
        Analyse le type de réponse

        Args:
            subject: Sujet de l'email
            body: Corps de l'email

        Returns:
            Type de réponse: positive, negative, neutral, auto_reply
        """
        text = (subject + " " + body).lower()

        # Réponses automatiques
        auto_reply_keywords = [
            "out of office",
            "absent",
            "congé",
            "vacances",
            "automatic reply",
            "réponse automatique",
            "ne pas répondre",
            "do not reply",
        ]

        if any(keyword in text for keyword in auto_reply_keywords):
            return "auto_reply"

        # Réponses positives
        positive_keywords = [
            "intéressé",
            "interested",
            "merci",
            "thank you",
            "rendez-vous",
            "meeting",
            "appelez",
            "call",
            "discuter",
            "discuss",
            "projet",
            "project",
        ]

        if any(keyword in text for keyword in positive_keywords):
            return "positive"

        # Réponses négatives
        negative_keywords = [
            "pas intéressé",
            "not interested",
            "non merci",
            "no thank",
            "déjà",
            "already",
            "stop",
            "unsubscribe",
            "ne pas contacter",
            "do not contact",
        ]

        if any(keyword in text for keyword in negative_keywords):
            return "negative"

        return "neutral"

    async def save_reply(self, reply: EmailReply) -> None:
        """
        Sauvegarde une réponse en base de données

        Args:
            reply: Réponse à sauvegarder
        """
        try:
            # TODO: Créer une table pour les réponses
            # Pour l'instant, on log juste
            self.log_info(f"Réponse détectée: {reply.from_email} - {reply.reply_type}")

        except Exception as e:
            self.log_error("Erreur sauvegarde réponse", error=e)

    async def check_replies(self, hours_back: int = 24) -> List[EmailReply]:
        """
        Vérifie les nouvelles réponses

        Args:
            hours_back: Nombre d'heures à vérifier

        Returns:
            Liste des nouvelles réponses
        """
        await self.initialize()

        self.log_info(f"Vérification des réponses des {hours_back} dernières heures")

        replies = self.get_recent_emails(hours_back)

        for reply in replies:
            await self.save_reply(reply)

        self.log_info(f"Trouvé {len(replies)} réponses")

        return replies
