"""
Gestionnaire de base de données pour l'historique des emails
Support MySQL et SQLite avec basculement automatique
"""

import asyncio
import logging
import os
import sqlite3
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional

import aiosqlite

from models import Company, EmailStatus, SentEmail

logger = logging.getLogger(__name__)

# Import conditionnel du MySQLManager
try:
    from .mysql_manager import MySQLManager

    MYSQL_AVAILABLE = True
except ImportError:
    MYSQL_AVAILABLE = False
    logger.warning("MySQL non disponible, utilisation de SQLite uniquement")


class DatabaseManager:
    """Gestionnaire de base de données unifié (MySQL par défaut, SQLite en fallback)"""

    def __init__(self, db_path: str = "prospection.db"):
        """
        Args:
            db_path: Chemin vers la base de données SQLite (utilisé en fallback)
        """
        self.db_path = Path(db_path)
        self._initialized = False
        self._mysql_manager = None
        self._use_mysql = os.getenv("DATABASE_TYPE", "sqlite").lower() == "mysql"

    async def initialize(self) -> None:
        """Initialise la base de données et crée les tables"""
        if self._initialized:
            return

        # Essayer MySQL d'abord si configuré
        if self._use_mysql and MYSQL_AVAILABLE:
            try:
                self._mysql_manager = MySQLManager()
                await self._mysql_manager.initialize()
                self._initialized = True
                logger.info("Base de données MySQL initialisée")
                return
            except Exception as e:
                logger.warning(f"Échec initialisation MySQL, basculement vers SQLite: {e}")
                self._use_mysql = False

        # Fallback vers SQLite
        try:
            async with aiosqlite.connect(self.db_path) as db:
                # Table des emails envoyés
                await db.execute(
                    """
                    CREATE TABLE IF NOT EXISTS sent_emails (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        company_siren TEXT,
                        company_domain TEXT,
                        company_name TEXT,
                        email_to TEXT NOT NULL,
                        subject TEXT NOT NULL,
                        body TEXT NOT NULL,
                        sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        status TEXT DEFAULT 'sent',
                        error_message TEXT,
                        tracking_id TEXT,
                        opened BOOLEAN DEFAULT NULL,
                        clicked BOOLEAN DEFAULT NULL,
                        replied BOOLEAN DEFAULT NULL,
                        campaign_id TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """
                )

                # Table des campagnes
                await db.execute(
                    """
                    CREATE TABLE IF NOT EXISTS campaigns (
                        id TEXT PRIMARY KEY,
                        name TEXT NOT NULL,
                        description TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        started_at TIMESTAMP,
                        completed_at TIMESTAMP,
                        total_leads INTEGER DEFAULT 0,
                        emails_sent INTEGER DEFAULT 0,
                        emails_opened INTEGER DEFAULT 0,
                        emails_replied INTEGER DEFAULT 0,
                        status TEXT DEFAULT 'draft'
                    )
                """
                )

                # Table des entreprises (cache)
                await db.execute(
                    """
                    CREATE TABLE IF NOT EXISTS companies_cache (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        siren TEXT UNIQUE,
                        name TEXT NOT NULL,
                        domain TEXT,
                        email TEXT,
                        city TEXT,
                        naf_code TEXT,
                        employees INTEGER,
                        last_contacted TIMESTAMP,
                        contact_count INTEGER DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """
                )

                # Index pour les performances
                await db.execute("CREATE INDEX IF NOT EXISTS idx_sent_emails_siren ON sent_emails(company_siren)")
                await db.execute("CREATE INDEX IF NOT EXISTS idx_sent_emails_domain ON sent_emails(company_domain)")
                await db.execute("CREATE INDEX IF NOT EXISTS idx_sent_emails_date ON sent_emails(sent_at)")
                await db.execute("CREATE INDEX IF NOT EXISTS idx_companies_siren ON companies_cache(siren)")
                await db.execute("CREATE INDEX IF NOT EXISTS idx_companies_domain ON companies_cache(domain)")

                # Migration : ajouter la colonne tracking_id si elle n'existe pas
                try:
                    await db.execute("ALTER TABLE sent_emails ADD COLUMN tracking_id TEXT")
                    await db.commit()
                    logger.info("Colonne tracking_id ajoutée à la table sent_emails")
                except Exception:
                    # La colonne existe déjà, c'est normal
                    pass

                await db.commit()

            self._initialized = True
            logger.info(f"Base de données initialisée: {self.db_path}")

        except Exception as e:
            logger.error(f"Erreur initialisation base de données: {e}")
            raise

    async def is_company_already_contacted(
        self, siren: Optional[str] = None, domain: Optional[str] = None, days_threshold: int = 90
    ) -> bool:
        """
        Vérifie si une entreprise a déjà été contactée récemment

        Args:
            siren: SIREN de l'entreprise
            domain: Domaine de l'entreprise
            days_threshold: Nombre de jours pour considérer un contact comme récent

        Returns:
            True si déjà contactée récemment
        """
        await self.initialize()

        if not siren and not domain:
            return False

        # Déléguer à MySQL si disponible
        if self._use_mysql and self._mysql_manager and siren:
            # Pour MySQL, on utilise une méthode simplifiée avec SIREN et email
            return False  # Pour l'instant, on laisse passer (à améliorer si nécessaire)

        # Fallback SQLite
        threshold_date = datetime.now() - timedelta(days=days_threshold)

        try:
            async with aiosqlite.connect(self.db_path) as db:
                query = """
                    SELECT COUNT(*) FROM sent_emails 
                    WHERE sent_at > ? AND (
                        (company_siren = ? AND company_siren IS NOT NULL) OR
                        (company_domain = ? AND company_domain IS NOT NULL)
                    )
                """

                async with db.execute(query, (threshold_date, siren, domain)) as cursor:
                    result = await cursor.fetchone()
                    return result[0] > 0

        except Exception as e:
            logger.error(f"Erreur vérification contact existant: {e}")
            return False

    async def save_sent_email(self, sent_email: SentEmail) -> int:
        """
        Sauvegarde un email envoyé

        Args:
            sent_email: Email envoyé à sauvegarder

        Returns:
            ID de l'email sauvegardé
        """
        await self.initialize()

        # Déléguer à MySQL si disponible
        if self._use_mysql and self._mysql_manager:
            return await self._mysql_manager.save_sent_email(sent_email)

        # Fallback SQLite
        try:
            async with aiosqlite.connect(self.db_path) as db:
                query = """
                    INSERT INTO sent_emails (
                        company_siren, company_domain, email_to, subject, body,
                        sent_at, status, error_message, campaign_id
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """

                values = (
                    sent_email.company_siren,
                    sent_email.company_domain,
                    sent_email.email_to,
                    sent_email.subject,
                    sent_email.body,
                    sent_email.sent_at,
                    sent_email.status.value,
                    sent_email.error_message,
                    None,  # campaign_id pour l'instant
                )

                cursor = await db.execute(query, values)
                await db.commit()

                email_id = cursor.lastrowid
                logger.info(f"Email sauvegardé avec ID: {email_id}")
                return email_id

        except Exception as e:
            logger.error(f"Erreur sauvegarde email: {e}")
            raise

    async def update_email_status(self, email_id: int, status: EmailStatus, error_message: Optional[str] = None) -> None:
        """
        Met à jour le statut d'un email

        Args:
            email_id: ID de l'email
            status: Nouveau statut
            error_message: Message d'erreur (optionnel)
        """
        await self.initialize()

        try:
            async with aiosqlite.connect(self.db_path) as db:
                query = "UPDATE sent_emails SET status = ?, error_message = ? WHERE id = ?"
                await db.execute(query, (status.value, error_message, email_id))
                await db.commit()

        except Exception as e:
            logger.error(f"Erreur mise à jour statut email {email_id}: {e}")
            raise

    async def update_email_tracking_id(self, email_id: int, tracking_id: str) -> None:
        """
        Met à jour le tracking_id d'un email

        Args:
            email_id: ID de l'email
            tracking_id: ID de tracking
        """
        await self.initialize()

        try:
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute(
                    "UPDATE sent_emails SET tracking_id = ? WHERE id = ?",
                    (tracking_id, email_id)
                )
                await db.commit()
                logger.info(f"Tracking ID mis à jour pour email {email_id}")

        except Exception as e:
            logger.error(f"Erreur mise à jour tracking ID email {email_id}: {e}")
            raise

    async def get_sent_emails(
        self, limit: int = 100, status: Optional[EmailStatus] = None, days_back: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        Récupère la liste des emails envoyés

        Args:
            limit: Nombre maximum d'emails à retourner
            status: Filtrer par statut (optionnel)
            days_back: Nombre de jours en arrière (optionnel)

        Returns:
            Liste des emails avec leurs métadonnées
        """
        await self.initialize()

        try:
            async with aiosqlite.connect(self.db_path) as db:
                query = "SELECT * FROM sent_emails"
                params = []
                conditions = []

                if status:
                    conditions.append("status = ?")
                    params.append(status.value)

                if days_back:
                    threshold_date = datetime.now() - timedelta(days=days_back)
                    conditions.append("sent_at > ?")
                    params.append(threshold_date)

                if conditions:
                    query += " WHERE " + " AND ".join(conditions)

                query += " ORDER BY sent_at DESC LIMIT ?"
                params.append(limit)

                async with db.execute(query, params) as cursor:
                    rows = await cursor.fetchall()

                    # Convertir en dictionnaires
                    columns = [description[0] for description in cursor.description]
                    emails = []
                    for row in rows:
                        email_dict = dict(zip(columns, row))
                        emails.append(email_dict)

                    return emails

        except Exception as e:
            logger.error(f"Erreur récupération emails: {e}")
            return []

    async def get_statistics(self, days_back: int = 30) -> Dict[str, Any]:
        """
        Récupère les statistiques d'envoi

        Args:
            days_back: Nombre de jours en arrière pour les statistiques

        Returns:
            Dictionnaire avec les statistiques
        """
        await self.initialize()

        try:
            threshold_date = datetime.now() - timedelta(days=days_back)

            async with aiosqlite.connect(self.db_path) as db:
                # Statistiques générales
                stats_query = """
                    SELECT
                        COUNT(*) as total_sent,
                        COUNT(CASE WHEN status = 'sent' THEN 1 END) as successful,
                        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed,
                        COUNT(CASE WHEN opened = 1 THEN 1 END) as opened,
                        COUNT(CASE WHEN clicked = 1 THEN 1 END) as clicked,
                        COUNT(CASE WHEN replied = 1 THEN 1 END) as replied,
                        COUNT(CASE WHEN DATE(sent_at) = DATE('now') THEN 1 END) as today_emails
                    FROM sent_emails
                    WHERE sent_at > ?
                """

                async with db.execute(stats_query, (threshold_date,)) as cursor:
                    row = await cursor.fetchone()

                    stats = {
                        "period_days": days_back,
                        "total_sent": row[0],
                        "successful": row[1],
                        "failed": row[2],
                        "opened": row[3],
                        "clicked": row[4],
                        "replied": row[5],
                        "today_emails": row[6],
                        "success_rate": (row[1] / row[0] * 100) if row[0] > 0 else 0,
                        "open_rate": (row[3] / row[1] * 100) if row[1] > 0 else 0,
                        "click_rate": (row[4] / row[1] * 100) if row[1] > 0 else 0,
                        "reply_rate": (row[5] / row[1] * 100) if row[1] > 0 else 0,
                    }

                # Statistiques par jour
                daily_query = """
                    SELECT 
                        DATE(sent_at) as date,
                        COUNT(*) as count
                    FROM sent_emails 
                    WHERE sent_at > ?
                    GROUP BY DATE(sent_at)
                    ORDER BY date DESC
                """

                daily_stats = []
                async with db.execute(daily_query, (threshold_date,)) as cursor:
                    async for row in cursor:
                        daily_stats.append({"date": row[0], "count": row[1]})

                stats["daily_breakdown"] = daily_stats
                return stats

        except Exception as e:
            logger.error(f"Erreur récupération statistiques: {e}")
            return {}

    async def cleanup_old_records(self, days_to_keep: int = 365) -> int:
        """
        Nettoie les anciens enregistrements

        Args:
            days_to_keep: Nombre de jours à conserver

        Returns:
            Nombre d'enregistrements supprimés
        """
        await self.initialize()

        try:
            threshold_date = datetime.now() - timedelta(days=days_to_keep)

            async with aiosqlite.connect(self.db_path) as db:
                # Compter les enregistrements à supprimer
                count_query = "SELECT COUNT(*) FROM sent_emails WHERE sent_at < ?"
                async with db.execute(count_query, (threshold_date,)) as cursor:
                    count_result = await cursor.fetchone()
                    records_to_delete = count_result[0]

                if records_to_delete > 0:
                    # Supprimer les anciens enregistrements
                    delete_query = "DELETE FROM sent_emails WHERE sent_at < ?"
                    await db.execute(delete_query, (threshold_date,))
                    await db.commit()

                    logger.info(f"Suppression de {records_to_delete} anciens enregistrements")

                return records_to_delete

        except Exception as e:
            logger.error(f"Erreur nettoyage base de données: {e}")
            return 0
