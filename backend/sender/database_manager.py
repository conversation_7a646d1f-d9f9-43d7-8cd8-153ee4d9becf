"""
Gestionnaire de base de données unifié - MySQL uniquement
Version nettoyée sans SQLite
"""

import logging
import os
from typing import Any, Dict, List, Optional

from models import EmailStatus, SentEmail

logger = logging.getLogger(__name__)

# Import du MySQLManager
try:
    from .mysql_manager import MySQLManager
    MYSQL_AVAILABLE = True
except ImportError:
    MYSQL_AVAILABLE = False
    logger.error("MySQL non disponible - requis pour le système")


class DatabaseManager:
    """Gestionnaire de base de données unifié - MySQL uniquement"""

    def __init__(self):
        """Initialise le gestionnaire avec MySQL uniquement"""
        self._initialized = False
        self._mysql_manager = None
        
        # Vérifier que MySQL est configuré
        if not MYSQL_AVAILABLE:
            raise Exception("MySQL requis mais non disponible")
        
        # Vérifier la configuration MySQL
        required_vars = ["MYSQL_HOST", "MYSQL_USER", "MYSQL_PASSWORD", "MYSQL_DATABASE"]
        missing_vars = [var for var in required_vars if not os.getenv(var)]
        if missing_vars:
            raise Exception(f"Variables MySQL manquantes: {missing_vars}")

    async def initialize(self) -> None:
        """Initialise la base de données MySQL"""
        if self._initialized:
            return

        try:
            self._mysql_manager = MySQLManager()
            await self._mysql_manager.initialize()
            self._initialized = True
            logger.info("Base de données MySQL initialisée")
        except Exception as e:
            logger.error(f"Erreur initialisation MySQL: {e}")
            raise

    async def is_company_already_contacted(
        self, siren: Optional[str] = None, domain: Optional[str] = None, days_threshold: int = 90
    ) -> bool:
        """
        Vérifie si une entreprise a déjà été contactée récemment

        Args:
            siren: SIREN de l'entreprise
            domain: Domaine de l'entreprise
            days_threshold: Nombre de jours pour considérer un contact comme récent

        Returns:
            True si déjà contactée récemment
        """
        await self.initialize()

        if not siren and not domain:
            return False

        # Déléguer à MySQL
        if siren:
            return await self._mysql_manager.check_email_sent(siren, "")
        
        return False

    async def save_sent_email(self, sent_email: SentEmail) -> int:
        """
        Sauvegarde un email envoyé

        Args:
            sent_email: Email à sauvegarder

        Returns:
            ID de l'email sauvegardé
        """
        await self.initialize()
        
        try:
            return await self._mysql_manager.save_sent_email(sent_email)
        except Exception as e:
            logger.error(f"Erreur sauvegarde email: {e}")
            raise

    async def update_tracking_id(self, email_id: int, tracking_id: str) -> bool:
        """
        Met à jour l'ID de tracking d'un email

        Args:
            email_id: ID de l'email
            tracking_id: ID de tracking

        Returns:
            True si mis à jour avec succès
        """
        await self.initialize()
        
        try:
            await self._mysql_manager.update_email_tracking_id(email_id, tracking_id)
            return True
        except Exception as e:
            logger.error(f"Erreur mise à jour tracking ID email {email_id}: {e}")
            return False

    async def get_email_statistics(self, days_back: int = 30) -> Dict[str, Any]:
        """
        Récupère les statistiques d'emails

        Args:
            days_back: Nombre de jours en arrière

        Returns:
            Statistiques d'emails
        """
        await self.initialize()
        
        try:
            return await self._mysql_manager.get_statistics(days_back)
        except Exception as e:
            logger.error(f"Erreur récupération statistiques: {e}")
            return {
                "period_days": days_back,
                "total_sent": 0,
                "successful": 0,
                "failed": 0,
                "opened": 0,
                "clicked": 0,
                "replied": 0,
                "today_emails": 0,
                "success_rate": 0,
                "open_rate": 0,
                "click_rate": 0,
                "reply_rate": 0,
                "daily_stats": []
            }

    async def get_tracking_statistics(self, days_back: int = 30) -> Dict[str, Any]:
        """
        Récupère les statistiques de tracking

        Args:
            days_back: Nombre de jours en arrière

        Returns:
            Statistiques de tracking
        """
        await self.initialize()
        
        try:
            return await self._mysql_manager.get_tracking_statistics(days_back)
        except Exception as e:
            logger.error(f"Erreur récupération statistiques tracking: {e}")
            return {
                "period_days": days_back,
                "total_emails": 0,
                "emails_opened": 0,
                "emails_clicked": 0,
                "unique_opens": 0,
                "unique_clicks": 0,
                "open_rate": 0,
                "click_rate": 0
            }

    async def update_email_tracking_id(self, email_id: int, tracking_id: str) -> bool:
        """
        Met à jour l'ID de tracking d'un email (alias pour update_tracking_id)

        Args:
            email_id: ID de l'email
            tracking_id: ID de tracking

        Returns:
            True si mis à jour avec succès
        """
        await self._mysql_manager.update_email_tracking_id(email_id, tracking_id)
        return True

    async def close(self) -> None:
        """Ferme les connexions à la base de données"""
        if self._mysql_manager:
            await self._mysql_manager.close()
        self._initialized = False
