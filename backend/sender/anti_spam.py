"""
Gestionnaire anti-spam pour éviter les envois en double
"""

import asyncio
import logging
from dataclasses import dataclass
from datetime import datetime, timedelta
from typing import Any, Dict, Optional, Set

from models import Company, EmailStatus

from .database_manager import DatabaseManager

logger = logging.getLogger(__name__)


@dataclass
class ContactRecord:
    """Enregistrement de contact"""

    identifier: str  # SIREN ou domaine
    last_contact: datetime
    contact_count: int
    status: str


class AntiSpamManager:
    """Gestionnaire anti-spam pour éviter les contacts en double"""

    def __init__(self, db_manager: DatabaseManager, min_days_between_contacts: int = 90, max_contacts_per_company: int = 3):
        """
        Args:
            db_manager: Gestionnaire de base de données
            min_days_between_contacts: Nombre minimum de jours entre deux contacts
            max_contacts_per_company: Nombre maximum de contacts par entreprise
        """
        self.db_manager = db_manager
        self.min_days_between_contacts = min_days_between_contacts
        self.max_contacts_per_company = max_contacts_per_company

        # Cache en mémoire pour les performances
        self._contact_cache: Dict[str, ContactRecord] = {}
        self._cache_last_refresh = datetime.now()
        self._cache_ttl = timedelta(hours=1)  # TTL du cache

    def _get_company_identifier(self, company: Company) -> Optional[str]:
        """
        Récupère l'identifiant unique d'une entreprise

        Args:
            company: Entreprise

        Returns:
            Identifiant unique (SIREN prioritaire, sinon domaine)
        """
        if company.siren:
            return f"siren:{company.siren}"
        elif company.domain:
            return f"domain:{company.domain}"
        else:
            return None

    async def _refresh_cache_if_needed(self) -> None:
        """Actualise le cache si nécessaire"""
        now = datetime.now()
        if now - self._cache_last_refresh > self._cache_ttl:
            await self._refresh_cache()

    async def _refresh_cache(self) -> None:
        """Actualise le cache depuis la base de données"""
        try:
            # Récupérer les contacts récents
            threshold_date = datetime.now() - timedelta(days=self.min_days_between_contacts * 2)

            recent_emails = await self.db_manager.get_sent_emails(
                limit=10000,  # Limite élevée pour récupérer tous les contacts récents
                days_back=self.min_days_between_contacts * 2,
            )

            # Reconstruire le cache
            self._contact_cache.clear()

            for email in recent_emails:
                # Identifier l'entreprise
                identifier = None
                if email.get("company_siren"):
                    identifier = f"siren:{email['company_siren']}"
                elif email.get("company_domain"):
                    identifier = f"domain:{email['company_domain']}"

                if identifier:
                    sent_at = (
                        datetime.fromisoformat(email["sent_at"]) if isinstance(email["sent_at"], str) else email["sent_at"]
                    )

                    if identifier in self._contact_cache:
                        # Mettre à jour avec le contact le plus récent
                        existing = self._contact_cache[identifier]
                        if sent_at > existing.last_contact:
                            existing.last_contact = sent_at
                        existing.contact_count += 1
                    else:
                        # Nouveau contact
                        self._contact_cache[identifier] = ContactRecord(
                            identifier=identifier, last_contact=sent_at, contact_count=1, status=email.get("status", "sent")
                        )

            self._cache_last_refresh = datetime.now()
            logger.debug(f"Cache anti-spam actualisé: {len(self._contact_cache)} entreprises")

        except Exception as e:
            logger.error(f"Erreur actualisation cache anti-spam: {e}")

    async def can_contact_company(self, company: Company) -> tuple[bool, str]:
        """
        Vérifie si une entreprise peut être contactée

        Args:
            company: Entreprise à vérifier

        Returns:
            Tuple (peut_contacter, raison)
        """
        identifier = self._get_company_identifier(company)
        if not identifier:
            return False, "Impossible d'identifier l'entreprise (pas de SIREN ni domaine)"

        await self._refresh_cache_if_needed()

        # Vérifier dans le cache
        if identifier in self._contact_cache:
            contact_record = self._contact_cache[identifier]

            # Vérifier le nombre maximum de contacts
            if contact_record.contact_count >= self.max_contacts_per_company:
                return False, f"Nombre maximum de contacts atteint ({self.max_contacts_per_company})"

            # Vérifier le délai minimum
            days_since_last_contact = (datetime.now() - contact_record.last_contact).days
            if days_since_last_contact < self.min_days_between_contacts:
                days_remaining = self.min_days_between_contacts - days_since_last_contact
                return False, f"Dernier contact trop récent (attendre {days_remaining} jours)"

        # Vérification supplémentaire en base de données pour être sûr
        is_already_contacted = await self.db_manager.is_company_already_contacted(
            siren=company.siren, domain=company.domain, days_threshold=self.min_days_between_contacts
        )

        if is_already_contacted:
            return False, "Entreprise déjà contactée récemment (vérification DB)"

        return True, "OK"

    async def mark_company_contacted(self, company: Company) -> None:
        """
        Marque une entreprise comme contactée

        Args:
            company: Entreprise contactée
        """
        identifier = self._get_company_identifier(company)
        if not identifier:
            return

        now = datetime.now()

        # Mettre à jour le cache
        if identifier in self._contact_cache:
            contact_record = self._contact_cache[identifier]
            contact_record.last_contact = now
            contact_record.contact_count += 1
        else:
            self._contact_cache[identifier] = ContactRecord(
                identifier=identifier, last_contact=now, contact_count=1, status="sent"
            )

    async def filter_contactable_companies(self, companies: list[Company]) -> tuple[list[Company], list[tuple[Company, str]]]:
        """
        Filtre une liste d'entreprises pour ne garder que celles contactables

        Args:
            companies: Liste des entreprises à filtrer

        Returns:
            Tuple (entreprises_contactables, entreprises_rejetées_avec_raison)
        """
        contactable = []
        rejected = []

        logger.info(f"Filtrage anti-spam de {len(companies)} entreprises")

        for company in companies:
            can_contact, reason = await self.can_contact_company(company)

            if can_contact:
                contactable.append(company)
            else:
                rejected.append((company, reason))
                logger.debug(f"Entreprise rejetée {company.name}: {reason}")

        logger.info(f"Filtrage terminé: {len(contactable)} contactables, {len(rejected)} rejetées")
        return contactable, rejected

    async def get_contact_history(self, company: Company) -> Dict[str, Any]:
        """
        Récupère l'historique de contact d'une entreprise

        Args:
            company: Entreprise

        Returns:
            Historique de contact
        """
        identifier = self._get_company_identifier(company)
        if not identifier:
            return {"error": "Impossible d'identifier l'entreprise"}

        await self._refresh_cache_if_needed()

        # Informations du cache
        cache_info = {}
        if identifier in self._contact_cache:
            contact_record = self._contact_cache[identifier]
            cache_info = {
                "last_contact": contact_record.last_contact.isoformat(),
                "contact_count": contact_record.contact_count,
                "days_since_last_contact": (datetime.now() - contact_record.last_contact).days,
            }

        # Récupérer l'historique détaillé depuis la DB
        try:
            recent_emails = await self.db_manager.get_sent_emails(limit=50)

            company_emails = []
            for email in recent_emails:
                if (company.siren and email.get("company_siren") == company.siren) or (
                    company.domain and email.get("company_domain") == company.domain
                ):
                    company_emails.append(
                        {
                            "sent_at": email["sent_at"],
                            "subject": email["subject"],
                            "status": email["status"],
                            "email_to": email["email_to"],
                        }
                    )

            return {
                "company_name": company.name,
                "identifier": identifier,
                "cache_info": cache_info,
                "email_history": company_emails,
                "can_contact": await self.can_contact_company(company),
            }

        except Exception as e:
            logger.error(f"Erreur récupération historique pour {company.name}: {e}")
            return {"error": str(e)}

    async def get_statistics(self) -> Dict[str, Any]:
        """
        Récupère les statistiques anti-spam

        Returns:
            Statistiques du système anti-spam
        """
        await self._refresh_cache_if_needed()

        # Analyser le cache
        total_companies = len(self._contact_cache)
        companies_by_contact_count = {}
        recent_contacts = 0

        threshold_recent = datetime.now() - timedelta(days=30)

        for contact_record in self._contact_cache.values():
            # Compter par nombre de contacts
            count = contact_record.contact_count
            if count not in companies_by_contact_count:
                companies_by_contact_count[count] = 0
            companies_by_contact_count[count] += 1

            # Compter les contacts récents
            if contact_record.last_contact > threshold_recent:
                recent_contacts += 1

        return {
            "total_companies_tracked": total_companies,
            "recent_contacts_30_days": recent_contacts,
            "companies_by_contact_count": companies_by_contact_count,
            "cache_last_refresh": self._cache_last_refresh.isoformat(),
            "min_days_between_contacts": self.min_days_between_contacts,
            "max_contacts_per_company": self.max_contacts_per_company,
        }
