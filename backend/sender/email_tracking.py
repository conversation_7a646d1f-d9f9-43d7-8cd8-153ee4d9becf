"""
Système de tracking des ouvertures et clics d'emails
"""

import base64
import logging
import uuid
from dataclasses import dataclass
from typing import Any, Dict, Optional

from logging_config import LoggerMixin
from sender.mysql_manager import MySQLManager

logger = logging.getLogger(__name__)


@dataclass
class TrackingData:
    """Données de tracking pour un email"""

    email_id: int
    tracking_id: str
    pixel_url: str
    tracked_links: Dict[str, str]  # original_url -> tracked_url


class EmailTrackingManager(LoggerMixin):
    """Gestionnaire de tracking des emails"""

    def __init__(self, base_url: str = "https://track.slconception.fr"):
        self.base_url = base_url.rstrip("/")
        self.mysql_manager = None
        self._initialized = False

    async def initialize(self) -> None:
        """Initialise le gestionnaire de tracking"""
        if self._initialized:
            return

        try:
            self.mysql_manager = MySQLManager()
            await self.mysql_manager.initialize()
            # Les tables de tracking sont créées automatiquement par SQLAlchemy
            self._initialized = True
            self.log_info("Email tracking manager initialisé")
        except Exception as e:
            self.log_error("Erreur initialisation tracking", error=e)
            # Ne pas lever l'erreur pour éviter de bloquer le système
            self.log_warning("Tracking désactivé en raison d'erreurs d'initialisation")



    def generate_tracking_id(self) -> str:
        """Génère un ID de tracking unique"""
        return str(uuid.uuid4())

    def generate_pixel_url(self, tracking_id: str) -> str:
        """Génère l'URL du pixel de tracking"""
        return f"{self.base_url}/track/open/{tracking_id}.png"

    def generate_tracked_link(self, tracking_id: str, original_url: str, link_id: str) -> str:
        """Génère un lien tracké"""
        encoded_url = base64.urlsafe_b64encode(original_url.encode()).decode()
        return f"{self.base_url}/track/click/{tracking_id}/{link_id}?url={encoded_url}"

    async def create_tracking_data(self, email_id: int, email_body: str) -> TrackingData:
        """
        Crée les données de tracking pour un email

        Args:
            email_id: ID de l'email en base
            email_body: Corps de l'email HTML

        Returns:
            Données de tracking
        """
        await self.initialize()

        tracking_id = self.generate_tracking_id()
        pixel_url = self.generate_pixel_url(tracking_id)

        # Extraire et tracker les liens
        tracked_links = await self._extract_and_track_links(email_id, tracking_id, email_body)

        return TrackingData(email_id=email_id, tracking_id=tracking_id, pixel_url=pixel_url, tracked_links=tracked_links)

    async def _extract_and_track_links(self, email_id: int, tracking_id: str, email_body: str) -> Dict[str, str]:
        """Extrait et crée des liens trackés"""
        import re

        # Pattern pour trouver les liens HTTP/HTTPS
        url_pattern = r'https?://[^\s<>"\']+|www\.[^\s<>"\']+|[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(?:/[^\s<>"\']*)?'

        tracked_links = {}
        urls_found = re.findall(url_pattern, email_body)

        try:
            async with self.mysql_manager.session_factory() as session:
                for i, url in enumerate(set(urls_found)):  # Éviter les doublons
                    # Nettoyer l'URL
                    clean_url = url.strip(".,!?;")
                    if not clean_url.startswith(("http://", "https://")):
                        clean_url = f"https://{clean_url}"

                    link_id = f"link_{i+1}"
                    tracked_url = self.generate_tracked_link(tracking_id, clean_url, link_id)

                    # Sauvegarder en base
                    await session.execute(
                        """
                        INSERT INTO tracked_links (email_id, tracking_id, original_url, link_id)
                        VALUES (%s, %s, %s, %s)
                    """,
                        (email_id, tracking_id, clean_url, link_id),
                    )

                    tracked_links[clean_url] = tracked_url

                await session.commit()

        except Exception as e:
            self.log_error("Erreur création liens trackés", error=e)

        return tracked_links

    def add_tracking_to_email(self, email_body: str, tracking_data: TrackingData) -> str:
        """
        Ajoute le tracking à un email HTML

        Args:
            email_body: Corps de l'email original
            tracking_data: Données de tracking

        Returns:
            Email avec tracking ajouté
        """
        # Convertir le texte en HTML si nécessaire
        if not email_body.strip().startswith("<"):
            # Convertir le texte en HTML
            html_body = email_body.replace("\n", "<br>\n")
            html_body = f"""
            <html>
            <head>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
            </head>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                {html_body}
            </body>
            </html>
            """
        else:
            html_body = email_body

        # Remplacer les liens par des liens trackés
        for original_url, tracked_url in tracking_data.tracked_links.items():
            html_body = html_body.replace(original_url, tracked_url)

        # Ajouter le pixel de tracking avant la fermeture du body
        pixel_html = f'<img src="{tracking_data.pixel_url}" width="1" height="1" style="display:none;" alt="">'

        if "</body>" in html_body:
            html_body = html_body.replace("</body>", f"{pixel_html}</body>")
        else:
            html_body += pixel_html

        return html_body

    async def record_open(self, tracking_id: str, ip_address: str = None, user_agent: str = None) -> bool:
        """
        Enregistre une ouverture d'email

        Args:
            tracking_id: ID de tracking
            ip_address: Adresse IP du visiteur
            user_agent: User agent du navigateur

        Returns:
            True si enregistré avec succès
        """
        await self.initialize()

        try:
            async with self.mysql_manager.session_factory() as session:
                # Vérifier si l'ouverture n'a pas déjà été enregistrée
                result = await session.execute(
                    """
                    SELECT COUNT(*) as count FROM email_tracking_events 
                    WHERE tracking_id = %s AND event_type = 'open'
                """,
                    (tracking_id,),
                )

                count = result.fetchone()[0]

                if count == 0:  # Première ouverture
                    # Récupérer l'email_id
                    result = await session.execute(
                        """
                        SELECT email_id FROM tracked_links 
                        WHERE tracking_id = %s LIMIT 1
                    """,
                        (tracking_id,),
                    )

                    row = result.fetchone()
                    if not row:
                        return False

                    email_id = row[0]

                    # Enregistrer l'ouverture
                    await session.execute(
                        """
                        INSERT INTO email_tracking_events 
                        (email_id, tracking_id, event_type, ip_address, user_agent)
                        VALUES (%s, %s, 'open', %s, %s)
                    """,
                        (email_id, tracking_id, ip_address, user_agent),
                    )

                    # Mettre à jour le statut de l'email
                    await session.execute(
                        """
                        UPDATE sent_emails 
                        SET status = 'opened' 
                        WHERE id = %s AND status = 'sent'
                    """,
                        (email_id,),
                    )

                    await session.commit()
                    self.log_info(f"Ouverture enregistrée pour tracking_id: {tracking_id}")
                    return True

        except Exception as e:
            self.log_error("Erreur enregistrement ouverture", error=e)

        return False

    async def record_click(
        self, tracking_id: str, link_id: str, ip_address: str = None, user_agent: str = None
    ) -> Optional[str]:
        """
        Enregistre un clic sur un lien

        Args:
            tracking_id: ID de tracking
            link_id: ID du lien cliqué
            ip_address: Adresse IP du visiteur
            user_agent: User agent du navigateur

        Returns:
            URL originale si trouvée
        """
        await self.initialize()

        try:
            async with self.mysql_manager.session_factory() as session:
                # Récupérer l'URL originale
                result = await session.execute(
                    """
                    SELECT email_id, original_url FROM tracked_links 
                    WHERE tracking_id = %s AND link_id = %s
                """,
                    (tracking_id, link_id),
                )

                row = result.fetchone()
                if not row:
                    return None

                email_id, original_url = row

                # Enregistrer le clic
                event_data = {"link_id": link_id, "original_url": original_url}
                await session.execute(
                    """
                    INSERT INTO email_tracking_events 
                    (email_id, tracking_id, event_type, event_data, ip_address, user_agent)
                    VALUES (%s, %s, 'click', %s, %s, %s)
                """,
                    (email_id, tracking_id, "click", str(event_data), ip_address, user_agent),
                )

                # Mettre à jour le statut de l'email
                await session.execute(
                    """
                    UPDATE sent_emails 
                    SET status = 'clicked' 
                    WHERE id = %s AND status IN ('sent', 'opened')
                """,
                    (email_id,),
                )

                await session.commit()
                self.log_info(f"Clic enregistré pour tracking_id: {tracking_id}, link: {link_id}")
                return original_url

        except Exception as e:
            self.log_error("Erreur enregistrement clic", error=e)

        return None

    async def get_tracking_stats(self, email_id: int) -> Dict[str, Any]:
        """
        Récupère les statistiques de tracking pour un email

        Args:
            email_id: ID de l'email

        Returns:
            Statistiques de tracking
        """
        await self.initialize()

        try:
            async with self.mysql_manager.session_factory() as session:
                # Compter les ouvertures
                result = await session.execute(
                    """
                    SELECT COUNT(*) as opens FROM email_tracking_events 
                    WHERE email_id = %s AND event_type = 'open'
                """,
                    (email_id,),
                )
                opens = result.fetchone()[0]

                # Compter les clics
                result = await session.execute(
                    """
                    SELECT COUNT(*) as clicks FROM email_tracking_events 
                    WHERE email_id = %s AND event_type = 'click'
                """,
                    (email_id,),
                )
                clicks = result.fetchone()[0]

                # Première ouverture
                result = await session.execute(
                    """
                    SELECT MIN(created_at) as first_open FROM email_tracking_events 
                    WHERE email_id = %s AND event_type = 'open'
                """,
                    (email_id,),
                )
                first_open = result.fetchone()[0]

                # Premier clic
                result = await session.execute(
                    """
                    SELECT MIN(created_at) as first_click FROM email_tracking_events 
                    WHERE email_id = %s AND event_type = 'click'
                """,
                    (email_id,),
                )
                first_click = result.fetchone()[0]

                return {
                    "email_id": email_id,
                    "opens": opens,
                    "clicks": clicks,
                    "first_open": first_open,
                    "first_click": first_click,
                    "opened": opens > 0,
                    "clicked": clicks > 0,
                }

        except Exception as e:
            self.log_error("Erreur récupération stats", error=e)
            return {
                "email_id": email_id,
                "opens": 0,
                "clicks": 0,
                "first_open": None,
                "first_click": None,
                "opened": False,
                "clicked": False,
            }
